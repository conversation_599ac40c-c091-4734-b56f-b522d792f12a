{"name": "ehs", "version": "1.0.0", "type": "module", "engines": {"node": ">=20"}, "prettier": {"semi": true, "singleQuote": true, "trailingComma": "all", "arrowParens": "always", "printWidth": 120, "tabWidth": 2, "useTabs": false}, "scripts": {"dev": "nodemon -w server --inspect=18594 -x tsx server/index.ts", "build": "vite build", "start": "tsx server/index.ts", "check": "tsc", "serveo": "ssh -R ehsupkapp.serveo.net:80:localhost:8594 serveo.net", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop", "test": "pnpm test:db:setup && vitest run --pool=threads --poolOptions.threads.singleThread", "test:watch": "pnpm test:db:setup && vitest --pool=threads --poolOptions.threads.singleThread", "test:coverage": "pnpm test:db:setup && vitest run --coverage --pool=threads --poolOptions.threads.singleThread", "test:server": "pnpm test:db:setup && find server -name \"*.test.ts\" | xargs npx vitest run --pool=threads --poolOptions.threads.singleThread", "test:server:coverage": "pnpm test:db:setup && find server -name \"*.test.ts\" | xargs npx vitest run --coverage --pool=forks", "test:db:setup": "drizzle-kit migrate --config drizzle-test.config.ts", "lint": "eslint \"**/*.{ts,tsx}\" --max-warnings=0", "lint:fix": "eslint \"**/*.{ts,tsx}\" --fix", "lint:server": "eslint \"server/**/*.ts\" --max-warnings=0", "lint:server:fix": "eslint \"server/**/*.ts\" --fix", "job:capas:overdue": "tsx server/jobs/overdue-notification.ts", "app:logs": "docker-compose logs -f ehs"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/s3-request-presigner": "^3.832.0", "@hookform/resolvers": "^5.1.1", "@mailchimp/mailchimp_transactional": "^1.0.59", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-core": "^0.13.8", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.80.10", "@tanstack/react-query-devtools": "^5.80.10", "@trpc-limiter/memory": "^1.0.0", "@trpc/client": "^11.4.2", "@trpc/react-query": "^11.4.2", "@trpc/server": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "@uidotdev/usehooks": "^2.4.1", "ai": "^4.3.16", "axios": "^1.10.0", "axios-retry": "^4.5.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.7.1", "dromo-uploader-react": "^2.1.10", "embla-carousel-react": "^8.6.0", "express": "^5.1.0", "fastest-levenshtein": "^1.0.16", "ioredis": "^5.6.1", "jspdf": "^3.0.1", "lucide-react": "^0.519.0", "mixpanel-browser": "^2.65.0", "motion": "^12.18.1", "pg": "^8.16.2", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "sonner": "^2.0.5", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vite-express": "^0.21.1", "winston": "^3.17.0", "wouter": "^3.7.1", "zod": "^3.24.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/canvas-confetti": "^1.9.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/mailchimp__mailchimp_transactional": "^1.0.11", "@types/mixpanel": "^2.14.9", "@types/mixpanel-browser": "^2.60.0", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react": "^4.5.2", "@vitest/coverage-v8": "^3.2.4", "drizzle-kit": "^0.31.1", "eslint": "^9.29.0", "eslint-plugin-vitest": "^0.5.4", "nodemon": "^3.1.10", "prettier": "^3.5.3", "supertest": "^7.1.1", "tailwindcss": "^4.1.10", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-runtime": "^1.4.0", "vitest": "^3.2.4"}, "pnpm": {"overrides": {"esbuild": "^0.25.5"}, "ignoredBuiltDependencies": ["esbuild"], "onlyBuiltDependencies": ["@tailwindcss/oxide"]}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}