import React from 'react';
import { CreateCommentFormSchema, ENTITY_TYPE_MAP, STATUS_MAP, UserPublic } from '@shared/schema.types';
import { z } from 'zod';
import { formatDate } from '@shared/date-utils';
import { getStatusBadgeStyle } from './helpers';

export type CommentMentionTemplateParams = z.infer<typeof CreateCommentFormSchema> & {
  entityUrl: string;
  timestamp: Date;
  reporter: Partial<UserPublic>;
  content: string;
  taggedUser?: string;
  users: UserPublic[];
};

const getTeamMemberMentions = (content: string, teamMembers: UserPublic[]) => {
  if (!content || typeof content !== 'string') return content;

  let processedContent = content;

  const mentions: Array<{ start: number; end: number; member: UserPublic }> = [];

  teamMembers.forEach((member) => {
    const escapedName = member.fullName?.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') || '';
    const mentionRegex = new RegExp(`@${escapedName}\\b`, 'gi');

    let match;
    while ((match = mentionRegex.exec(processedContent)) !== null) {
      mentions.push({
        start: match.index,
        end: match.index + match[0].length,
        member: member,
      });
    }
  });

  mentions.sort((a, b) => a.start - b.start);

  const uniqueMentions = mentions.filter((mention, index) => {
    if (index === 0) return true;
    const prevMention = mentions[index - 1];
    return mention.start >= prevMention.end;
  });

  if (uniqueMentions.length === 0) {
    return processedContent;
  }

  const parts: React.ReactNode[] = [];
  let lastIndex = 0;

  uniqueMentions.forEach((mention) => {
    if (mention.start > lastIndex) {
      parts.push(
        <span key={`text-${lastIndex}-${mention.start}`}>{processedContent.substring(lastIndex, mention.start)}</span>,
      );
    }

    parts.push(
      <span
        key={`mention-${mention.start}-${mention.member.id}`}
        style={{ color: '#3958BA', fontWeight: 'bold', background: '#DBEAFE', padding: '2px 4px', borderRadius: '4px' }}
      >
        @{mention.member.fullName}
      </span>,
    );

    lastIndex = mention.end;
  });

  if (lastIndex < processedContent.length) {
    parts.push(<span key={`text-${lastIndex}`}>{processedContent.substring(lastIndex)}</span>);
  }

  return parts;
};

export default function CommentMentionTemplate({
  entitySlug,
  entityTitle,
  entityType,
  timestamp,
  reporter,
  entityUrl,
  content,
  status,
  taggedUser,
  users,
}: CommentMentionTemplateParams) {
  const styles = {
    body: {
      fontFamily: 'Arial, sans-serif',
      background: '#fff',
      color: '#222',
      margin: 0,
      padding: 0,
    },
    container: {
      maxWidth: '700px',
      margin: '32px auto',
      background: '#fff',
      borderRadius: '4px',
      border: '1px solid #eee',
    },
    content: {
      padding: '0 32px 20px',
    },
    header: {
      fontSize: '18px',
      fontWeight: 'bold',
      marginBottom: '20px',
      padding: '10px',
      background: '#f3f4f6',
      borderBottom: '1px solid #e5e7eb',
      borderRadius: '4px 4px 0 0',
    },
    subject: {
      color: '#555',
      fontSize: '15px',
      marginBottom: '24px',
    },
    sectionTitle: {
      fontSize: '18px',
      fontWeight: 600,
      marginTop: '32px',
      marginBottom: '20px',
    },
    infoTable: {
      width: '100%',
      borderCollapse: 'collapse' as const,
      marginBottom: '24px',
    },
    fieldBlock: {
      marginBottom: '18px',
    },
    label: {
      color: '#222',
      display: 'block',
      fontWeight: 600,
      textAlign: 'left' as const,
    },
    value: {
      display: 'block',
      marginTop: '10px',
      fontWeight: 500,
    },
    badge: {
      display: 'inline-block',
      padding: '2px 10px',
      borderRadius: '8px',
      fontSize: '13px',
      fontWeight: 600,
      marginRight: '6px',
      marginTop: '10px',
    },
    badgeIncident: {
      background: '#2563eb',
      color: '#fff',
    },
    badgeHigh: {
      background: '#fee2e2',
      color: '#b91c1c',
    },
    descBlock: {
      marginTop: '36px',
    },
    descLabel: {
      color: '#222',
      display: 'block',
      marginBottom: '8px',
      fontWeight: 400,
    },
    descValue: {
      display: 'block',
      color: '#222',
      background: '#f9fafb',
      padding: '16px 12px',
      border: '1px solid #e5e7eb',
      borderRadius: '6px',
    },
    ctaBtn: {
      display: 'inline-block',
      background: '#3e63dd',
      color: '#fff',
      padding: '10px 28px',
      borderRadius: '6px',
      fontWeight: 600,
      textDecoration: 'none',
      margin: '16px 0 0',
    },
    footer: {
      color: '#888',
      fontSize: '13px',
      marginTop: '32px',
      textAlign: 'left' as const,
    },
    card: {
      border: '1px solid #eee',
      borderRadius: '10px',
      padding: '24px',
      marginBottom: '24px',
      background: '#fff',
    },
    viewSection: {
      marginBottom: '18px',
      border: '1px solid #eee',
      borderRadius: '10px',
      padding: '18px 24px',
      background: '#fff',
      textAlign: 'left' as const,
    },
    ctaCenter: {
      textAlign: 'center' as const,
    },
    muted: {
      color: '#888',
      fontSize: '15px',
      marginTop: '25px',
    },
    divider: {
      border: 'none',
      borderTop: '1px solid #e5e7eb',
      margin: '24px 0 28px 0',
    },
  };

  return (
    <html lang="en">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>You've been tagged in a comment</title>
        <style>{`
          @media only screen and (max-width: 600px) {
            .responsive-container {
              margin: 16px !important;
              border-radius: 0 !important;
            }
            
            .responsive-content {
              padding: 0 16px 20px !important;
            }
            
            .responsive-card {
              padding: 16px !important;
              margin-bottom: 16px !important;
            }
            
            .responsive-table {
              display: block !important;
              width: 100% !important;
            }
            
            .responsive-table tbody,
            .responsive-table tr,
            .responsive-table td {
              display: block !important;
              width: 100% !important;
            }
            
            .responsive-table td {
              display: block !important;
              width: 100% !important;
              padding-bottom: 24px !important;
              border: none !important;
              box-sizing: border-box !important;
            }
            
            .responsive-table td:last-child {
              padding-bottom: 0 !important;
            }
            
            .mobile-column {
              width: 100% !important;
              display: block !important;
              vertical-align: top !important;
            }
            
            .mobile-section-title {
              font-size: 16px !important;
              font-weight: 600 !important;
              margin: 0 0 20px 0 !important;
              padding-bottom: 8px !important;
              border-bottom: 1px solid #e5e7eb !important;
            }
            
            .mobile-divider {
              display: block !important;
              border: none !important;
              border-top: 1px solid #e5e7eb !important;
              margin: 24px 0 !important;
            }
          }
          
          /* Fallback for email clients */
          @media screen and (max-device-width: 600px) {
            .responsive-table td {
              display: block !important;
              width: 100% !important;
            }
            
            .mobile-column {
              width: 100% !important;
              display: block !important;
            }
          }
        `}</style>
      </head>
      <body style={styles.body}>
        <div style={styles.container} className="responsive-container">
          <div style={styles.header}>You've been tagged in a comment</div>
          <div style={styles.content} className="responsive-content">
            <hr style={styles.divider} />
            <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '4px' }}>{entityTitle}</div>
            <div style={{ marginBottom: '24px' }}>
              Someone has mentioned you in a comment on an incident or CAPA. Please review the comment and respond if
              needed.
            </div>
            <div style={styles.card} className="responsive-card">
              <table style={styles.infoTable} className="responsive-table" width="100%">
                <tbody>
                  <tr>
                    <td style={{ width: '50%', verticalAlign: 'top' }} className="mobile-column">
                      <div
                        style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}
                        className="mobile-section-title"
                      >
                        Item Information
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Item ID</span>
                        <span style={styles.value}>{entitySlug}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Title</span>
                        <span style={styles.value}>{entityTitle}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Type</span>
                        <div style={styles.value}>
                          <span style={{ ...styles.badge, ...styles.badgeIncident }}>
                            {ENTITY_TYPE_MAP[entityType]}
                          </span>
                        </div>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Status</span>
                        <div style={styles.value}>
                          <span style={getStatusBadgeStyle(status, styles)}>{STATUS_MAP[status]}</span>
                        </div>
                      </div>
                    </td>
                    <td style={{ width: '50%', verticalAlign: 'top' }} className="mobile-column">
                      <hr className="mobile-divider" style={{ display: 'none' }} />
                      <div
                        style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}
                        className="mobile-section-title"
                      >
                        Comment Details
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Posted By</span>
                        <span style={styles.value}>{reporter.fullName || reporter.firstName || 'Unknown'}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Date Posted</span>
                        <span style={styles.value}>{formatDate(timestamp)}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Tagged User</span>
                        <span style={styles.value}>{taggedUser}</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style={styles.descBlock}>
                <span style={styles.descLabel}>Comment</span>
                <span style={styles.descValue}>"{getTeamMemberMentions(content, users)}"</span>
              </div>
            </div>
            <div style={styles.viewSection}>
              <div style={styles.ctaCenter}>
                <a href={entityUrl} style={styles.ctaBtn}>
                  View Comment & Reply ↗
                </a>
              </div>
            </div>
            <div style={styles.footer}>
              This is an automated notification from your UpKeep EHS system. Please do not reply to this email.
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
