import React from 'react';
import { formatDate } from '@shared/date-utils';
import { RouterOutputs } from '@shared/router.types';
import { CAPA_PRIORITY_MAP, CAPA_TYPE_MAP, Location, STATUS_MAP, UserPublic } from '@shared/schema.types';

export type CapaAssignedTemplateParams = RouterOutputs['capa']['create'] & {
  owner: Partial<UserPublic>;
  location?: Location;
  linkedEvent?: {
    title: string;
    slug: string;
    url: string;
  };
  capaUrl: string;
};

export default function CapaAssignedTemplate({
  slug,
  title,
  type,
  priority,
  status,
  dueDate,
  location,
  rcaFindings,
  linkedEvent,
  capaUrl,
  owner,
}: CapaAssignedTemplateParams) {
  const styles = {
    body: {
      fontFamily: 'Arial, sans-serif',
      background: '#fff',
      color: '#222',
      margin: 0,
      padding: 0,
    },
    container: {
      maxWidth: '700px',
      margin: '32px auto',
      background: '#fff',
      borderRadius: '4px',
      border: '1px solid #eee',
    },
    content: {
      padding: '0 32px 20px',
    },
    header: {
      fontSize: '18px',
      fontWeight: 600,
      marginBottom: '20px',
      background: '#f3f4f6',
      padding: '10px',
      borderBottom: '1px solid #e5e7eb',
      borderRadius: '4px 4px 0 0',
    },
    sectionTitle: {
      fontSize: '18px',
      fontWeight: 600,
      marginTop: '32px',
      marginBottom: '20px',
    },
    infoTable: {
      width: '100%',
      borderCollapse: 'collapse' as const,
      marginBottom: '24px',
    },
    fieldBlock: {
      marginBottom: '18px',
    },
    label: {
      color: '#222',
      display: 'block',
      fontWeight: 600,
      textAlign: 'left' as const,
    },
    value: {
      display: 'block',
      fontWeight: 500,
    },
    badge: {
      display: 'inline-block',
      padding: '2px 10px',
      borderRadius: '8px',
      fontSize: '13px',
      fontWeight: 600,
      marginRight: '6px',
    },
    badgeCorrective: {
      background: '#3e63dd',
      color: '#fff',
    },
    badgeHigh: {
      background: '#fee2e2',
      color: '#b91c1c',
    },
    badgeStatusOpen: {
      background: '#fef9c3',
      color: '#b45309',
    },
    descBlock: {
      marginTop: '36px',
    },
    descLabel: {
      color: '#222',
      display: 'block',
      marginBottom: '8px',
      fontWeight: 600,
    },
    descValue: {
      display: 'block',
      color: '#222',
      fontWeight: 500,
    },
    linkBadge: {
      background: '#e0e7ff',
      color: '#3e63dd',
      borderRadius: '6px',
      padding: '2px 8px',
      fontSize: '13px',
      fontWeight: 600,
      textDecoration: 'none',
      marginRight: '6px',
    },
    linkIncident: {
      color: '#3e63dd',
      textDecoration: 'underline',
      fontSize: '14px',
    },
    ctaBtn: {
      display: 'inline-block',
      background: '#3e63dd',
      color: '#fff',
      padding: '10px 28px',
      borderRadius: '6px',
      fontWeight: 600,
      textDecoration: 'none',
      margin: '16px 0 0',
    },
    footer: {
      color: '#888',
      fontSize: '13px',
      marginTop: '32px',
      textAlign: 'left' as const,
    },
    card: {
      border: '1px solid #eee',
      borderRadius: '10px',
      padding: '24px',
      marginBottom: '24px',
      background: '#fff',
    },
    ctaCenter: {
      textAlign: 'center' as const,
    },
    muted: {
      color: '#888',
      fontSize: '15px',
      marginTop: '25px',
    },
    divider: {
      border: 'none',
      borderTop: '1px solid #e5e7eb',
      margin: '24px 0 28px 0',
    },
  };

  return (
    <html lang="en">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>CAPA Assigned Notification</title>
        <style>{`
          @media only screen and (max-width: 600px) {
            .responsive-container {
              margin: 16px !important;
              border-radius: 0 !important;
            }
            
            .responsive-content {
              padding: 0 16px 20px !important;
            }
            
            .responsive-card {
              padding: 16px !important;
              margin-bottom: 16px !important;
            }
            
            .responsive-table {
              display: block !important;
              width: 100% !important;
            }
            
            .responsive-table tbody,
            .responsive-table tr,
            .responsive-table td {
              display: block !important;
              width: 100% !important;
            }
            
            .responsive-table td {
              display: block !important;
              width: 100% !important;
              padding-bottom: 24px !important;
              border: none !important;
              box-sizing: border-box !important;
            }
            
            .responsive-table td:last-child {
              padding-bottom: 0 !important;
            }
            
            .mobile-column {
              width: 100% !important;
              display: block !important;
              vertical-align: top !important;
            }
            
            .mobile-section-title {
              font-size: 16px !important;
              font-weight: 600 !important;
              margin: 0 0 20px 0 !important;
              padding-bottom: 8px !important;
              border-bottom: 1px solid #e5e7eb !important;
            }
            
            .mobile-divider {
              display: block !important;
              border: none !important;
              border-top: 1px solid #e5e7eb !important;
              margin: 24px 0 !important;
            }
          }
          
          /* Fallback for email clients */
          @media screen and (max-device-width: 600px) {
            .responsive-table td {
              display: block !important;
              width: 100% !important;
            }
            
            .mobile-column {
              width: 100% !important;
              display: block !important;
            }
          }
        `}</style>
      </head>
      <body style={styles.body}>
        <div style={styles.container} className="responsive-container">
          <div style={styles.header}>CAPA Assigned Notification</div>
          <div style={styles.content} className="responsive-content">
            <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '4px' }}>{title}</div>
            <div style={{ marginBottom: '24px' }}>
              You've been assigned a new CAPA. Please review and take action on the assigned CAPA in UpKeep EHS.
            </div>
            <div style={styles.card} className="responsive-card">
              <table style={styles.infoTable} className="responsive-table" width="100%">
                <tbody>
                  <tr>
                    <td style={{ width: '50%', verticalAlign: 'top' }} className="mobile-column">
                      <div
                        style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}
                        className="mobile-section-title"
                      >
                        CAPA Information
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>CAPA ID</span>
                        <span style={styles.value}>{slug}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Title</span>
                        <span style={styles.value}>{title}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Type</span>
                        <div style={styles.value}>
                          <span style={{ ...styles.badge, ...styles.badgeCorrective }}>{CAPA_TYPE_MAP[type]}</span>
                        </div>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Priority</span>
                        <div style={styles.value}>
                          <span style={{ ...styles.badge, ...styles.badgeHigh }}>{CAPA_PRIORITY_MAP[priority]}</span>
                        </div>
                      </div>
                      {linkedEvent?.url && (
                        <div style={styles.fieldBlock}>
                          <span style={styles.label}>Linked Safety Event</span>
                          <div style={styles.value}>
                            <span style={styles.linkBadge}>{linkedEvent.slug}</span>
                            <a href={linkedEvent.url} style={styles.linkIncident}>
                              {linkedEvent.title}
                            </a>
                          </div>
                        </div>
                      )}
                    </td>
                    <td style={{ width: '50%', verticalAlign: 'top' }} className="mobile-column">
                      <hr className="mobile-divider" style={{ display: 'none' }} />
                      <div
                        style={{ ...styles.sectionTitle, fontSize: '16px', marginTop: 0 }}
                        className="mobile-section-title"
                      >
                        Assignment Details
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Status</span>
                        <div style={styles.value}>
                          <span style={{ ...styles.badge, ...styles.badgeStatusOpen }}>{STATUS_MAP[status]}</span>
                        </div>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Due Date</span>
                        <span style={styles.value}>{dueDate ? formatDate(dueDate) : 'Not specified'}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Assigned To</span>
                        <span style={styles.value}>{owner?.fullName}</span>
                      </div>
                      <div style={styles.fieldBlock}>
                        <span style={styles.label}>Location</span>
                        <span style={styles.value}>{location?.name || 'Not specified'}</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style={styles.descBlock}>
                <span style={styles.descLabel}>RCA Findings</span>
                <span style={styles.descValue}>{rcaFindings}</span>
              </div>
            </div>

            <div style={styles.ctaCenter}>
              <a href={capaUrl} style={styles.ctaBtn}>
                View CAPA ↗
              </a>
            </div>

            <div style={styles.footer}>
              This is an automated notification from your UpKeep EHS system. Please do not reply to this email.
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
