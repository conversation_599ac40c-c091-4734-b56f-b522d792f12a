import { ROUTES } from '@shared/ROUTE_PATHS';
import { EmailUser, sendEmail } from '@server/services/email.service';
import IncidentCreateTemplate, { IncidentCreateTemplateParams } from '@server/templates/incident-create';
import IncidentUpdateTemplate, { IncidentUpdateTemplateParams } from '@server/templates/incident-update';
import { env } from 'env';
import React from 'react';

export const sendPublicIncidentCreateNotification = async ({
  event,
  toUsers,
}: {
  event: IncidentCreateTemplateParams;
  toUsers: EmailUser[];
}) => {
  const template = React.createElement(IncidentCreateTemplate, {
    ...event,
    incidentUrl: `${env.EHS_URL}${ROUTES.BUILD_INCIDENT_DETAILS_PATH(event.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `New Safety Event Submitted: ${event.title}`,
  });
};

export const sendIncidentCreateNotification = async ({
  event,
  toUsers,
}: {
  event: IncidentCreateTemplateParams;
  toUsers: EmailUser[];
}) => {
  const template = React.createElement(IncidentCreateTemplate, {
    ...event,
    incidentUrl: `${env.EHS_URL}${ROUTES.BUILD_INCIDENT_DETAILS_PATH(event.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `New Safety Event Submitted: ${event.title}`,
  });
};

export const sendIncidentUpdateNotification = async ({
  event,
  toUsers,
}: {
  event: IncidentUpdateTemplateParams;
  toUsers: EmailUser[];
}) => {
  const template = React.createElement(IncidentUpdateTemplate, {
    ...event,
    incidentUrl: `${env.EHS_URL}${ROUTES.BUILD_INCIDENT_DETAILS_PATH(event.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `Safety Event Updated: ${event.slug} - ${event.title}`,
  });
};
