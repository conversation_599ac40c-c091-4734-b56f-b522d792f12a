import api, { buildDefaultHeaders, type Headers } from '@server/utils/api';
import { convertNumberToPriority, convertPriorityToNumber } from '@server/utils/workOrderPriority';
import type {
  CountWorkOrdersByCapaIdInput,
  CreateWorkOrderFromCapaInput,
  CreateWorkOrderParams,
  RawParseObject,
  UpkeepAsset,
  WorkOrder,
} from '@shared/schema.types';
import { TRPCError } from '@trpc/server';

type RawWorkOrderFromApi = {
  id: string;
  workOrderNumber: string;
  mainDescription: string;
  currentStatus: string;
  priorityNumber: number;
  dueDate: string;
  objectLocationForWorkOrder: RawParseObject;
  objectAsset: UpkeepAsset;
  userAssignedTo: RawParseObject;
};

export const createWorkOrder = async (params: CreateWorkOrderParams, headers: Headers): Promise<boolean> => {
  const response = await api.post('/api/v1/work-orders/create/wrapper', params, {
    headers: buildDefaultHeaders(headers),
  });

  return response.data.success;
};

export const searchWorkOrdersByCapaId = async (
  params: { capaId: string[]; limit: number; offset: number; sort?: string },
  headers: Headers,
): Promise<WorkOrder[]> => {
  const requestBody = {
    limit: params.limit,
    offset: params.offset,
    sort: params.sort || 'createdAt DESC',
    capaId: params.capaId,
  };

  const response = await api.post('/api/v1/work-orders/search', requestBody, {
    headers: buildDefaultHeaders(headers),
  });

  if (!response.data?.success || !response.data?.results) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to search work orders',
    });
  }

  const apiData = response.data.results;

  // Transform the response to match our simplified WorkOrder type
  return apiData.map((workOrder: RawWorkOrderFromApi) => ({
    id: workOrder.id,
    workOrderNumber: workOrder.workOrderNumber,
    title: workOrder.mainDescription,
    currentStatus: workOrder.currentStatus,
    priority: convertNumberToPriority(workOrder.priorityNumber),
    dueDate: workOrder.dueDate,
    assignedTo: workOrder?.userAssignedTo?.objectId,
    locationId: workOrder?.objectLocationForWorkOrder?.objectId,
    assetId: workOrder?.objectAsset?.id,
    assetName: workOrder?.objectAsset?.Name,
  }));
};

export const getWorkOrdersCountByCapa = async (
  params: CountWorkOrdersByCapaIdInput,
  headers: Headers,
): Promise<number> => {
  const requestBody = {
    capaId: params.capaId,
  };

  const response = await api.post('/api/v1/work-orders/search/count', requestBody, {
    headers: buildDefaultHeaders(headers),
  });

  if (!response.data?.success) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to count work orders',
    });
  }

  return response.data.result as number;
};

export const createWorkOrderFromCapa = async (
  capaData: CreateWorkOrderFromCapaInput,
  headers: Headers,
): Promise<boolean> => {
  // Validate required fields
  if (
    !capaData.id ||
    !capaData.title ||
    !capaData.slug ||
    !capaData.actionsToAddress ||
    !capaData.priority ||
    !capaData.dueDate
  ) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Missing required fields: id, title, slug, actionsToAddress, priority, and dueDate are mandatory',
    });
  }

  // Map CAPA fields to work order parameters
  const mainDescription = capaData.title;

  const note = `This Work Order was created from ${capaData.slug}.
${capaData.actionsToAddress}`;

  const priorityNumber = convertPriorityToNumber(capaData.priority);

  console.log('note', note);

  const workOrderParams: CreateWorkOrderParams = {
    mainDescription,
    note,
    priorityNumber,
    dueDate: capaData.dueDate,
    objectLocationForWorkOrder: capaData.locationId ?? undefined,
    objectAsset: capaData.assetId ?? undefined,
    userAssignedTo: capaData.userAssignedTo,
    capaId: capaData.id,
  };

  // Create the work order
  return await createWorkOrder(workOrderParams, headers);
};
