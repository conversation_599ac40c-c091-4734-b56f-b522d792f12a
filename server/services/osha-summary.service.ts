import { db } from '@server/db';
import {
  GetEstablishInformationByYear,
  OshaSummaryExecutiveCertification,
  UpsertOshaCompanyInformation,
} from '@shared/osha.types';
import { oshaAuditTrail, oshaCompanyInformation } from '@shared/schema';
import { User } from '@shared/schema.types';
import { and, eq, not, sql } from 'drizzle-orm';

export const upsertOshaSummary = async (input: UpsertOshaCompanyInformation, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      createdBy: user!.id,
      upkeepCompanyId: user!.upkeepCompanyId,
    };

    const inserted = await tx
      .insert(oshaCompanyInformation)
      .values({ ...values, createdAt: new Date() })
      .onConflictDoUpdate({
        target: [oshaCompanyInformation.upkeepCompanyId, oshaCompanyInformation.year],
        set: { ...values, updatedAt: new Date() },
      })
      .returning({
        id: oshaCompanyInformation.id,
        createdAt: oshaCompanyInformation.createdAt,
        updatedAt: oshaCompanyInformation.updatedAt,
      });

    const summary = inserted.at(0);
    if (!summary) {
      return;
    }

    const action = summary?.updatedAt ? 'updated' : 'created';

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: summary.id,
      entityType: 'osha_company_information',
      action,
      createdBy: user.id!,
      ipAddress: input.ipAddress || 'unknown',
      userAgent: input.userAgent || 'unknown',
      details: JSON.stringify(values),
    });

    return summary;
  });
};

export const upsertOshaExecutiveCertification = async (input: OshaSummaryExecutiveCertification, user: User) => {
  return db.transaction(async (tx) => {
    const { summaryId, ...summary } = input;

    const values = {
      ...summary,
      createdBy: user!.id,
      upkeepCompanyId: user!.upkeepCompanyId,
    };

    await tx.update(oshaCompanyInformation).set(values).where(eq(oshaCompanyInformation.id, summaryId));

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: summaryId,
      entityType: 'osha_company_information',
      action: 'updated',
      createdBy: user.id!,
      ipAddress: input.ipAddress || 'unknown',
      userAgent: input.userAgent || 'unknown',
      details: JSON.stringify(values),
    });

    return values;
  });
};

export const getEstablishmentInformationByYear = async ({ year }: GetEstablishInformationByYear, user: User) => {
  const summary = await db
    .select()
    .from(oshaCompanyInformation)
    .where(
      and(eq(oshaCompanyInformation.year, year), eq(oshaCompanyInformation.upkeepCompanyId, user.upkeepCompanyId)),
    );
  return summary.at(0) ?? null;
};

export const toggleArchiveOshaSummary = async (
  { id, ipAddress, userAgent }: { id: string; ipAddress: string; userAgent: string },
  user: User,
) => {
  return db.transaction(async (tx) => {
    const updated = await tx
      .update(oshaCompanyInformation)
      .set({
        archived: not(oshaCompanyInformation.archived),
        archivedAt: sql`CASE WHEN ${oshaCompanyInformation.archived} = true THEN NULL ELSE NOW() END`,
        archivedBy: sql`CASE WHEN ${oshaCompanyInformation.archived} = true THEN NULL ELSE ${user.id} END`,
      })
      .where(eq(oshaCompanyInformation.id, id))
      .returning({
        id: oshaCompanyInformation.id,
        archived: oshaCompanyInformation.archived,
        archivedAt: oshaCompanyInformation.archivedAt,
        archivedBy: oshaCompanyInformation.archivedBy,
      });

    const action = updated.at(0)?.archived ? 'archived' : 'restored';

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: id,
      entityType: 'osha_company_information',
      action,
      createdBy: user.id!,
      ipAddress,
      userAgent,
      details: JSON.stringify(updated.at(0)),
    });
  });
};
