import { db } from '@server/db';
import { OshaSummaryExecutiveCertificationInput, UpsertOshaCompanyInformationInput } from '@shared/osha.types';
import { oshaAuditTrail, oshaCompanyInformation } from '@shared/schema';
import { User } from '@shared/schema.types';
import { eq, and, isNull } from 'drizzle-orm';

export const upsertOshaSummary = async (input: UpsertOshaCompanyInformationInput, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      createdBy: user!.id,
      upkeepCompanyId: user!.upkeepCompanyId,
    };

    const inserted = await tx
      .insert(oshaCompanyInformation)
      .values({ ...values, createdAt: new Date() })
      .onConflictDoUpdate({
        target: [oshaCompanyInformation.upkeepCompanyId, oshaCompanyInformation.year],
        set: { ...values, updatedAt: new Date() },
      })
      .returning({
        id: oshaCompanyInformation.id,
        createdAt: oshaCompanyInformation.createdAt,
        updatedAt: oshaCompanyInformation.updatedAt,
      });

    const summary = inserted.at(0);
    if (!summary) {
      return;
    }

    const action = summary?.updatedAt ? 'updated' : 'created';

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: summary.id,
      entityType: 'osha_company_information',
      action,
      createdBy: user.id!,
      ipAddress: input.ipAddress || 'unknown',
      userAgent: input.userAgent || 'unknown',
      details: JSON.stringify(values),
    });

    return summary;
  });
};

export const upsertOshaExecutiveCertification = async (input: OshaSummaryExecutiveCertificationInput, user: User) => {
  return db.transaction(async (tx) => {
    const { summaryId, ...summary } = input;

    const values = {
      ...summary,
      createdBy: user!.id,
      upkeepCompanyId: user!.upkeepCompanyId,
    };

    await tx.update(oshaCompanyInformation).set(values).where(eq(oshaCompanyInformation.id, summaryId));

    await tx.insert(oshaAuditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: summaryId,
      entityType: 'osha_company_information',
      action: 'updated',
      createdBy: user.id!,
      ipAddress: input.ipAddress || 'unknown',
      userAgent: input.userAgent || 'unknown',
      details: JSON.stringify(values),
    });

    return values;
  });
};

export const getEstablishmentInformationByYear = async (year: number, user: User) => {
  const summary = await db
    .select()
    .from(oshaCompanyInformation)
    .where(
      and(
        eq(oshaCompanyInformation.year, year),
        eq(oshaCompanyInformation.upkeepCompanyId, user.upkeepCompanyId),
        isNull(oshaCompanyInformation.archivedAt),
      ),
    );
  return summary.at(0);
};
