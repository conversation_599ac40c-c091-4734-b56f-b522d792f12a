import { CreateGlobalLocation, DeleteGlobalLocation, ListGlobalLocationsSchema } from '@shared/location.types';
import { PaginatedResponse, User } from '@shared/schema.types';
import { auditTrail, globalLocations } from '@shared/schema';
import { db } from '@server/db';
import { and, eq, gte, ilike, inArray, isNull, lte } from 'drizzle-orm';
import { z } from 'zod';
import { endOfDay, startOfDay } from 'date-fns';
import { createPaginatedResponse } from '@server/utils/pagination';

export const createGlobalLocation = async (input: CreateGlobalLocation, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
      createdAt: new Date(),
    };

    console.log('values', values);

    const inserted = await tx.insert(globalLocations).values(values).returning({
      id: globalLocations.id,
      name: globalLocations.name,
    });

    const globalLocation = inserted.at(0);

    if (!globalLocation) {
      return;
    }

    await tx.insert(auditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: globalLocation.id,
      entityType: 'global_location',
      action: 'created',
      userId: user.id,
      details: JSON.stringify(values),
    });
    return globalLocation;
  });
};

export const listGlobalLocations = async (input: z.infer<typeof ListGlobalLocationsSchema>, user: User) => {
  const { cursor = 0, limit = 10, search, createdBy = [], includeArchived = false, createdDateRange } = input;

  const data = await db
    .select({
      id: globalLocations.id,
      name: globalLocations.name,
      upkeepCompanyId: globalLocations.upkeepCompanyId,
      createdBy: globalLocations.createdBy,
      createdAt: globalLocations.createdAt,
      archivedAt: globalLocations.archivedAt,
    })
    .from(globalLocations)
    .where(
      and(
        eq(globalLocations.upkeepCompanyId, user.upkeepCompanyId),
        includeArchived ? undefined : isNull(globalLocations.archivedAt),
        search ? ilike(globalLocations.name, `%${search}%`) : undefined,
        createdBy.length > 0 ? inArray(globalLocations.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(globalLocations.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(globalLocations.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .limit(limit)
    .offset(cursor)
    .orderBy(globalLocations.createdAt);

  return createPaginatedResponse(data, { cursor, limit });
};

export const deleteGlobalLocation = async (input: DeleteGlobalLocation, user: User) => {
  return db.transaction(async (tx) => {
    // First check if the location exists and belongs to the user's company
    const existingLocation = await tx
      .select()
      .from(globalLocations)
      .where(
        and(
          eq(globalLocations.id, input.id),
          eq(globalLocations.upkeepCompanyId, user.upkeepCompanyId),
          isNull(globalLocations.archivedAt),
        ),
      )
      .limit(1);

    if (existingLocation.length === 0) {
      throw new Error('Global location not found or already archived');
    }

    // Soft delete by setting archivedAt timestamp
    const updated = await tx
      .update(globalLocations)
      .set({
        archivedAt: new Date(),
      })
      .where(eq(globalLocations.id, input.id))
      .returning({
        id: globalLocations.id,
        name: globalLocations.name,
      });

    const deletedLocation = updated.at(0);

    if (!deletedLocation) {
      throw new Error('Failed to archive global location');
    }

    // Add audit trail entry
    await tx.insert(auditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: deletedLocation.id,
      entityType: 'global_location',
      action: 'archived',
      userId: user.id,
      details: JSON.stringify({ name: deletedLocation.name }),
    });

    return deletedLocation;
  });
};
