import { CreateGlobalLocation, ArchiveGlobalLocation, ListGlobalLocationsSchema } from '@shared/settings.types';
import { User } from '@shared/schema.types';
import { auditTrail, globalLocations } from '@shared/schema';
import { db } from '@server/db';
import { and, eq, gte, ilike, inArray, isNull, lte, sql } from 'drizzle-orm';
import { z } from 'zod';
import { endOfDay, startOfDay } from 'date-fns';
import { createPaginatedResponse } from '@server/utils/pagination';
import { TRPCError } from '@trpc/server';

export const createGlobalLocation = async (input: CreateGlobalLocation, user: User) => {
  return db.transaction(async (tx) => {
    const values = {
      ...input,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
      createdAt: new Date(),
    };

    const inserted = await tx.insert(globalLocations).values(values).returning({
      id: globalLocations.id,
      name: globalLocations.name,
    });

    const globalLocation = inserted.at(0);

    if (!globalLocation) {
      return;
    }

    await tx.insert(auditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: globalLocation.id,
      entityType: 'global_location',
      action: 'created',
      userId: user.id,
      details: JSON.stringify(values),
    });
    return globalLocation;
  });
};

export const listGlobalLocations = async (input: z.infer<typeof ListGlobalLocationsSchema>, user: User) => {
  const { cursor = 0, limit = 10, search, createdBy = [], includeArchived = false, createdDateRange } = input;

  const data = await db
    .select({
      id: globalLocations.id,
      name: globalLocations.name,
      upkeepCompanyId: globalLocations.upkeepCompanyId,
      createdBy: globalLocations.createdBy,
      createdAt: globalLocations.createdAt,
      archivedAt: globalLocations.archivedAt,
    })
    .from(globalLocations)
    .where(
      and(
        eq(globalLocations.upkeepCompanyId, user.upkeepCompanyId),
        includeArchived ? undefined : isNull(globalLocations.archivedAt),
        search ? ilike(globalLocations.name, `%${search}%`) : undefined,
        createdBy.length > 0 ? inArray(globalLocations.createdBy, createdBy) : undefined,
        createdDateRange?.from ? gte(globalLocations.createdAt, startOfDay(createdDateRange.from)) : undefined,
        createdDateRange?.to ? lte(globalLocations.createdAt, endOfDay(createdDateRange.to)) : undefined,
      ),
    )
    .limit(limit)
    .offset(cursor)
    .orderBy(globalLocations.createdAt);

  return createPaginatedResponse(data, { cursor, limit });
};

export const toggleArchiveGlobalLocation = async (input: ArchiveGlobalLocation, user: User) => {
  return db.transaction(async (tx) => {
    // Soft delete by setting archivedAt timestamp
    const now = new Date().toISOString();
    const updated = await tx
      .update(globalLocations)
      .set({
        archivedAt: sql`CASE WHEN ${globalLocations.archivedAt} IS NULL THEN CAST(${now} AS TIMESTAMP) ELSE NULL END`,
      })
      .where(eq(globalLocations.id, input.id))
      .returning({
        id: globalLocations.id,
        name: globalLocations.name,
        archivedAt: globalLocations.archivedAt,
      });

    const deletedLocation = updated.at(0);

    if (!deletedLocation) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to archive global location' });
    }

    const action = deletedLocation.archivedAt ? 'unarchived' : 'archived';
    // Add audit trail entry
    await tx.insert(auditTrail).values({
      upkeepCompanyId: user.upkeepCompanyId,
      entityId: deletedLocation.id,
      entityType: 'global_location',
      action,
      userId: user.id,
      details: JSON.stringify({ name: deletedLocation.name }),
    });

    return deletedLocation;
  });
};
