import { EmailUser, sendEmail } from '@server/services/email.service';
import CommentMentionTemplate, { CommentMentionTemplateParams } from '@server/templates/comment-mention';
import React from 'react';

export const sendCommentMentionNotification = async ({
  data,
  toUsers,
}: {
  data: CommentMentionTemplateParams;
  toUsers: EmailUser[];
}) => {
  let contentWithNames = data.content;
  if (toUsers && toUsers.length > 0) {
    for (const user of toUsers) {
      if (user && user.fullName) {
        contentWithNames = contentWithNames.replace(`@${user.id}`, `@${user.fullName}`);
      }
    }
  }

  const template = React.createElement(CommentMentionTemplate, {
    ...data,
    content: contentWithNames,
    taggedUser: toUsers.map((user) => user.fullName).join(', '),
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `You've been tagged in a comment: ${data?.entitySlug} – ${data?.entityTitle}`,
  });
};
