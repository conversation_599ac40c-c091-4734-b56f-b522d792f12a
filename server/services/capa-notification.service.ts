import { ROUTES } from '@shared/ROUTE_PATHS';
import { EmailUser, sendEmail } from '@server/services/email.service';
import CapaAssignedTemplate, { CapaAssignedTemplateParams } from '@server/templates/capa-assigned';
import CapaOverdueTemplate, { CapaOverdueTemplateParams } from '@server/templates/capa-overdue';
import CapaUpdateTemplate, { CapaUpdateTemplateParams } from '@server/templates/capa-update';
import { env } from 'env';
import React from 'react';

export const sendCapaOverdueNotification = async ({
  capa,
  toUsers,
}: {
  capa: CapaOverdueTemplateParams;
  toUsers: EmailUser[];
}) => {
  const template = React.createElement(CapaOverdueTemplate, {
    ...capa,
    capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `CAPA Overdue: ${capa.slug} - ${capa.title}`,
  });
};

export const sendCapaAssignedNotification = async ({
  capa,
  toUsers,
}: {
  capa: CapaAssignedTemplateParams;
  toUsers: EmailUser[];
}) => {
  const template = React.createElement(CapaAssignedTemplate, {
    ...capa,
    capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `CAPA Assigned: ${capa.slug} - ${capa.title}`,
  });
};

export const sendCapaUpdatedNotification = async ({
  capa,
  toUsers,
}: {
  capa: CapaUpdateTemplateParams;
  toUsers: EmailUser[];
}) => {
  const template = React.createElement(CapaUpdateTemplate, {
    ...capa,
    capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
  });

  await sendEmail(template, {
    to: toUsers,
    subject: `CAPA Updated: ${capa.slug} - ${capa.title}`,
  });
};
