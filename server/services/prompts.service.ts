import { MORE_THAN_ONE_LOCATION_MATCHED, NO_LOCATION_MATCHED } from '@server/utils/match-single-location';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, rcaMethodEnum } from '@shared/schema';
import {
  CreateCapasForm,
  EventCategorySchema,
  Location,
  ReportTypeSchema,
  RootCauseSchema,
  SeveritySchema,
  StatusSchema,
} from '@shared/schema.types';
import { addDays, format, subDays } from 'date-fns';

export const getEventPrompts = () => {
  // Calculate dates at request time, not module load time
  const currentDate = new Date();
  const formattedCurrentDate = format(currentDate, 'yyyy-MM-dd HH:mm:ss');

  // Example past dates for the prompt
  const lastWeekDate = format(subDays(currentDate, 7), 'yyyy-MM-dd');
  const lastMonthDate = format(subDays(currentDate, 30), 'yyyy-MM-dd');
  const yesterday = format(addDays(currentDate, 1), 'yyyy-MM-dd');

  const systemPrompt =
    'You are an AI assistant specialized in extracting detailed and accurate structured information about safety incidents from transcripts. ' +
    'Your task is to precisely identify key incident details while strictly adhering to the provided categorization systems. ' +
    'Extract only information that is explicitly mentioned in the transcript. Do not make assumptions or add information not directly stated. ' +
    'Ensure categorizations match the exact values specified in the schema. If uncertain about a field, omit it rather than guessing.' +
    `Today's date is ${formattedCurrentDate} for reference. When you see "last week", it refers to a date like ${lastWeekDate}, not a date in the future. ` +
    `When you see "last month", it refers to a date like ${lastMonthDate}, not a date in the future. ` +
    `When you see "yesterday", it refers to a date like ${yesterday}, not a date in the future. ` +
    "If the user doesn't provide date, you may assume the current date." +
    'The response should always be in english, despite the transcript being in another language.';

  const generateUserPrompt = (
    transcript: string,
    timezone?: string,
  ) => `Extract comprehensive details from this safety incident transcript. Return ONLY a JSON object with these precise keys:
  
            title (a clear, concise title summarizing the incident)
            description (a detailed description of what happened)
            location (specific location where the incident occurred)
            type (choose one of the following must be one of: ${ReportTypeSchema.options.join(', ')})
            category (must be one of: ${EventCategorySchema.options.join(', ')})
            severity (if mentioned: ${SeveritySchema.options.join(', ')})
            status (if mentioned: ${StatusSchema.options.join(', ')})            
            immediateActions (any immediate actions taken after the incident)            
            reportedAt (date and time the incident was reported) if timezone is provided, use the ${timezone} to parse the date, if not provided, use the UTC timezone


            IMPORTANT DATE HANDLING INSTRUCTIONS:
            - Today's date is ${formattedCurrentDate}
            - If the transcript mentions "last week", use a date around ${lastWeekDate}
            - If the transcript mentions "last month", use a date around ${lastMonthDate}
            - If the transcript mentions "yesterday", use ${yesterday}
            - If the user provides only hours and minutes, use the current date and the provided hours and minutes
            - If the user doesn't provide or mention a date, use the current date on UTC timezone
            
            Only include fields where information is explicitly provided in the transcript.
            Use exact category, severity, type, and rootCause values as specified.
            
            Transcript: "${transcript}"`;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

/**
 * CAPA analysis prompts configuration
 */
export const getCapaPrompts = () => {
  // Calculate dates at request time, not module load time
  const currentDate = new Date();
  const formattedCurrentDate = format(currentDate, 'yyyy-MM-dd');

  // Example future dates for the prompt
  const nextWeekDate = format(addDays(currentDate, 7), 'yyyy-MM-dd');
  const nextMonthDate = format(addDays(currentDate, 30), 'yyyy-MM-dd');
  const tomorrow = format(addDays(currentDate, 1), 'yyyy-MM-dd');

  // System prompt for CAPA analysis
  const systemPrompt =
    'You are an AI assistant specialized in extracting detailed and accurate structured information about Corrective and Preventive Actions (CAPAs) from transcripts. ' +
    'You are also a certified expert in occupational safety and workplace compliance, with deep knowledge of OSHA standards, labor regulations, and risk mitigation strategies. ' +
    'You understand how to assess both immediate and systemic safety issues, and provide corrective and preventive actions that align with industry best practices. ' +
    'When generating suggested actions, you will think like a safety officer conducting a root cause analysis. Describe countermeasures as clearly, specifically, and thoroughly as possible — including both short-term fixes and long-term systemic improvements. ' +
    'Your task is to precisely identify key CAPA details while strictly adhering to the provided categorization systems. ' +
    'Extract only information that is explicitly mentioned in the transcript. Do not make assumptions or add information not directly stated. ' +
    'Ensure categorizations match the exact values specified in the schema. If uncertain about a field, omit it rather than guessing. ' +
    'Use the exact allowed values from the schema for: "type", "rootCause", "status", "priority", "rcaMethod". ' +
    'Suggested mapping: ' +
    '- **High**: critical issues, injury risk, urgent language, only mark priority: high if there was actual harm, a safety shutdown, or critical system failure. ' +
    '- **Medium**: recurring problems or moderate safety impact, only mark priority: medium if there was a safety shutdown or critical system failure. ' +
    '- **Low**: audit findings, future tasks, documentation-only needs use low for near misses or other non-harmful incidents. ' +
    'For date fields, interpret relative time references (like "next week", "tomorrow", "in two days") correctly relative to the current date. ' +
    `Today's date is ${formattedCurrentDate} for reference. When you see "next week", it refers to a date like ${nextWeekDate}, not a date in the past. ` +
    'You need always to return a due date in the future. If the due date is not provided in the transcript, you need to calculate it based on the root cause and severity.' +
    'When returning the field rcaFindings, the format should depend on the selected rcaMethod: ' +
    '- For "5_whys": return a sequence of five "Why?" questions and answers that lead logically to the root cause. Those questions should be numbered and the answers should be in the same order as the questions. after a question and answer break the line.' +
    '- For "fishbone": summarize the findings in paragraph form, citing categories like People, Process, Equipment, Environment. Mention how each contributed. ' +
    '- For "fault_tree": describe a logic chain that led to the top event, referencing contributing events and conditions. ' +
    '- For "other" or "not_selected": provide a short paragraph summarizing the root cause in plain language. ' +
    'The aiSuggestedAction field should include a **numbered list** of clear corrective and preventive steps. ' +
    'If a method like fishbone is used, and a monitoring plan is mentioned (e.g., "track incident rates for 3 months"), include that as the final step. ' +
    'The response should always be in english, despite the transcript being in another language.';

  // Function to generate the user prompt with the transcript
  const generateUserPrompt = (
    transcript: string,
    timezone?: string,
  ) => `Extract comprehensive details from this CAPA transcript. Return ONLY a JSON object with these precise keys:

    title (a clear, concise title summarizing the CAPA)
    summary (a concise summary of the CAPA, 1-2 sentences)
    type (must be one of: ${capaTypeEnum.enumValues.join(', ')})
    rcaMethod (must be one of: ${rcaMethodEnum.enumValues.join(', ')})
    rcaFindings (based on rcaMethod: for "5_whys" provide five "Why?" questions and answers that lead to the root cause; for fault_tree, return rcaFindings as a paragraph describing the top event and the contributing failure chains. Start the paragraph with: "Fault Tree Analysis of '...' identified...".
    rootCause (if determined: ${RootCauseSchema.options.join(', ')})
    otherRootCause (if rootCause is not selected, provide a short paragraph summarizing the root cause in plain language)
    dueDate (when CAPA should be completed, in ISO format if possible) if timezone is provided, use the ${timezone} to parse the date, if not provided, use the UTC timezone
    tags (select tags that apply from ${capaTagsEnum.enumValues.join(', ')})
    priority (level of priority: ${capaPriorityEnum.enumValues.join(', ')})
    actionsToAddress (AI-generated action suggestions. Provide the response as a numbered list of clear, concise action steps)
    aiConfidenceScore (AI analysis confidence level) should be a number between 0 and 1 as percentage where 0.85 is 85%
    

    IMPORTANT DATE HANDLING INSTRUCTIONS:
    - Today's date is ${formattedCurrentDate}
    - If the transcript mentions "next week", use a date around ${nextWeekDate}
    - If the transcript mentions "next month", use a date around ${nextMonthDate}
    - If the transcript mentions "tomorrow", use ${tomorrow}
    - All due dates should be in the future, not the past
    - For relative time references, calculate the proper future date based on today
    - Format all dates as ISO strings (YYYY-MM-DDTHH:MM:SS.SSSZ)
    
    Only include fields where information is explicitly provided in the transcript.
    Use exact type, rootCause, severity, status, and priority values as specified.
    
    Transcript: "${transcript}"`;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

export const getCapaSummaryPrompt = (capa: CreateCapasForm) => {
  const systemPrompt =
    'You are an AI assistant specialized in creating a summary for a CAPA. The summary should be a concise summary of the CAPA, 1-2 sentences.';

  const generateUserPrompt = `
    You are an assistant creating brief CAPA summaries.
    
    Example:
    CAPA:
    {
      "title": "Hydraulic Leak on Pump 12",
      "rcaFindings": "Seal failure due to lack of inspection",
      "actionsToAddress": "Replace seal, update inspection schedule"
    }
    Return: { "summary": "Pump 12 experienced a seal failure due to missed inspections. The maintenance schedule has been updated and the seal replaced." }
    
    Now summarize this CAPA:
    ${JSON.stringify(capa, null, 2)}
    
    Return ONLY: { "summary": "..." }
    `;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

export const getLocationMatchPrompt = (location: string, locations: Location[]) => {
  const systemPrompt = `
You are a smart assistant that helps match user-submitted location name to a Candidate list of location names.

On locations, you must try to match the location value with one of the known Candidate names with a high confidence. 
When you find a good match, return the locationId and locationName.
**If theres more than one candidate, return on reason ${MORE_THAN_ONE_LOCATION_MATCHED}.** 
**If no candidate is a good match, return on reason ${NO_LOCATION_MATCHED}.**

You must not guess, and you must not assume the best match is the first one listed. Evaluate all candidates carefully before making a decision.

Respond strictly in this JSON format:
{
  "locationId": string,
  "locationName": string,
  "reason": string // ${NO_LOCATION_MATCHED} or ${MORE_THAN_ONE_LOCATION_MATCHED}
}
`;

  const userPrompt = `
Input (string):
${location}

Candidates (array of objects):
${JSON.stringify(locations, null, 2)}
`;

  return {
    systemPrompt,
    userPrompt,
  };
};
