import { getCache, setCache } from '@server/redis/cache';
import api, { buildDefaultHeaders, type Headers } from '@server/utils/api';
import { logger } from '@server/utils/logger';
import {
  FeatureFlag,
  featureFlags,
  PaginatedResponse,
  PublicSearchInput,
  User,
  UserPublic,
  UserSchema,
} from '@shared/schema.types';
import {
  AllowedActions,
  Modules,
  PERMISSION_LEVELS,
  permissions,
  USER_ACCOUNT_TYPES,
  UPKEEP_USER_ACCOUNT_TYPES,
} from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';

export const getUserCacheKey = (token: string) => `user:me:${token}`;

export const getCurrentUser = async ({ headers }: { headers: Headers }): Promise<User> => {
  const token = headers['x-user-token'] || headers['cookie'];

  if (!token) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'No token provided',
    });
  }

  const cacheKey = getUserCacheKey(token as string);

  const cachedUser = await getCache<User>(cacheKey);

  if (cachedUser) {
    return cachedUser;
  }

  const response = await api.get('/api/v1/users/me', {
    headers: buildDefaultHeaders(headers),
  });

  const user = response.data.result;

  user.featureFlags = featureFlags.reduce(
    (acc, flag) => {
      acc[flag] = user.featureFlags[flag] ?? false;
      return acc;
    },
    {} as Record<FeatureFlag, boolean>,
  );

  const result = UserSchema.safeParse({
    id: user.id,
    username: user.username,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    upkeepCompanyId: user.roleId,
    fullName: `${user.firstName}${user.lastName ? ` ${user.lastName}` : ''}`,
    permissions: getUserPermissions(user.ehsAccountType),
    role: user.ehsAccountType ?? undefined,
    featureFlags: user.featureFlags,
    createdAt: user.createdAt,
    lastLoginAt: user.dateOfLastLogin,
    hasEhsEnabled: user.role.specialSubscription?.hasEhsAccess ?? false,
  });

  if (!result.success) {
    logger.error('Error parsing user', result.error);
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Error parsing user',
    });
  }

  await setCache<User>(cacheKey, result.data);
  return result.data;
};

export const getUserPermissions = (userAccountType: string) => {
  // Return admin permissions if admin, otherwise tech permissions
  return userAccountType === USER_ACCOUNT_TYPES.ADMIN
    ? permissions[USER_ACCOUNT_TYPES.ADMIN]
    : permissions[USER_ACCOUNT_TYPES.TECHNICIAN];
};

export const getUsers = async ({
  headers,
  search,
  objectId,
}: {
  headers: Headers;
  search?: string;
  objectId?: string[];
}): Promise<User[]> => {
  const response = await api.get('/api/v1/users', {
    headers: buildDefaultHeaders(headers),
    params: {
      search: search || '',
      objectId: objectId || [],
      userAccountType: [
        UPKEEP_USER_ACCOUNT_TYPES.ADMIN,
        UPKEEP_USER_ACCOUNT_TYPES.TECHNICIAN,
        UPKEEP_USER_ACCOUNT_TYPES.LIMITED_TECHNICIAN,
      ],
    },
  });

  const users = response.data.results as User[];

  if (!users) {
    return [];
  }

  return users.map((user) => ({
    ...user,
    userAccountType: user.role,
    fullName: `${user.firstName}${user.lastName ? ` ${user.lastName}` : ''}`,
    email: user.email ?? user.username,
  }));
};

export const getUsersPublic = async (params: PublicSearchInput): Promise<PaginatedResponse<UserPublic>> => {
  try {
    const { upkeepCompanyId, search, objectId, userAccountType, limit = 50, cursor = 0, mustIncludeObjectIds } = params;
    const requestBody = {
      roleId: upkeepCompanyId,
      limit: limit + 1,
      offset: cursor,
      ...(userAccountType
        ? { userAccountType }
        : {
            userAccountType: [
              UPKEEP_USER_ACCOUNT_TYPES.ADMIN,
              UPKEEP_USER_ACCOUNT_TYPES.TECHNICIAN,
              UPKEEP_USER_ACCOUNT_TYPES.LIMITED_TECHNICIAN,
            ],
          }),
      ...(search ? { search } : {}),
      ...(objectId ? { objectId: Array.isArray(objectId) ? objectId : [objectId] } : {}),
    };

    const response = await api.post('/api/app/search/users', requestBody);

    const users = response.data.results
      .filter((user: UserPublic) => {
        return user.firstName && user.firstName !== undefined;
      })
      .map((user: UserPublic) => ({
        ...user,
        fullName: `${user.firstName} ${user.lastName ? user.lastName : ''}`,
        email: user.username,
      })) as UserPublic[];

    const hasMore = users.length > limit;
    const usersToReturn = hasMore ? users.slice(0, limit) : users;

    const nextCursor = hasMore ? cursor + limit : undefined;

    let isNotIncluded = [];
    if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0 && cursor === 0) {
      isNotIncluded = mustIncludeObjectIds.filter((userId) => !users.map((user) => user.id).includes(userId));

      if (isNotIncluded.length > 0) {
        const mustIncludeUsers = await api.post('/api/app/search/users', {
          ...requestBody,
          objectId: isNotIncluded,
        });

        if (mustIncludeUsers.data.success && mustIncludeUsers.data.results) {
          const usersToAdd = mustIncludeUsers.data.results.map((user: UserPublic) => ({
            ...user,
            fullName: `${user.firstName} ${user.lastName ? user.lastName : ''}`,
            email: user.username,
          }));
          usersToReturn.unshift(...usersToAdd);
        }
      }
    }

    return {
      noResults: usersToReturn.length === 0,
      result: usersToReturn,
      nextCursor,
    };
  } catch (error) {
    logger.error('Error searching users', error);
    return {
      noResults: true,
      result: [],
      nextCursor: undefined,
    };
  }
};

export const getUserPublic = async ({
  upkeepCompanyId,
  id,
  userAccountType,
  search,
  email,
}: {
  upkeepCompanyId: string;
  id?: string;
  userAccountType?: string | string[];
  search?: string;
  email?: string;
}): Promise<UserPublic | undefined> => {
  try {
    const response = await api.post('/api/app/search/users', {
      roleId: upkeepCompanyId,
      objectId: [id],
      ...(userAccountType ? { userAccountType } : {}),
      ...(search ? { search } : {}),
      ...(email ? { email } : {}),
    });

    const users = response.data.results.map((user: UserPublic) => ({
      ...user,
      fullName: `${user.firstName} ${user.lastName ? user.lastName : ''}`,
      email: user.username,
    })) as UserPublic[];

    return users.at(0);
  } catch (error) {
    logger.error('Error searching user', error);
  }
};

export const getUserById = async (id: string, headers: Headers): Promise<User> => {
  const response = await api.get(`/api/v1/users/${id}`, {
    headers: buildDefaultHeaders(headers),
  });

  const user = response.data.result;
  return {
    ...user,
    role: user.ehsAccountType ?? undefined,
    fullName: `${user.firstName}${user.lastName ? ` ${user.lastName}` : ''}`,
    createdAt: user.createdAt,
    lastLoginAt: user.dateOfLastLogin,
    hasEhsEnabled: user.role?.hasEhsEnabled ?? false,
  };
};

export const hasPermission = (
  user: User,
  dataObject: Modules,
  action: AllowedActions,
  requireFullAccess = false,
): boolean => {
  const permission = user.permissions[dataObject][action];

  if (!permission || permission === PERMISSION_LEVELS.NONE) {
    return false;
  }

  if (requireFullAccess) {
    return permission === PERMISSION_LEVELS.FULL;
  }

  // Either full or partial access is acceptable
  return permission === PERMISSION_LEVELS.FULL || permission === PERMISSION_LEVELS.PARTIAL;
};
