import { describe, it, expect, vi, beforeEach } from 'vitest';
import { z } from 'zod';

// Mock environment variables first
vi.mock('../../env', () => ({
  env: {
    OPENAI_API_KEY: 'test-api-key',
  },
  OpenAIModel: {
    GPT_4_TURBO: 'gpt-4-turbo',
  },
}));

// Mock the AI SDK with hoisted mocks to ensure they're applied before imports
const mockGenerateObject = vi.fn();
const mockExperimentalTranscribe = vi.fn();

vi.mock('ai', () => ({
  generateObject: mockGenerateObject,
  experimental_transcribe: mockExperimentalTranscribe,
}));

// Mock the OpenAI SDK
const mockOpenaiTranscription = vi.fn();
const mockOpenai = vi.fn();

vi.mock('@ai-sdk/openai', () => ({
  openai: Object.assign(mockOpenai, {
    transcription: mockOpenaiTranscription,
  }),
}));

describe('AI Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock implementations
    mockGenerateObject.mockResolvedValue({
      object: {
        title: 'Test Incident',
        description: 'This is a test incident',
        category: 'near_miss',
      },
    });

    mockExperimentalTranscribe.mockResolvedValue({
      text: 'This is a transcription of an incident report',
    });

    mockOpenai.mockImplementation((modelName) => `mocked-${modelName}`);
    mockOpenaiTranscription.mockImplementation((model) => `mocked-transcription-${model}`);
  });

  describe('transcribeAudio', () => {
    it('should transcribe audio buffer to text', async () => {
      const { transcribeAudio } = await import('../ai.service');

      const audioBuffer = Buffer.from('test audio data');
      const result = await transcribeAudio(audioBuffer);

      expect(mockOpenaiTranscription).toHaveBeenCalledWith('whisper-1');
      expect(mockExperimentalTranscribe).toHaveBeenCalledWith({
        model: 'mocked-transcription-whisper-1',
        audio: audioBuffer,
      });

      expect(result).toEqual({
        text: 'This is a transcription of an incident report',
      });
    });
  });

  describe('analyzeIncidentTranscript', () => {
    it('should analyze transcript and return structured data', async () => {
      const { analyzeEventTranscript: analyzeIncidentTranscript } = await import('../ai.service');

      const transcript = 'Someone fell on the factory floor at building 3 yesterday';
      const result = await analyzeIncidentTranscript(transcript);

      expect(mockOpenai).toHaveBeenCalledWith('gpt-4-turbo');
      expect(mockGenerateObject).toHaveBeenCalledWith(
        expect.objectContaining({
          model: 'mocked-gpt-4-turbo',
          schema: expect.anything(),
          prompt: expect.stringContaining(transcript),
        }),
      );

      expect(result).toEqual({
        title: 'Test Incident',
        description: 'This is a test incident',
        category: 'near_miss',
      });
    });

    it('should use the correct schema for validation', async () => {
      const { AnalyzeEventTranscriptSchema: AnalyzeIncidentTranscriptSchema } = await import('../ai.service');

      // Verify schema has the required fields
      expect(AnalyzeIncidentTranscriptSchema.shape.title).toBeInstanceOf(z.ZodString);
      expect(AnalyzeIncidentTranscriptSchema.shape.description).toBeInstanceOf(z.ZodString);
      expect(AnalyzeIncidentTranscriptSchema.shape.category).toBeDefined();
    });
  });
});
