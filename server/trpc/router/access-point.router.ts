import {
  createAccessPointsBulk,
  getAccessPointById,
  listAccessPoints,
  updateAccessPoint,
} from '@server/services/access-point.service';
import { matchLocationsWithAI } from '@server/services/ai.service';
import { getAllLocations, getLocations } from '@server/services/location.service';
import { getUsersPublic } from '@server/services/user.service';
import { privateProcedure, publicProcedure, trpc } from '@server/trpc/trpc';
import { createInputKey, matchManyLocations } from '@server/utils/match-many-locations';
import { NO_LOCATION_MATCHED } from '@server/utils/match-single-location';
import { prepareLocationContext } from '@server/utils/prepare-location-context';
import {
  BulkCreateAccessPointInputSchema,
  CreateAccessPointFormSchema,
  IdSchema,
  ListAccessPointsSchema,
  Location,
  UpdateAccessPointSchema,
  UpkeepCompanyIdSchema,
  UserPublic,
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';

export const accessPointRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE)
    .input(CreateAccessPointFormSchema)
    .mutation(async ({ input, ctx }) => {
      const [accessPoint] = await createAccessPointsBulk([input], ctx.user);

      if (!accessPoint) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to create access point',
        });
      }

      return accessPoint;
    }),

  update: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.EDIT)
    .input(UpdateAccessPointSchema.extend({ id: IdSchema.shape.id }))
    .mutation(async ({ input: { id, ...data }, ctx }) => {
      const updatedAccessPoint = await updateAccessPoint(id, data, ctx.user);

      if (!updatedAccessPoint) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to update access point',
        });
      }

      return updatedAccessPoint;
    }),

  // Cursor-based list for useInfiniteQuery
  list: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)
    .input(ListAccessPointsSchema)
    .query(async ({ input, ctx }) => {
      const paginatedAccessPoints = await listAccessPoints(input, ctx.user);

      const locationIds = Array.from(
        new Set(paginatedAccessPoints.result.map((ap) => ap.locationId).filter(Boolean)),
      ) as string[];

      const userIds = Array.from(
        new Set(paginatedAccessPoints.result.map((ap) => ap.createdBy).filter(Boolean)),
      ) as string[];

      const [locationsResponse, users] = await Promise.all([
        locationIds.length > 0
          ? getLocations(
              {
                objectId: locationIds,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { result: [] },
        userIds.length > 0
          ? getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds })
          : { result: [], nextCursor: undefined, noResults: true },
      ]);

      const locationMap = locationsResponse.result?.reduce(
        (acc, location) => {
          acc[location.id] = {
            id: location.id,
            name: location.name,
          };
          return acc;
        },
        {} as Record<string, Location>,
      );

      const userMap = users?.result?.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      const populatedAccessPoints = paginatedAccessPoints.result.map((ap) => ({
        ...ap,
        createdByUser: ap.createdBy ? userMap[ap.createdBy] : undefined,
        location: ap.locationId ? locationMap[ap.locationId] : undefined,
      }));

      return {
        ...paginatedAccessPoints,
        result: populatedAccessPoints,
      };
    }),

  getById: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      return await getAccessPointById(input.id, ctx.user);
    }),

  getByIdPublic: publicProcedure.input(IdSchema.and(UpkeepCompanyIdSchema)).query(async ({ input }) => {
    return await getAccessPointById(input.id, { upkeepCompanyId: input.upkeepCompanyId });
  }),
  bulkCreate: privateProcedure
    .hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE)
    .input(z.array(BulkCreateAccessPointInputSchema))
    .mutation(async ({ input: inputs, ctx }) => {
      // Validate input array
      if (!inputs || inputs.length === 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'No access points provided for bulk creation',
        });
      }

      const candidates = await getAllLocations(ctx.user.upkeepCompanyId);
      const context = prepareLocationContext(candidates);
      const initial = matchManyLocations(inputs, context);

      // AI matching for failed locations - run in parallel for better performance
      const aiMatchingPromises = Array.from(initial.failMap.values())
        .filter((item) => item.reason === NO_LOCATION_MATCHED)
        .map(async (item) => {
          try {
            const aiResponse = await matchLocationsWithAI(item.location, context.fuzzyList);
            if (aiResponse.locationId) {
              return {
                success: true as const,
                item,
                result: {
                  name: item.name,
                  location: item.location,
                  locationId: aiResponse.locationId,
                  locationName: aiResponse.locationName || '',
                },
              };
            }
          } catch (error) {
            console.error('AI location matching failed for:', item.location, error);
          }
          return { success: false as const, item };
        });

      // Process AI results
      const aiResults = await Promise.allSettled(aiMatchingPromises);
      for (const result of aiResults) {
        if (result.status === 'fulfilled' && result.value.success) {
          const { item, result: matchResult } = result.value;
          const key = createInputKey(item);
          initial.successMap.set(key, matchResult);
          initial.failMap.delete(key);
        }
      }

      const success = Array.from(initial.successMap.values());
      const failed = Array.from(initial.failMap.values());

      if (success.length > 0) {
        await createAccessPointsBulk(success, ctx.user);
      }

      return {
        success: true,
        created: success.length,
        failed: failed.length,
        total: inputs.length,
        failedItems: failed,
      };
    }),
});
