import { createId } from '@paralleldrive/cuid2';
import { User } from '@shared/schema.types';
import {
  ALLOWED_ACTIONS,
  generatePermissions,
  MODULES,
  PERMISSION_LEVELS,
  PermissionLevel,
  USER_ACCOUNT_TYPES,
  UserPermission,
} from '@shared/user-permissions';
import { Request } from 'express';

// Re-export types for convenience
export type { User };

/**
 * Create a mock user with proper permissions for testing
 */
export const createMockUser = (userType: 'ADMIN' | 'TECHNICIAN' = 'ADMIN', overrides?: Partial<User>) => {
  const userAccountType = USER_ACCOUNT_TYPES[userType];

  const baseUser = {
    id: createId(),
    username: 'testuser',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    fullName: 'Test User',
    upkeepCompanyId: 'test-role-id',
    groupName: 'Test Group',
    groupId: 'test-group-id',
    role: userAccountType,
    permissions: generatePermissions(userAccountType),
    featureFlags: { webEHS: true },
    hasEhsEnabled: true,
    ...overrides,
  };

  return baseUser;
};

/**
 * Create admin user for testing
 */
export const createAdminUser = (overrides?: Partial<User>) => createMockUser('ADMIN', overrides);

/**
 * Create technician user for testing
 */
export const createTechnicianUser = (overrides?: Partial<User>) => createMockUser('TECHNICIAN', overrides);

/**
 * Create user with custom permissions for testing edge cases
 */
export const createUserWithCustomPermissions = (permissions: UserPermission, overrides?: Partial<User>): User => {
  return createMockUser('TECHNICIAN', {
    permissions,
    ...overrides,
  });
};

/**
 * Create user with no permissions for testing unauthorized scenarios
 */
export const createUnauthorizedUser = (overrides?: Partial<User>) => {
  return createUserWithCustomPermissions({}, overrides);
};

/**
 * Enhanced context type matching the tRPC context
 */
export type MockEnhancedContext = {
  user: User | null;
  req: Request;
  permissionLevel?: PermissionLevel;
  needPartialCheck?: boolean;
};

/**
 * Create mock TRPC context for testing
 */
export const createMockContext = (user: User | null = null): MockEnhancedContext => ({
  user,
  req: {
    headers: {
      'auth-token': 'test-token',
      'x-user-token': 'test-user-token',
    },
  } as Partial<Request> as Request,
});

/**
 * Permission test scenarios for common use cases
 */
export const permissionTestScenarios = {
  adminUser: createAdminUser(),
  technicianUser: createTechnicianUser(),
  unauthorizedUser: createUnauthorizedUser(),
  partialEditUser: {
    [MODULES.EHS_INCIDENT]: {
      [ALLOWED_ACTIONS.VIEW]: PERMISSION_LEVELS.FULL,
      [ALLOWED_ACTIONS.EDIT]: PERMISSION_LEVELS.PARTIAL,
    },
  },
};
