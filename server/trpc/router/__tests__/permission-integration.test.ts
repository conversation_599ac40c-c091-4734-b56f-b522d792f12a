import { createId } from '@paralleldrive/cuid2';
import { createEvent, getEventById } from '@server/services/incident.service';
import { getUserById, getUsersPublic, hasPermission } from '@server/services/user.service';
import { incidentRouter } from '@server/trpc/router/incident.router';
import { ALLOWED_ACTIONS, MODULES, PERMISSION_LEVELS, UserPermission } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { subDays, subSeconds } from 'date-fns';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  createAdminUser,
  createMockContext,
  createTechnicianUser,
  createUnauthorizedUser,
  createUserWithCustomPermissions,
} from './test-utils';

const yesterday = subDays(new Date(), 1);

// Mock services
vi.mock('@server/services/incident.service', () => ({
  createEvent: vi.fn(),
  getEventById: vi.fn(),
  isResourceOwner: vi.fn(),
}));

vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsersPublic: vi.fn(),
  hasPermission: vi.fn(),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: 'location-1', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: 'location-1', name: 'Test Location' }]),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue([]),
  getAssetById: vi.fn().mockResolvedValue({ id: 'asset-1', name: 'Test Asset', description: 'Test Description' }),
}));

describe('Permission Integration Tests', () => {
  const mockEvent = {
    id: createId(),
    slug: 'INC-001',
    title: 'Test Incident',
    description: 'Test Description',
    reportedBy: 'user-123',
    type: 'incident' as const,
    category: 'chemical' as const,
    severity: 'high' as const,
    status: 'open' as const,
    reportedAt: yesterday,
    upkeepCompanyId: 'company-1',
    reportedByName: 'Test User',
    reportedByEmail: '<EMAIL>',
    locationId: 'location-1',
    assetIds: [],
    archived: false,
    immediateActions: '',
    rootCause: 'equipment_failure' as const,
    otherRootCause: '',
    oshaReportable: false,
    aiConfidenceScore: 0.5,
    updatedAt: new Date(),
    deletedAt: null,
    media: [],
    teamMembersToNotify: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    const adminUser = createAdminUser();
    vi.mocked(getUserById).mockResolvedValue(adminUser);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [
        {
          id: adminUser.id,
          firstName: adminUser.firstName,
          lastName: adminUser.lastName,
          fullName: adminUser.fullName,
          username: adminUser.email,
          email: adminUser.email,
        },
      ],
      nextCursor: undefined,
    });
    vi.mocked(getEventById).mockResolvedValue(mockEvent);
  });

  describe('Admin User Permissions', () => {
    const adminUser = createAdminUser();
    const adminContext = createMockContext(adminUser);

    beforeEach(() => {
      // Admin should have full permissions
      vi.mocked(hasPermission).mockReturnValue(true);
    });

    it('should allow admin to create incidents', async () => {
      vi.mocked(createEvent).mockResolvedValue({
        ...mockEvent,
      });

      await expect(
        incidentRouter.createCaller(adminContext).create({
          title: 'Admin Incident',
          description: 'Created by admin',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: yesterday,
        }),
      ).resolves.not.toThrow();

      expect(hasPermission).toHaveBeenCalledWith(adminUser, MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE, false);
    });

    it('should allow admin to view any incident', async () => {
      await expect(incidentRouter.createCaller(adminContext).getById({ id: mockEvent.id })).resolves.toBeDefined();

      expect(hasPermission).toHaveBeenCalledWith(adminUser, MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW, false);
    });
  });

  describe('Technician User Permissions', () => {
    const technicianUser = createTechnicianUser();
    const technicianContext = createMockContext(technicianUser);

    beforeEach(() => {
      // Technician has limited permissions
      vi.mocked(hasPermission).mockImplementation((user, module, action) => {
        if (module === MODULES.EHS_INCIDENT) {
          return [ALLOWED_ACTIONS.CREATE, ALLOWED_ACTIONS.VIEW, ALLOWED_ACTIONS.EDIT].includes(
            action as typeof ALLOWED_ACTIONS.CREATE,
          );
        }
        return false;
      });
    });

    it('should allow technician to create incidents', async () => {
      vi.mocked(createEvent).mockResolvedValue({
        ...mockEvent,
      });

      await expect(
        incidentRouter.createCaller(technicianContext).create({
          title: 'Technician Incident',
          description: 'Created by technician',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: yesterday,
        }),
      ).resolves.not.toThrow();
    });

    it('should allow technician to view incidents (partial permission)', async () => {
      await expect(incidentRouter.createCaller(technicianContext).getById({ id: mockEvent.id })).resolves.toBeDefined();
    });
  });

  describe('Unauthorized User', () => {
    const unauthorizedUser = createUnauthorizedUser();
    const unauthorizedContext = createMockContext(unauthorizedUser);

    beforeEach(() => {
      // No permissions
      vi.mocked(hasPermission).mockReturnValue(false);
    });

    it('should deny unauthorized user from creating incidents', async () => {
      await expect(
        incidentRouter.createCaller(unauthorizedContext).create({
          title: 'Unauthorized Incident',
          description: 'Should fail',
          type: 'incident',
          category: 'chemical',
          severity: 'high',
          reportedAt: yesterday,
        }),
      ).rejects.toThrow(TRPCError);
    });

    it('should deny unauthorized user from viewing incidents', async () => {
      await expect(incidentRouter.createCaller(unauthorizedContext).getById({ id: mockEvent.id })).rejects.toThrow(
        TRPCError,
      );
    });
  });

  describe('Partial Permissions with Resource Ownership', () => {
    const partialPermissions: UserPermission = {
      [MODULES.EHS_INCIDENT]: {
        [ALLOWED_ACTIONS.VIEW]: PERMISSION_LEVELS.PARTIAL,
        [ALLOWED_ACTIONS.EDIT]: PERMISSION_LEVELS.PARTIAL,
      },
    };

    const partialUser = createUserWithCustomPermissions(partialPermissions);
    const partialContext = createMockContext(partialUser);

    beforeEach(() => {
      // Partial permissions - need contextual checks
      vi.mocked(hasPermission).mockImplementation((user, module, action) => {
        const modulePermissions = user.permissions[module];
        if (modulePermissions) {
          const permission = modulePermissions[action];
          return !!permission && permission !== PERMISSION_LEVELS.NONE;
        }
        return false;
      });
    });

    it('should allow partial user to view their own incidents', async () => {
      // Mock that user owns this incident
      const ownedIncident = { ...mockEvent, reportedBy: partialUser.id };
      vi.mocked(getEventById).mockResolvedValue(ownedIncident);

      await expect(
        incidentRouter.createCaller(partialContext).getById({ id: ownedIncident.id }),
      ).resolves.toBeDefined();
    });

    it('should demonstrate permission middleware is called', async () => {
      vi.mocked(getEventById).mockResolvedValue(mockEvent);

      await incidentRouter.createCaller(partialContext).getById({ id: mockEvent.id });

      // Verify the permission system was called
      expect(hasPermission).toHaveBeenCalledWith(partialUser, MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW, false);
    });
  });
});
