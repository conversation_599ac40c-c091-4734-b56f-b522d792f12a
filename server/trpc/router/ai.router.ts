import { z } from 'zod';

import { privateProcedure, publicProcedure, trpc } from '../trpc';
import { analyzeEventTranscript, analyzeCapaTranscript, transcribeAudio } from 'server/services/ai.service';
import { TRPCError } from '@trpc/server';

export const aiRouter = trpc.router({
  transcribePublic: publicProcedure
    .input(
      z.object({
        audioBase64: z.string().min(1),
      }),
    )
    .mutation(async ({ input }) => {
      if (!input.audioBase64) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Audio is required' });
      }
      try {
        const audioBuffer = Buffer.from(input.audioBase64, 'base64');
        return await transcribeAudio(audioBuffer);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process audio',
          cause: error,
        });
      }
    }),
  analyzePublic: publicProcedure
    .input(
      z.object({
        text: z.string().min(1),
        timezone: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      const { text, timezone } = input;

      return await analyzeEventTranscript(text, timezone);
    }),
  transcribe: privateProcedure
    .input(
      z.object({
        audioBase64: z.string().min(1),
      }),
    )
    // {ctx, input} both available here, ctx skipped since we're not using it
    .mutation(async ({ input }) => {
      if (!input.audioBase64) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Audio is required' });
      }
      try {
        const audioBuffer = Buffer.from(input.audioBase64, 'base64');
        return await transcribeAudio(audioBuffer);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to process audio',
          cause: error,
        });
      }
    }),

  analyzeIncident: privateProcedure
    .input(
      z.object({
        text: z.string().min(1),
        timezone: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      return await analyzeEventTranscript(input.text, input.timezone);
    }),

  analyzeCapa: privateProcedure
    .input(
      z.object({
        text: z.string().min(1),
        timezone: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      return await analyzeCapaTranscript(input.text, input.timezone);
    }),
});
