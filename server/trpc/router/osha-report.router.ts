import {
  create<PERSON>haRep<PERSON>,
  get<PERSON>haReportById,
  list<PERSON>haReports,
  update<PERSON>haReport,
} from '@server/services/osha-report.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { getIpAddress } from '@server/utils/get-ip-address';
import { logger } from '@server/utils/logger';
import {
  CreateOshaReportFormSchema,
  IdSchema,
  ListOshaReportsSchema,
  UpdateOshaReportFormSchema,
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';

export const oshaReportRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE)
    .input(CreateOshaReportFormSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      const report = await createOshaReport(
        { ...input, ipAddress, userAgent: ctx.req.headers['user-agent'] },
        ctx.user,
      );
      if (!report) {
        logger.error('Failed to create OSHA report', { input, user: ctx.user });
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create OSHA report' });
      }
      return report;
    }),
  update: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT)
    .input(UpdateOshaReportFormSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      const report = await updateOshaReport(
        { ...input, ipAddress, userAgent: ctx.req.headers['user-agent'] },
        ctx.user,
      );
      if (!report) {
        logger.error('Failed to update OSHA report', { input, user: ctx.user });
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to update OSHA report' });
      }
      return report;
    }),
  getById: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      return await getOshaReportById(input.id, ctx.user);
    }),
  list: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(ListOshaReportsSchema)
    .query(async ({ input, ctx }) => {
      return await listOshaReports(input, ctx.user);
    }),
});
