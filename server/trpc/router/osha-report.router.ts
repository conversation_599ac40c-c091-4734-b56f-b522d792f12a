import { getAssets } from '@server/services/asset.service';
import { getLocationById } from '@server/services/location.service';
import {
  createOshaReport,
  getOshaReportById,
  listOshaReports,
  updateOshaReport
} from '@server/services/osha-report.service';
import { getUserPublic } from '@server/services/user.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { getIpAddress } from '@server/utils/get-ip-address';
import { logger } from '@server/utils/logger';
import {
  CreateOshaReportFormSchema,
  IdSchema,
  ListOshaReportsSchema,
  UpdateOshaReportFormSchema
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';

export const oshaReportRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE)
    .input(CreateOshaReportFormSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      const report = await createOshaReport(
        { ...input, ipAddress, userAgent: ctx.req.headers['user-agent'] },
        ctx.user,
      );
      if (!report) {
        logger.error('Failed to create OSHA report', { input, user: ctx.user });
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create OSHA report' });
      }
      return report;
    }),
  update: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT)
    .input(UpdateOshaReportFormSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      const report = await updateOshaReport(
        { ...input, ipAddress, userAgent: ctx.req.headers['user-agent'] },
        ctx.user,
      );
      if (!report) {
        logger.error('Failed to update OSHA report', { input, user: ctx.user });
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to update OSHA report' });
      }
      return report;
    }),
  getById: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const oshaReport = await getOshaReportById(input.id, ctx.user);

      const [createdBy, eventReportedBy, assets, location] = await Promise.all([
        oshaReport.createdBy
          ? getUserPublic({ upkeepCompanyId: oshaReport.upkeepCompanyId, id: oshaReport.createdBy })
          : undefined,
        oshaReport.event?.reportedBy
          ? getUserPublic({ upkeepCompanyId: oshaReport.upkeepCompanyId, id: oshaReport.event.reportedBy })
          : undefined,
        oshaReport.event?.assetIds && oshaReport.event?.assetIds.length > 0
          ? getAssets(
              {
                objectId: oshaReport.event.assetIds,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { noResults: false, result: [], nextCursor: undefined },
        oshaReport.event?.locationId ? getLocationById(oshaReport.event?.locationId, ctx.req.headers) : undefined,
      ]);

      return {
        ...oshaReport,
        createdBy,
        event: {
          ...oshaReport.event,
          assets: assets.result,
          location: location,
          reportedBy: eventReportedBy,
        },
      };
    }),

  getByIdForEdit: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      return await getOshaReportById(input.id, ctx.user);
    }),

  list: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(ListOshaReportsSchema)
    .query(async ({ input, ctx }) => {
      return await listOshaReports(input, ctx.user);
    }),
});
