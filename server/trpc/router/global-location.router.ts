import { privateProcedure, trpc } from '@server/trpc/trpc';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  CreateGlobalLocationSchema,
  DeleteGlobalLocationSchema,
  GlobalLocationFiltersSchema,
  ListGlobalLocationsSchema,
} from '@shared/location.types';
import {
  createGlobalLocation,
  deleteGlobalLocation,
  listGlobalLocations,
} from '@server/services/global-location.service';
import { TRPCError } from '@trpc/server';
import { getUsersPublic } from '@server/services/user.service';

export const globalLocationRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.CREATE)
    .input(CreateGlobalLocationSchema)
    .mutation(async ({ input, ctx }) => {
      console.log('input', input);

      const globalLocation = await createGlobalLocation(input, ctx.user);
      if (!globalLocation) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create global location' });
      }
      return globalLocation;
    }),
  list: privateProcedure
    .hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.VIEW)
    .input(ListGlobalLocationsSchema)
    .query(async ({ input, ctx }) => {
      const paginatedGlobalLocations = await listGlobalLocations(input, ctx.user);

      const userIds = Array.from(
        new Set(paginatedGlobalLocations.result.map((gl) => gl.createdBy).filter(Boolean)),
      ) as string[];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      const userMap = users.result?.reduce(
        (acc, user) => {
          acc[user.id] = {
            id: user.id,
            fullName: user.fullName ?? 'Unknown',
          };

          return acc;
        },
        {} as Record<string, { id: string; fullName: string }>,
      );

      return {
        ...paginatedGlobalLocations,
        result: paginatedGlobalLocations.result.map((gl) => ({
          ...gl,
          createdBy: userMap?.[gl.createdBy],
        })),
      };
    }),
  delete: privateProcedure
    .hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.DELETE)
    .input(DeleteGlobalLocationSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await deleteGlobalLocation(input, ctx.user);
      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to delete global location' });
      }
      return result;
    }),
});
