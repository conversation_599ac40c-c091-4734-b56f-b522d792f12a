import { privateProcedure, trpc } from '@server/trpc/trpc';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  CreateGlobalLocationSchema,
  ArchiveGlobalLocationSchema,
  ListGlobalLocationsSchema,
} from '@shared/settings.types';
import {
  createGlobalLocation,
  toggleArchiveGlobalLocation,
  listGlobalLocations,
} from '@server/services/global-location.service';
import { TRPCError } from '@trpc/server';
import { getUsersPublic } from '@server/services/user.service';

export const globalLocationRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.CREATE)
    .input(CreateGlobalLocationSchema)
    .mutation(async ({ input, ctx }) => {
      const globalLocation = await createGlobalLocation(input, ctx.user);
      if (!globalLocation) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create global location' });
      }
      return globalLocation;
    }),
  list: privateProcedure
    .hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.VIEW)
    .input(ListGlobalLocationsSchema)
    .query(async ({ input, ctx }) => {
      const paginatedGlobalLocations = await listGlobalLocations(input, ctx.user);

      const userIds = Array.from(
        new Set(paginatedGlobalLocations.result.map((gl) => gl.createdBy).filter(Boolean)),
      ) as string[];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      const userMap = users.result?.reduce(
        (acc, user) => {
          acc[user.id] = {
            id: user.id,
            fullName: user.fullName ?? 'Unknown',
            username: user.username ?? 'Unknown',
          };

          return acc;
        },
        {} as Record<string, { id: string; fullName: string; username: string }>,
      );

      return {
        ...paginatedGlobalLocations,
        result: paginatedGlobalLocations.result.map((gl) => ({
          ...gl,
          createdBy: userMap?.[gl.createdBy],
        })),
      };
    }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.EDIT)
    .input(ArchiveGlobalLocationSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await toggleArchiveGlobalLocation(input, ctx.user);
      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to delete global location' });
      }
      return result;
    }),
});
