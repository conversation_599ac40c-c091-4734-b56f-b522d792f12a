import { searchLocationsPublic, getLocations } from '@server/services/location.service';
import { privateProcedure, trpc, publicProcedure } from '@server/trpc/trpc';
import { Location, LocationSearchInputSchema, PaginatedResponse, PublicSearchSchema } from '@shared/schema.types';

/**
 * Location Router with Pagination
 *
 * This router follows the same pagination pattern as the asset router.
 * It supports search, pagination, and restrictions level filtering.
 */

export const locationRouter = trpc.router({
  // Cursor-based search for useInfiniteQuery
  search: privateProcedure
    .input(LocationSearchInputSchema.default({ cursor: 0, limit: 10, search: '' }))
    .query(async ({ input, ctx }): Promise<PaginatedResponse<Location>> => {
      const { cursor = 0, limit = 10, search, mustIncludeObjectIds } = input;

      return await getLocations(
        {
          cursor,
          limit,
          search,
          mustIncludeObjectIds,
          sort: 'createdAt DESC',
        },
        ctx.req.headers,
      );
    }),

  // Public location search endpoint - requires roleId and authentication headers
  searchPublic: publicProcedure
    .input(PublicSearchSchema)
    .query(async ({ input }): Promise<PaginatedResponse<Location>> => {
      return await searchLocationsPublic(input);
    }),
});
