import { sendCommentMentionNotification } from '@server/services/comment-notification.service';
import { createComment, deleteComment, fetchCommentById, fetchComments } from '@server/services/comment.service';
import { getInfo } from '@server/services/company.service';
import { getUsersPublic } from '@server/services/user.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { CreateCommentFormSchema, IdSchema, ListCommentsSchema } from '@shared/schema.types';
import { TRPCError } from '@trpc/server';
import { env } from 'env';
import { toZonedTime } from 'date-fns-tz';

export const commentRouter = trpc.router({
  create: privateProcedure.input(CreateCommentFormSchema).mutation(async ({ input, ctx }) => {
    const result = await createComment(input, ctx.user);

    if (!result) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Failed to create comment. Please try again.',
      });
    }

    const { newComment, savedMentions } = result;

    const routeBuilder = {
      event: ROUTES.BUILD_INCIDENT_DETAILS_PATH,
      capa: ROUTES.BUILD_CAPA_DETAILS_PATH,
    };

    const toUserIds = savedMentions?.map((mention) => mention.userId) || [];

    const [users, { clientTimezone }] = await Promise.all([
      getUsersPublic({
        upkeepCompanyId: newComment.upkeepCompanyId,
        objectId: toUserIds,
      }),
      getInfo(ctx.req.headers),
    ]);

    const timestamp = clientTimezone ? toZonedTime(newComment.createdAt, clientTimezone) : newComment.createdAt;

    const updatedContent = newComment.content.replace(/@(\w+)/g, (_, userId) => {
      const user = users.result.find((u) => u.id === userId);
      return user ? `@${user.fullName}` : `@${userId}`; // fallback to original if not found
    });

    await sendCommentMentionNotification({
      data: {
        ...input,
        entityUrl: `${env.EHS_URL}${routeBuilder[input.entityType](input.entityId)}`,
        timestamp,
        reporter: {
          fullName: ctx.user.fullName,
          email: ctx.user.email,
        },
        content: updatedContent,
        users: users.result,
      },
      toUsers: users.result
        .filter((user) => user.email && user.fullName)
        .map((user) => ({
          email: user.email!,
          fullName: user.fullName!,
          type: 'to',
        })),
    });

    return newComment;
  }),
  list: privateProcedure.input(ListCommentsSchema).query(async ({ input, ctx }) => {
    return await fetchComments({ entityId: input.entityId, entityType: input.entityType }, ctx.user);
  }),
  getById: privateProcedure.input(IdSchema).query(async ({ input, ctx }) => {
    return await fetchCommentById({ id: input.id }, ctx.user);
  }),
  delete: privateProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    return await deleteComment({ id: input.id }, ctx.user);
  }),
});
