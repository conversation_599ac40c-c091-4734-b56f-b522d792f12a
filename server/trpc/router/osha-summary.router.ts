import {
  getEstablishmentInformationByYear,
  upsertOshaSummary,
  upsertOshaExecutiveCertification,
  toggleArchiveOshaSummary,
} from '@server/services/osha-summary.service';
import { getOshaCasesSummary } from '@server/services/osha-report.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { getIpAddress } from '@server/utils/get-ip-address';
import {
  GetEstablishInformationByYearSchema,
  OshaSummaryExecutiveCertificationSchema,
  UpsertOshaCompanyInformationSchema,
} from '@shared/osha.types';

import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { logger } from '@server/utils/logger';
import { IdSchema } from '@shared/schema.types';

export const oshaSummaryRouter = trpc.router({
  getOshaCasesSummary: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(GetEstablishInformationByYearSchema)
    .query(async ({ input, ctx }) => {
      const [casesSummary, establishmentInfo] = await Promise.all([
        getOshaCasesSummary(input, ctx.user),
        getEstablishmentInformationByYear(input, ctx.user),
      ]);

      const totalHoursWorked = establishmentInfo?.companyTotalHoursWorked ?? 0;

      let trcRate = 0;
      let dartRate = 0;

      if (totalHoursWorked > 0 && casesSummary) {
        trcRate = (casesSummary.totalCases / totalHoursWorked) * 200000;
        dartRate = ((casesSummary.daysAwayCases + casesSummary.restrictedWorkCases) / totalHoursWorked) * 200000;
      }

      return {
        ...casesSummary,
        trcRate: parseFloat(trcRate.toFixed(2)),
        dartRate: parseFloat(dartRate.toFixed(2)),
      };
    }),
  getEstablishmentInformationByYear: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.VIEW)
    .input(GetEstablishInformationByYearSchema)
    .query(({ input, ctx }) => {
      return getEstablishmentInformationByYear(input, ctx.user);
    }),
  upsertEstablishmentInformation: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE)
    .input(UpsertOshaCompanyInformationSchema)
    .mutation(async ({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      const userAgent = ctx.req.headers['user-agent'];
      const summary = await upsertOshaSummary({ ...input, ipAddress, userAgent }, ctx.user);
      if (!summary) {
        logger.error('Failed to upsert establishment information', { input, user: ctx.user });
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to upsert establishment information',
        });
      }
      return summary;
    }),
  upsertExecutiveCertification: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE)
    .input(OshaSummaryExecutiveCertificationSchema)
    .mutation(({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req);
      const userAgent = ctx.req.headers['user-agent'];
      return upsertOshaExecutiveCertification({ ...input, ipAddress, userAgent }, ctx.user);
    }),
  toggleArchiveOshaSummary: privateProcedure
    .hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.CREATE)
    .input(IdSchema)
    .mutation(({ input, ctx }) => {
      const ipAddress = getIpAddress(ctx.req) ?? 'unknown';
      const userAgent = ctx.req.headers['user-agent'] ?? 'unknown';
      return toggleArchiveOshaSummary({ id: input.id, ipAddress, userAgent }, ctx.user);
    }),
});
