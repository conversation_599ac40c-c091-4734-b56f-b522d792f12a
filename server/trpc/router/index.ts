import { aiRouter } from '@server/trpc/router/ai.router';
import { assetRouter } from '@server/trpc/router/asset.router';
import { auditTrailRouter } from '@server/trpc/router/audit-trail.router';
import { commentRouter } from '@server/trpc/router/comment.router';
import { configRouter } from '@server/trpc/router/config.router';
import { fileRouter } from '@server/trpc/router/file.router';
import { incidentRouter } from '@server/trpc/router/incident.router';
import { locationRouter } from '@server/trpc/router/location.router';
import { userRouter } from '@server/trpc/router/user.router';
import { capaRouter } from 'server/trpc/router/capa.router';
import { accessPointRouter } from '@server/trpc/router/access-point.router';
import { workOrderRouter } from '@server/trpc/router/work-order.router';
import { oshaReportRouter } from '@server/trpc/router/osha-report.router';
import { oshaSummaryRouter } from '@server/trpc/router/osha-summary.router';
import { globalLocationRouter } from '@server/trpc/router/global-location.router';

import { trpc } from '@server/trpc/trpc';

export const router = trpc.router({
  incident: incidentRouter,
  capa: capaRouter,
  comment: commentRouter,
  auditTrail: auditTrailRouter,
  user: userRouter,
  ai: aiRouter,
  file: fileRouter,
  asset: assetRouter,
  location: locationRouter,
  accessPoint: accessPointRouter,
  workOrder: workOrderRouter,
  config: configRouter,
  oshaReport: oshaReportRouter,
  oshaSummary: oshaSummaryRouter,
  globalLocation: globalLocationRouter,
});

export type AppRouter = typeof router;
