import { getAssetById } from '@server/services/asset.service';
import { sendCapaAssignedNotification, sendCapaUpdatedNotification } from '@server/services/capa-notification.service';
import { createCapa, getCapaById, listCapas, updateCapa } from '@server/services/capa.service';
import { getEventById } from '@server/services/incident.service';
import { getLocationById } from '@server/services/location.service';
import { getUserPublic, getUsersPublic } from '@server/services/user.service';
import { privateProcedure, trpc } from '@server/trpc/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import {
  CreateCapasFormSchema,
  EditCapasFormSchema,
  IdSchema,
  ListCapasSchema,
  UserPublic,
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { env } from 'env';

export const capaRouter = trpc.router({
  // Create CAPA procedure - requires CAPA create permission
  create: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)
    .input(CreateCapasFormSchema)
    .mutation(async ({ input, ctx }) => {
      const createdCapa = await createCapa(input, ctx.user);

      if (!createdCapa) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to create CAPA',
        });
      }

      const teamMembers =
        createdCapa?.teamMembersToNotify && createdCapa?.teamMembersToNotify?.length > 0
          ? createdCapa?.teamMembersToNotify
          : [];

      const allUserIds = teamMembers.concat(createdCapa?.ownerId);
      const toNotifyUserIds = [...new Set(allUserIds)];

      const [location, event, usersToNotify] = await Promise.all([
        createdCapa?.locationId ? getLocationById(createdCapa?.locationId, ctx.req.headers) : undefined,
        createdCapa?.eventId ? getEventById(createdCapa?.eventId, ctx.user, ctx.needPartialCheck || false) : undefined,
        getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: toNotifyUserIds }),
      ]);

      const owner = usersToNotify.result.find((user) => user.id === createdCapa?.ownerId);

      await sendCapaAssignedNotification({
        capa: {
          ...createdCapa,
          linkedEvent: event
            ? {
                title: event?.title,
                slug: event?.slug!,
                url: `${env.EHS_URL}${ROUTES.BUILD_INCIDENT_DETAILS_PATH(event?.id)}`,
              }
            : undefined,
          owner: {
            email: owner?.email,
            fullName: owner?.fullName,
          },
          location: location,
          capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(createdCapa.id)}`,
        },
        toUsers: [
          { email: ctx.user.email, fullName: ctx.user.fullName, type: 'to' },
          ...usersToNotify.result
            .filter((user) => user.email && user.fullName)
            .map((user) => ({
              email: user.email!,
              fullName: user.fullName!,
              type: 'to' as const,
            })),
        ],
      });

      return createdCapa;
    }),

  // Get CAPA by ID procedure - requires CAPA view permission
  getById: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const capa = await getCapaById(input.id, ctx.user, ctx.needPartialCheck || false);

      // If CAPA not found, throw a NOT_FOUND error
      if (!capa) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `CAPA with ID ${input.id} not found`,
        });
      }

      const [owner, implementedBy, voePerformedBy, asset, location, incident, usersToNotify] = await Promise.all([
        getUserPublic({ id: capa.ownerId, upkeepCompanyId: ctx.user.upkeepCompanyId }),
        capa.implementedBy
          ? getUserPublic({ id: capa.implementedBy, upkeepCompanyId: ctx.user.upkeepCompanyId })
          : undefined,
        capa.voePerformedBy
          ? getUserPublic({ id: capa.voePerformedBy, upkeepCompanyId: ctx.user.upkeepCompanyId })
          : undefined,
        capa?.assetId ? getAssetById(capa?.assetId, ctx.req.headers) : undefined,
        capa?.locationId ? getLocationById(capa?.locationId, ctx.req.headers) : undefined,
        capa?.eventId ? getEventById(capa?.eventId, ctx.user, ctx.needPartialCheck || false) : undefined,
        capa?.teamMembersToNotify && capa?.teamMembersToNotify?.length > 0
          ? getUsersPublic({
              upkeepCompanyId: capa.upkeepCompanyId,
              objectId: capa?.teamMembersToNotify,
            })
          : {
              result: [],
              total: 0,
              hasMore: false,
              cursor: 0,
            },
      ]);

      return {
        ...capa,
        owner,
        implementedBy,
        voePerformedBy,
        asset,
        location,
        incident,
        teamMembersToNotify: usersToNotify?.result,
      };
    }),

  getByIdForEdit: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const capa = await getCapaById(input.id, ctx.user, ctx.needPartialCheck || false);

      if (!capa) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `CAPA with ID ${input.id} not found`,
        });
      }

      return capa;
    }),

  // Cursor-based list for useInfiniteQuery
  list: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
    .input(
      ListCapasSchema.default({
        cursor: 0,
        limit: 10,
      }),
    )
    .query(async ({ ctx, input }) => {
      const capas = await listCapas(input, ctx.user, ctx.needPartialCheck || false);

      const userIds = Array.from(new Set(capas.result.map((capa) => capa.ownerId).filter(Boolean))) as string[];

      const users = await getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: userIds });

      const usersMap = users.result.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      const capasWithUsers = capas.result.map((capa) => {
        const user = usersMap[capa.ownerId];
        return {
          ...capa,
          owner: user,
        };
      });

      return {
        ...capas,
        result: capasWithUsers,
      };
    }),

  // Update CAPA procedure
  update: privateProcedure
    .hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT)
    .input(EditCapasFormSchema)
    .mutation(async ({ input, ctx }) => {
      const updatedCapa = await updateCapa(input.id, input, ctx.user);

      if (!updatedCapa) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to update CAPA',
        });
      }

      const teamMembers =
        updatedCapa?.teamMembersToNotify && updatedCapa?.teamMembersToNotify?.length > 0
          ? updatedCapa?.teamMembersToNotify
          : [];

      const allUserIds = teamMembers.concat(updatedCapa?.ownerId);
      const toNotifyUserIds = [...new Set(allUserIds)];

      const [location, event, usersToNotify] = await Promise.all([
        updatedCapa?.locationId ? getLocationById(updatedCapa?.locationId, ctx.req.headers) : undefined,
        updatedCapa?.eventId ? getEventById(updatedCapa?.eventId, ctx.user, ctx.needPartialCheck || false) : undefined,
        getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: toNotifyUserIds }),
      ]);

      const owner = usersToNotify.result.find((user) => user.id === updatedCapa?.ownerId);

      await sendCapaUpdatedNotification({
        capa: {
          ...updatedCapa,
          linkedEvent: event
            ? {
                title: event?.title,
                slug: event?.slug!,
                url: `${env.EHS_URL}${ROUTES.BUILD_INCIDENT_DETAILS_PATH(event?.id)}`,
              }
            : undefined,
          owner: {
            email: owner?.email,
            fullName: owner?.fullName,
          },
          updatedBy: {
            email: ctx.user.email,
            fullName: ctx.user.fullName,
          },
          location: location,
          capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(updatedCapa.id)}`,
        },
        toUsers: [
          { email: ctx.user.email, fullName: ctx.user.fullName, type: 'to' },
          ...usersToNotify.result
            .filter((user) => user.email && user.fullName)
            .map((user) => ({
              email: user.email!,
              fullName: user.fullName!,
              type: 'to' as const,
            })),
        ],
      });

      return updatedCapa;
    }),
});
