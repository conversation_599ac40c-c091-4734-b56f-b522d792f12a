import { getAssets, searchAssetsPublic } from '@server/services/asset.service';
import {
  sendIncidentCreateNotification,
  sendIncidentUpdateNotification,
  sendPublicIncidentCreateNotification,
} from '@server/services/incident-notification.service';
import {
  createEvent,
  createEventPublic,
  getEventById,
  listEvents,
  updateEvent,
} from '@server/services/incident.service';
import { getLocationById, getLocations, searchLocationsPublic } from '@server/services/location.service';
import { getUserById, getUserPublic, getUsersPublic } from '@server/services/user.service';
import { privateProcedure, publicProcedure, trpc } from '@server/trpc/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import {
  CreateEventFormPublicSchema,
  CreateEventFormSchema,
  EditEventFormSchema,
  IdSchema,
  ListEventSchema,
  Location,
} from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES, USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { TRPCError } from '@trpc/server';
import { env } from 'env';

export const incidentRouter = trpc.router({
  createPublic: publicProcedure.input(CreateEventFormPublicSchema).mutation(async ({ input }) => {
    const [adminUsers, possibleReporter] = await Promise.all([
      getUsersPublic({
        upkeepCompanyId: input.upkeepCompanyId,
        userAccountType: USER_ACCOUNT_TYPES.ADMIN,
      }),
      getUserPublic({
        upkeepCompanyId: input.upkeepCompanyId,
        email: input.email,
      }),
    ]);

    const usersToNotify = adminUsers.result.filter((user) => user.email !== input.email);

    const createdIncident = await createEventPublic(
      { ...input, teamMembersToNotify: usersToNotify.map((user) => user.id) },
      {
        email: possibleReporter?.email ?? input.email,
        fullName: possibleReporter?.fullName ?? input.name,
        id: possibleReporter?.id,
      },
    );

    if (!createdIncident) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Failed to create public incident',
      });
    }

    const [location, assets] = await Promise.all([
      createdIncident.locationId
        ? searchLocationsPublic({
            upkeepCompanyId: input.upkeepCompanyId,
            search: '',
            objectId: [createdIncident.locationId],
            limit: 1,
          })
        : undefined,
      createdIncident.assetIds && createdIncident.assetIds.length > 0
        ? searchAssetsPublic({
            upkeepCompanyId: input.upkeepCompanyId,
            search: '',
            objectId: createdIncident.assetIds,
            limit: 100,
          })
        : { noResults: true, result: [], nextCursor: undefined },
    ]);

    await sendPublicIncidentCreateNotification({
      event: {
        ...createdIncident,
        location: location?.result?.at(0),
        assets: assets?.result || [],
        incidentUrl: `${env.EHS_URL}${ROUTES.BUILD_INCIDENT_DETAILS_PATH(createdIncident.id)}`,
        reportedBy: {
          email: input.email,
          fullName: input.name,
        },
      },
      toUsers: [
        {
          email: possibleReporter?.email ?? input.email,
          fullName: possibleReporter?.fullName ?? input.name,
          type: 'to',
        },
        ...usersToNotify
          .filter((user) => user.email && user.fullName)
          .map((user) => ({
            email: user.email!,
            fullName: user.fullName!,
            type: 'to' as const,
          })),
      ],
    });

    return createdIncident;
  }),
  create: privateProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE)
    .input(CreateEventFormSchema)
    .mutation(async ({ input, ctx }) => {
      const createdEvent = await createEvent(input, ctx.user);

      if (!createdEvent) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to create incident',
        });
      }

      const [location, assets, adminUsers, teamMembersToNotify] = await Promise.all([
        createdEvent.locationId ? getLocationById(createdEvent.locationId, ctx.req.headers) : undefined,
        createdEvent.assetIds && createdEvent.assetIds.length > 0
          ? getAssets(
              {
                objectId: createdEvent.assetIds,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { result: [] },
        getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, userAccountType: USER_ACCOUNT_TYPES.ADMIN }),
        input?.teamMembersToNotify && input?.teamMembersToNotify?.length > 0
          ? getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: input?.teamMembersToNotify })
          : { noResults: true, result: [], nextCursor: undefined },
      ]);

      const usersToNotify = [...adminUsers.result, ...teamMembersToNotify.result, ctx.user];
      const uniqueUsersToNotify = [...new Map(usersToNotify.map((user) => [user.id, user])).values()];

      await sendIncidentCreateNotification({
        event: {
          ...createdEvent,
          location,
          assets: assets.result,
          reportedBy: {
            email: ctx.user.email,
            fullName: ctx.user.fullName,
          },
          incidentUrl: `${env.EHS_URL}${ROUTES.BUILD_INCIDENT_DETAILS_PATH(createdEvent.id)}`,
        },
        toUsers: [
          ...uniqueUsersToNotify
            .filter((user) => user.email && user.fullName)
            .map((user) => ({
              email: user.email!,
              fullName: user.fullName!,
              type: 'to' as const,
            })),
        ],
      });

      return createdEvent;
    }),

  getById: privateProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const event = await getEventById(input.id, ctx.user, ctx.needPartialCheck || false);

      if (!event) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Safety event with ID ${input.id} not found`,
        });
      }

      const [assets, location, reportedByUser, usersToNotify] = await Promise.all([
        event?.assetIds && event?.assetIds.length > 0
          ? getAssets(
              {
                objectId: event.assetIds,
                cursor: 0,
                limit: 100,
              },
              ctx.req.headers,
            )
          : { result: [] },
        event?.locationId ? getLocationById(event?.locationId, ctx.req.headers) : undefined,
        event?.reportedBy ? getUserById(event?.reportedBy, ctx.req.headers) : undefined,
        event?.teamMembersToNotify && event?.teamMembersToNotify?.length > 0
          ? getUsersPublic({
              upkeepCompanyId: event.upkeepCompanyId,
              objectId: event.teamMembersToNotify,
            })
          : { noResults: true, result: [], nextCursor: undefined },
      ]);

      return {
        ...event,
        reportedByUser,
        teamMembersToNotify: usersToNotify.result,
        assets: assets.result,
        location,
      };
    }),

  getByIdForEdit: privateProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT)
    .input(IdSchema)
    .query(async ({ input, ctx }) => {
      const event = await getEventById(input.id, ctx.user, ctx.needPartialCheck || false);

      if (!event) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Safety event with ID ${input.id} not found`,
        });
      }

      return event;
    }),

  list: privateProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW)
    .input(
      ListEventSchema.default({
        cursor: 0,
        limit: 10,
      }),
    )
    .query(async ({ ctx, input }) => {
      const paginatedEvents = await listEvents(input, ctx.user, ctx.needPartialCheck || false);

      const locationIds = Array.from(
        new Set(paginatedEvents.result.map((event) => event.locationId).filter(Boolean)),
      ) as string[];

      const locationsResponse = await getLocations(
        {
          objectId: locationIds,
          cursor: 0,
          limit: 100,
        },
        ctx.req.headers,
      );

      const mappedLocations = locationsResponse.result.reduce(
        (acc, location) => {
          acc[location.id] = location;
          return acc;
        },
        {} as Record<string, Location>,
      );

      const eventsWithLocation = paginatedEvents.result.map((event) => {
        const location = mappedLocations[event.locationId || ''];

        if (!location) {
          return {
            ...event,
            location: undefined,
          };
        }

        return {
          ...event,
          location,
        };
      });

      return {
        ...paginatedEvents,
        result: eventsWithLocation,
      };
    }),

  minimalList: privateProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW)
    .input(
      ListEventSchema.default({
        cursor: 0,
        limit: 10,
      }),
    )
    .query(async ({ ctx, input }) => {
      return await listEvents(input, ctx.user, ctx.needPartialCheck || false);
    }),

  update: privateProcedure
    .hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT)
    .input(EditEventFormSchema)
    .mutation(async ({ input, ctx }) => {
      const updatedEvent = await updateEvent(input.id, input, ctx.user, ctx.needPartialCheck || false);

      if (!updatedEvent) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Failed to update incident',
        });
      }

      const [location, usersToNotify] = await Promise.all([
        updatedEvent.locationId ? getLocationById(updatedEvent.locationId, ctx.req.headers) : undefined,
        updatedEvent?.teamMembersToNotify && updatedEvent?.teamMembersToNotify?.length > 0
          ? getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: updatedEvent.teamMembersToNotify })
          : { noResults: true, result: [], nextCursor: undefined },
      ]);
      await sendIncidentUpdateNotification({
        event: {
          ...updatedEvent,
          location,
          incidentUrl: `${env.EHS_URL}${ROUTES.BUILD_INCIDENT_DETAILS_PATH(updatedEvent.id)}`,
          reportedBy: {
            email: ctx.user.email,
            fullName: ctx.user.fullName,
          },
        },
        toUsers: [
          {
            email: ctx.user.email,
            fullName: ctx.user.fullName,
            type: 'to' as const,
          },
          ...usersToNotify.result
            .filter((user) => user.email && user.fullName)
            .map((user) => ({
              email: user.email!,
              fullName: user.fullName!,
              type: 'to' as const,
            })),
        ],
      });

      return updatedEvent;
    }),
});
