import { ROUTES } from '@shared/ROUTE_PATHS';
import { sendCapaOverdueNotification } from '@server/services/capa-notification.service';
import { getOverdueCapas } from '@server/services/capa.service';
import { searchLocationsPublic } from '@server/services/location.service';
import { getUserPublic, getUsersPublic } from '@server/services/user.service';
import { logger } from '@server/utils/logger';
import { UPKEEP_USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { differenceInDays } from 'date-fns';
import { env } from 'env';

const run = async () => {
  logger.info('Running overdue notification job');

  const capas = await getOverdueCapas();

  // create a map with upkeepCompanyId as key and array of locationIds as value
  const locationMap = new Map<string, string[]>();
  for (const capa of capas) {
    if (!locationMap.has(capa.upkeepCompanyId)) {
      locationMap.set(capa.upkeepCompanyId, []);
    }

    if (capa.locationId) {
      const existingLocations = locationMap.get(capa.upkeepCompanyId) || [];
      locationMap.set(capa.upkeepCompanyId, [...new Set([...existingLocations, capa.locationId])]);
    }
  }

  // Fetch all locations in parallel
  const locationPromises = Array.from(locationMap.entries())
    .filter(([_, locationIds]) => locationIds.length > 0)
    .map(([upkeepCompanyId, locationIds]) =>
      searchLocationsPublic({
        upkeepCompanyId,
        objectId: locationIds,
        search: '',
        limit: locationIds.length,
      }).then((locations) => ({ upkeepCompanyId, locations })),
    );

  const locationResults = await Promise.all(locationPromises);

  // Create a nested lookup map for quick access: upkeepCompanyId -> { locationId -> location }
  const locationLookup = new Map();
  locationResults.forEach(({ upkeepCompanyId, locations }) => {
    const locationsByIdMap = new Map();
    locations.result.forEach((location) => {
      locationsByIdMap.set(location.id, location);
    });
    locationLookup.set(upkeepCompanyId, locationsByIdMap);
  });

  for (const capa of capas) {
    try {
      // calculate days overdue
      let daysOverdue = 0;
      if (capa.dueDate) {
        daysOverdue = differenceInDays(new Date(), capa.dueDate);
      }

      // Get location from nested lookup map
      const location = capa.locationId ? locationLookup.get(capa.upkeepCompanyId)?.get(capa.locationId) : undefined;

      const { upkeepCompanyId } = capa;

      const [admins, owner] = await Promise.all([
        getUsersPublic({ upkeepCompanyId, userAccountType: UPKEEP_USER_ACCOUNT_TYPES.ADMIN }),
        getUserPublic({ upkeepCompanyId, id: capa.ownerId }),
      ]);

      const toAdmins =
        admins?.result?.map((admin) => ({
          email: admin.email ?? '',
          fullName: admin.fullName ?? '',
          type: 'to' as const,
        })) ?? [];

      if (!capa.ownerId || !owner) {
        logger.error('No owner found for CAPA overdue notification', { capaId: capa.id });
        throw new Error(`No owner found for CAPA ${capa.id} overdue notification`);
      }

      const toOwner = [
        {
          email: owner.email ?? '',
          fullName: owner.fullName ?? '',
          type: 'to' as const,
        },
      ];

      // unique emails
      const toNotified = Array.from(new Set([...toAdmins, ...toOwner].map((item) => item)));

      if (toNotified.length === 0) {
        logger.warn('No recipients found for CAPA overdue notification', { capaId: capa.id });
        continue;
      }

      const notifications = [];

      if (toNotified.length > 0) {
        notifications.push(
          sendCapaOverdueNotification({
            capa: {
              ...capa,
              daysOverdue,
              owner,
              location,
              capaUrl: `${env.EHS_URL}${ROUTES.BUILD_CAPA_DETAILS_PATH(capa.id)}`,
            },
            toUsers: toNotified,
          }),
        );
      }

      await Promise.all(notifications);

      logger.info('Sent overdue notification', { capaId: capa.id, recipients: toNotified.map((r) => r.email) });
    } catch (error) {
      logger.error('Failed to send overdue notification', { error, capaId: capa.id });
    }
  }

  logger.info('Overdue notification job completed');
  process.exit(0);
};

run();
