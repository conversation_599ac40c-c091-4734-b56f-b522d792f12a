import { USER_ACCOUNT_TYPES, generatePermissions } from '@shared/user-permissions';

export const mockUser = {
  id: '*********',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
  upkeepCompanyId: '*********',
  groupName: 'Test Group',
  groupId: '*********',
  role: USER_ACCOUNT_TYPES.ADMIN,
  permissions: generatePermissions(USER_ACCOUNT_TYPES.ADMIN),
  featureFlags: { webEHS: true },
  hasEhsEnabled: true,
};

export const mockTechnicianUser = {
  id: '*********',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
  upkeepCompanyId: '*********',
  groupName: 'Test Group',
  groupId: '*********',
  role: USER_ACCOUNT_TYPES.TECHNICIAN,
  permissions: generatePermissions(USER_ACCOUNT_TYPES.TECHNICIAN),
  featureFlags: { webEHS: true },
  hasEhsEnabled: true,
};

export const mockPublicUser = {
  id: '*********',
  username: 'testuser',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  fullName: 'Test User',
};
