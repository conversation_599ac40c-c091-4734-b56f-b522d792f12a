import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { getUsersPublic, hasPermission } from '@server/services/user.service';
import { auditTrail, globalLocations } from '@shared/schema';
import { globalLocationRouter } from '@server/trpc/router/global-location.router';
import { eq } from 'drizzle-orm';
import { TRPCError } from '@trpc/server';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUsersPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
}));

describe('globalLocationRouter', () => {
  const mockContext = createMockContext(mockUser);
  const caller = globalLocationRouter.createCaller(mockContext);
  const mockPublicUserData = {
    id: mockUser.id,
    fullName: mockUser.fullName ?? 'Test User',
    username: mockUser.username ?? 'testuser',
    firstName: mockUser.firstName ?? 'Test',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(hasPermission).mockReturnValue(true);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [mockPublicUserData],
      nextCursor: undefined,
    });
  });

  afterEach(async () => {
    await db.delete(globalLocations);
    await db.delete(auditTrail);
  });

  describe('create', () => {
    it('should create a new global location', async () => {
      const input = { name: 'Test Location', description: 'A test description' };
      const result = await caller.create(input);

      expect(result).toBeDefined();
      expect(result.name).toBe(input.name);

      const dbLocation = await db.query.globalLocations.findFirst({
        where: eq(globalLocations.id, result.id),
      });

      expect(dbLocation).toBeDefined();
      expect(dbLocation?.name).toBe(input.name);
      expect(dbLocation?.createdBy).toBe(mockUser.id);
    });
  });

  describe('list', () => {
    it('should list global locations and enrich with creator info', async () => {
      const locationData = {
        name: 'List Test Location',
        upkeepCompanyId: mockUser.upkeepCompanyId,
        createdBy: mockUser.id,
      };
      await db.insert(globalLocations).values(locationData);

      const listResult = await caller.list({ cursor: 0, limit: 10 });

      expect(listResult.result.length).toBe(1);
      expect(listResult.result[0].name).toBe(locationData.name);
      expect(listResult.result[0].createdBy).toBeDefined();
      expect(listResult.result[0].createdBy?.id).toBe(mockUser.id);
      expect(listResult.result[0].createdBy?.fullName).toBe(mockPublicUserData.fullName);
    });

    it('should return an empty list when no locations exist', async () => {
      const listResult = await caller.list({ cursor: 0, limit: 10 });
      expect(listResult.result.length).toBe(0);
    });
  });

  describe('toggleArchive', () => {
    it('should archive a global location', async () => {
      const [location] = await db
        .insert(globalLocations)
        .values({
          name: 'Archive Test Location',
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id,
        })
        .returning();

      const result = await caller.toggleArchive({ id: location.id });
      expect(result.archivedAt).not.toBeNull();

      const dbLocation = await db.query.globalLocations.findFirst({
        where: eq(globalLocations.id, location.id),
      });
      expect(dbLocation?.archivedAt).not.toBeNull();
    });

    it('should unarchive a global location', async () => {
      const [location] = await db
        .insert(globalLocations)
        .values({
          name: 'Unarchive Test Location',
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id,
          archivedAt: new Date(),
        })
        .returning();

      const result = await caller.toggleArchive({ id: location.id });
      expect(result.archivedAt).toBeNull();

      const dbLocation = await db.query.globalLocations.findFirst({
        where: eq(globalLocations.id, location.id),
      });
      expect(dbLocation?.archivedAt).toBeNull();
    });

    it('should throw an error when trying to archive a non-existent location', async () => {
      await expect(caller.toggleArchive({ id: 'non-existent-id' })).rejects.toThrowError(
        new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to archive global location' }),
      );
    });
  });
});
