import { describe, it, expect, beforeEach, afterEach, vi, afterAll, beforeAll } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';
import { events, oshaAuditTrail, oshaReports, shiftsEnum, typeOfMedicalCareEnum } from '@shared/schema';
import { oshaReportRouter } from '@server/trpc/router/osha-report.router';
import { eq } from 'drizzle-orm';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

let eventId: string;

describe('oshaReportRouter', () => {
  beforeAll(async () => {
    const event = await db
      .insert(events)
      .values({
        upkeepCompanyId: '123',
        title: 'Test incident',
        description: 'Test description',
        type: 'incident',
        category: 'other',
        severity: 'low',
        reportedAt: new Date(),
        locationId: '123',
        assetIds: ['123'],
        status: 'open',
      })
      .returning({ id: events.id });
    eventId = event.at(0)!.id;
  });

  const mockContext = createMockContext(mockUser);
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(oshaAuditTrail);
    await db.delete(oshaReports);
  });

  afterAll(async () => {
    await db.delete(events);
  });

  describe('create', () => {
    it('should not allow employeeName to be empty when privacyCase is false', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const mockOshaReportInput = {
        eventId,
        privacyCase: false,
        employeeName: '',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.create(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should not allow reasonForPrivacyCase to be empty when privacyCase is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const mockOshaReportInput = {
        eventId,
        privacyCase: true,
        reasonForPrivacyCase: '',
        employeeName: '',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.create(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should require daysAwayFromWork when wasHospitalized is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const mockOshaReportInput = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: true,
        daysAwayFromWork: 0,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.create(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should require daysRestrictedFromWork when wasHospitalized is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const mockOshaReportInput = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: true,
        daysRestrictedFromWork: 0,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.create(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should require daysAwayFromWork when wasDeceased is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const mockOshaReportInput = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasDeceased: true,
        daysAwayFromWork: 0,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.create(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should require daysRestrictedFromWork when wasDeceased is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const mockOshaReportInput = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasDeceased: true,
        daysRestrictedFromWork: 0,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.create(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should create a new OSHA report with slug generated and an audit trail', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const mockOshaReportInput = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };
      const report = await caller.create(mockOshaReportInput);
      expect(report).toBeDefined();

      const auditTrail = await db.select().from(oshaAuditTrail).where(eq(oshaAuditTrail.entityId, report.id)).limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('created');

      const createdReport = await db.select().from(oshaReports).where(eq(oshaReports.id, report.id)).limit(1);
      expect(createdReport).toBeDefined();
      expect(createdReport.length).toBe(1);
      expect(createdReport[0].id).toBe(report.id);
      expect(createdReport[0].eventId).toBe(eventId);
      expect(createdReport[0].employeeName).toBe(mockOshaReportInput.employeeName);
      expect(createdReport[0].employeeDepartment).toBe(mockOshaReportInput.employeeDepartment);
      expect(createdReport[0].employeeJobTitle).toBe(mockOshaReportInput.employeeJobTitle);
      expect(createdReport[0].slug).toBeDefined();
    });

    it('should create a new OSHA report with privacy case true and employeeName empty with slug generated and an audit trail', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const mockOshaReportInput = {
        eventId,
        privacyCase: true,
        reasonForPrivacyCase: 'Test reason',
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };
      const report = await caller.create(mockOshaReportInput);
      expect(report).toBeDefined();

      const auditTrail = await db.select().from(oshaAuditTrail).where(eq(oshaAuditTrail.entityId, report.id)).limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('created');

      const createdReport = await db.select().from(oshaReports).where(eq(oshaReports.id, report.id)).limit(1);
      expect(createdReport).toBeDefined();
      expect(createdReport.length).toBe(1);
      expect(createdReport[0].id).toBe(report.id);
      expect(createdReport[0].eventId).toBe(eventId);
      expect(createdReport[0].employeeName).toBeNull();
      expect(createdReport[0].employeeDepartment).toBe(mockOshaReportInput.employeeDepartment);
      expect(createdReport[0].employeeJobTitle).toBe(mockOshaReportInput.employeeJobTitle);
      expect(createdReport[0].slug).toBeDefined();
    });
  });

  describe('update', () => {
    it('should not allow employeeName to be empty when privacyCase is false', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const mockOshaReportInput = {
        id: '123',
        privacyCase: false,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.update(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should not allow reasonForPrivacyCase to be empty when privacyCase is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const mockOshaReportInput = {
        id: '123',
        privacyCase: true,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.update(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should require daysAwayFromWork when updating wasHospitalized to true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const report = await db
        .insert(oshaReports)
        .values({
          eventId,
          privacyCase: false,
          employeeName: 'John Doe',
          employeeWorkLocation: 'Main Office',
          employeeDepartment: 'IT',
          employeeJobTitle: 'Software Engineer',
          employeeDateOfHire: new Date(),
          employeeShift: shiftsEnum.enumValues[0],
          bodyPartInjured: 'head',
          typeOfInjury: 'cut',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
          wasHospitalized: false,
          reportedBy: 'John Doe',
          preparedByUserId: '123',
          reasonForReport: 'some reason here',
          primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
          upkeepCompanyId: '123',
          createdBy: '123',
        })
        .returning({ id: oshaReports.id });

      const mockOshaReportInput = {
        id: report[0].id,
        wasHospitalized: true,
        daysAwayFromWork: 0,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.update(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should require daysRestrictedFromWork when updating wasHospitalized to true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const report = await db
        .insert(oshaReports)
        .values({
          eventId,
          privacyCase: false,
          employeeName: 'John Doe',
          employeeWorkLocation: 'Main Office',
          employeeDepartment: 'IT',
          employeeJobTitle: 'Software Engineer',
          employeeDateOfHire: new Date(),
          employeeShift: shiftsEnum.enumValues[0],
          bodyPartInjured: 'head',
          typeOfInjury: 'cut',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
          wasHospitalized: false,
          reportedBy: 'John Doe',
          preparedByUserId: '123',
          reasonForReport: 'some reason here',
          primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
          upkeepCompanyId: '123',
          createdBy: '123',
        })
        .returning({ id: oshaReports.id });

      const mockOshaReportInput = {
        id: report[0].id,
        wasHospitalized: true,
        daysRestrictedFromWork: 0,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.update(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should require daysAwayFromWork when updating wasDeceased to true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const report = await db
        .insert(oshaReports)
        .values({
          eventId,
          privacyCase: false,
          employeeName: 'John Doe',
          employeeWorkLocation: 'Main Office',
          employeeDepartment: 'IT',
          employeeJobTitle: 'Software Engineer',
          employeeDateOfHire: new Date(),
          employeeShift: shiftsEnum.enumValues[0],
          bodyPartInjured: 'head',
          typeOfInjury: 'cut',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
          wasHospitalized: false,
          reportedBy: 'John Doe',
          preparedByUserId: '123',
          reasonForReport: 'some reason here',
          primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
          upkeepCompanyId: '123',
          createdBy: '123',
        })
        .returning({ id: oshaReports.id });

      const mockOshaReportInput = {
        id: report[0].id,
        wasDeceased: true,
        daysAwayFromWork: 0,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.update(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should require daysRestrictedFromWork when updating wasDeceased to true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const report = await db
        .insert(oshaReports)
        .values({
          eventId,
          privacyCase: false,
          employeeName: 'John Doe',
          employeeWorkLocation: 'Main Office',
          employeeDepartment: 'IT',
          employeeJobTitle: 'Software Engineer',
          employeeDateOfHire: new Date(),
          employeeShift: shiftsEnum.enumValues[0],
          bodyPartInjured: 'head',
          typeOfInjury: 'cut',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
          wasHospitalized: false,
          reportedBy: 'John Doe',
          preparedByUserId: '123',
          reasonForReport: 'some reason here',
          primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
          upkeepCompanyId: '123',
          createdBy: '123',
        })
        .returning({ id: oshaReports.id });

      const mockOshaReportInput = {
        id: report[0].id,
        wasDeceased: true,
        daysRestrictedFromWork: 0,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await expect(async () => await caller.update(mockOshaReportInput)).rejects.toThrowError();
    });

    it('should update an OSHA report and create an audit trail', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const report = await db
        .insert(oshaReports)
        .values({
          eventId,
          privacyCase: false,
          employeeName: 'John Doe',
          employeeWorkLocation: 'Main Office',
          employeeDepartment: 'IT',
          employeeJobTitle: 'Software Engineer',
          employeeDateOfHire: new Date(),
          employeeShift: shiftsEnum.enumValues[0],
          bodyPartInjured: 'head',
          typeOfInjury: 'cut',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
          wasHospitalized: false,
          reportedBy: 'John Doe',
          preparedByUserId: '123',
          reasonForReport: 'some reason here',
          primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
          upkeepCompanyId: '123',
          createdBy: '123',
        })
        .returning({ id: oshaReports.id });

      const mockOshaReportInput = {
        id: report[0].id,
        employeeJobTitle: 'HR Manager',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      const updatedReport = await caller.update(mockOshaReportInput);
      expect(updatedReport).toBeDefined();

      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail.entityId, updatedReport.id))
        .limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('updated');

      const loadedReport = await db.select().from(oshaReports).where(eq(oshaReports.id, updatedReport.id)).limit(1);
      expect(loadedReport).toBeDefined();
      expect(loadedReport.length).toBe(1);
      expect(loadedReport[0].id).toBe(updatedReport.id);
      expect(loadedReport[0].employeeJobTitle).toBe(mockOshaReportInput.employeeJobTitle);
    });

    it('should set employeeName to null when privacyCase is true', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      const report = await db
        .insert(oshaReports)
        .values({
          eventId,
          privacyCase: false,
          employeeName: 'John Doe',
          employeeWorkLocation: 'Main Office',
          employeeDepartment: 'IT',
          employeeJobTitle: 'Software Engineer',
          employeeDateOfHire: new Date(),
          employeeShift: shiftsEnum.enumValues[0],
          bodyPartInjured: 'head',
          typeOfInjury: 'cut',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
          wasHospitalized: false,
          reportedBy: 'John Doe',
          preparedByUserId: '123',
          reasonForReport: 'some reason here',
          primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
          upkeepCompanyId: '123',
          createdBy: '123',
        })
        .returning({ id: oshaReports.id });

      const mockOshaReportInput = {
        id: report[0].id,
        privacyCase: true,
        reasonForPrivacyCase: 'Test reason',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      const updatedReport = await caller.update(mockOshaReportInput);

      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail.entityId, updatedReport.id))
        .limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);

      expect(auditTrail[0].action).toBe('updated');

      const loadedReport = await db.select().from(oshaReports).where(eq(oshaReports.id, updatedReport.id)).limit(1);
      expect(loadedReport).toBeDefined();
      expect(loadedReport.length).toBe(1);
      expect(loadedReport[0].id).toBe(updatedReport.id);
      expect(loadedReport[0].employeeName).toBeNull();
    });
  });

  describe('getById', () => {
    it('should fetch an OSHA report by ID with related event data', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // First create an OSHA report
      const mockOshaReportInput = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      const createdReport = await caller.create(mockOshaReportInput);

      // Now fetch it by ID
      const fetchedReport = await caller.getById({ id: createdReport.id });

      expect(fetchedReport).toBeDefined();
      expect(fetchedReport.id).toBe(createdReport.id);
      expect(fetchedReport.employeeName).toBe(mockOshaReportInput.employeeName);
      expect(fetchedReport.employeeDepartment).toBe(mockOshaReportInput.employeeDepartment);
      expect(fetchedReport.employeeJobTitle).toBe(mockOshaReportInput.employeeJobTitle);

      // Check that event data is included
      expect(fetchedReport.event).toBeDefined();
      expect(fetchedReport.event?.id).toBe(eventId);
      expect(fetchedReport.event?.title).toBe('Test incident');

      // Check that audit trail is included
      expect(fetchedReport.auditTrail).toBeDefined();
      expect(Array.isArray(fetchedReport.auditTrail)).toBe(true);
      expect(fetchedReport.auditTrail.length).toBeGreaterThan(0);
      expect(fetchedReport.auditTrail[0].action).toBe('created');
    });

    it('should return 404 for non-existent OSHA report', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);
      const nonExistentId = 'non-existent-id';

      await expect(async () => await caller.getById({ id: nonExistentId })).rejects.toThrowError();
    });

    it('should handle privacy case data correctly when fetching by ID', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // Create a privacy case OSHA report
      const mockOshaReportInput = {
        eventId,
        privacyCase: true,
        reasonForPrivacyCase: 'Employee requested privacy',
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      const createdReport = await caller.create(mockOshaReportInput);
      const fetchedReport = await caller.getById({ id: createdReport.id });

      expect(fetchedReport).toBeDefined();
      expect(fetchedReport.privacyCase).toBe(true);
      expect(fetchedReport.reasonForPrivacyCase).toBe('Employee requested privacy');
      expect(fetchedReport.employeeName).toBeNull(); // Should be null for privacy cases
    });

    it('should always mask employeeName when privacyCase is true, even if stored in database', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // First create a regular OSHA report with employeeName
      const mockOshaReportInput = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      const createdReport = await caller.create(mockOshaReportInput);

      // Manually update the database to set privacyCase to true while keeping employeeName
      // This simulates a scenario where data might exist in the database but should be masked
      await db
        .update(oshaReports)
        .set({
          privacyCase: true,
          reasonForPrivacyCase: 'Privacy protection required',
          updatedAt: new Date(),
        })
        .where(eq(oshaReports.id, createdReport.id));

      // Fetch the report - employeeName should be masked despite being in the database
      const fetchedReport = await caller.getById({ id: createdReport.id });

      expect(fetchedReport).toBeDefined();
      expect(fetchedReport.privacyCase).toBe(true);
      expect(fetchedReport.reasonForPrivacyCase).toBe('Privacy protection required');
      expect(fetchedReport.employeeName).toBeNull(); // Should be masked even if stored in DB
    });
  });

  describe('list', () => {
    it('should list OSHA reports with pagination', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // First create a few OSHA reports
      const mockOshaReportInput1 = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      const mockOshaReportInput2 = {
        ...mockOshaReportInput1,
        employeeName: 'Jane Smith',
        employeeJobTitle: 'HR Manager',
        privacyCase: true,
        reasonForPrivacyCase: 'Employee requested privacy',
      };

      await caller.create(mockOshaReportInput1);
      await caller.create(mockOshaReportInput2);

      // Now fetch the list
      const result = await caller.list({
        cursor: 0,
        limit: 10,
      });

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(2);

      // Find the privacy case and regular case
      const privacyCase = result.result.find((r) => r.privacyCase === true);
      const regularCase = result.result.find((r) => r.privacyCase === false);

      expect(privacyCase).toBeDefined();
      expect(regularCase).toBeDefined();
      expect(privacyCase!.employeeName).toBe('Privacy Case'); // Privacy case should be masked
      expect(regularCase!.employeeName).toBeDefined(); // Regular case should have name
    });

    it('should filter by case type', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // Create a death case
      const deathCase = {
        eventId,
        privacyCase: false,
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'fatal injury',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasDeceased: true,
        daysAwayFromWork: 1,
        daysRestrictedFromWork: 1,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'fatality' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await caller.create(deathCase);

      // Filter by death cases
      const result = await caller.list({
        cursor: 0,
        limit: 10,
        caseType: 'death',
      });

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(1);
      expect(result.result[0].wasDeceased).toBe(true);
    });

    it('should handle privacy case filtering', async () => {
      const caller = oshaReportRouter.createCaller(mockContext);

      // Create a privacy case
      const privacyCase = {
        eventId,
        privacyCase: true,
        reasonForPrivacyCase: 'Employee requested privacy',
        employeeName: 'John Doe',
        employeeWorkLocation: 'Main Office',
        employeeDepartment: 'IT',
        employeeJobTitle: 'Software Engineer',
        employeeDateOfHire: new Date(),
        employeeShift: shiftsEnum.enumValues[0],
        bodyPartInjured: 'head',
        typeOfInjury: 'cut',
        treatmentLocation: 'hospital',
        typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
        wasHospitalized: false,
        reportedBy: 'John Doe',
        preparedByUserId: '123',
        reasonForReport: 'some reason here',
        primaryRecordableOutcome: 'medical_treatment_beyond_first_aid' as const,
        ipAddress: '127.0.0.1',
        userAgent: 'Test Browser',
      };

      await caller.create(privacyCase);

      // Filter out privacy cases
      const result = await caller.list({
        cursor: 0,
        limit: 10,
        showPrivacyCases: false,
      });

      expect(result).toBeDefined();
      expect(result.result).toHaveLength(0); // Should not include privacy cases
    });
  });
});
