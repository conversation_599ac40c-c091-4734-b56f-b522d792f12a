import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, getUsersPublic, hasPermission } from '@server/services/user.service';
import { files, events } from '@shared/schema';
import { incidentRouter } from '@server/trpc/router/incident.router';
import { getLocations } from '@server/services/location.service';
import { getAssets } from '@server/services/asset.service';

const mockIncidentInput = {
  title: 'Test incident',
  description: 'Test description',
  type: 'incident' as const,
  category: 'other' as const,
  severity: 'low' as const,
  reportedAt: new Date(),
  locationId: '123',
  assetIds: ['123'],
};

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  getUsersPublic: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

vi.mock('@server/services/email/incident-notification.service', () => ({
  handleIncidentNotifications: vi.fn(),
  handleEventPublicNotifications: vi.fn(),
  sendEventSubmittedNotification: vi.fn(),
}));

vi.mock('@server/services/location.service', () => ({
  getLocationById: vi.fn().mockResolvedValue({ id: '123', name: 'Test Location' }),
  getLocations: vi.fn().mockResolvedValue([{ id: '123', name: 'Test Location' }]),
}));

vi.mock('@server/services/asset.service', () => ({
  getAssets: vi.fn().mockResolvedValue({
    noResults: false,
    result: [{ id: '123', name: 'Test Asset', description: 'Test Asset Description' }],
    nextCursor: undefined,
  }),
}));

describe('incident router', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(getUsersPublic).mockResolvedValue({
      noResults: false,
      result: [
        {
          id: mockUser.id,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          fullName: mockUser.fullName,
          username: mockUser.email,
          email: mockUser.email,
        },
      ],
      nextCursor: undefined,
    });
    vi.mocked(hasPermission).mockReturnValue(true);
    vi.mocked(getLocations).mockResolvedValue({
      noResults: false,
      result: [{ id: '123', name: 'Test Location' }],
      nextCursor: undefined,
    });
    vi.mocked(getAssets).mockResolvedValue({
      noResults: false,
      result: [{ id: '123', name: 'Test Asset', description: 'Test Asset Description' }],
      nextCursor: undefined,
    });
  });

  afterEach(async () => {
    await db.delete(files);
    await db.delete(events);
  });

  describe('create', () => {
    it('should create a new incident', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      const incident = await caller.create(mockIncidentInput);

      expect(incident).toBeDefined();
      expect(incident?.title).toBe(mockIncidentInput.title);
      expect(incident?.description).toBe(mockIncidentInput.description);
    });
  });

  describe('getById', () => {
    it('should get an incident by id without files', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      const incident = await caller.create(mockIncidentInput);
      const incidentById = await caller.getById({ id: incident?.id! });

      expect(incidentById).toBeDefined();
      expect(incidentById?.title).toBe(mockIncidentInput.title);
      expect(incidentById?.description).toBe(mockIncidentInput.description);
      expect(incidentById?.media).toBeNull();
    });

    it('should get an incident by id with files', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      const incident = await caller.create(mockIncidentInput);

      await db.insert(files).values({
        id: 'test-file-id',
        upkeepCompanyId: mockUser.upkeepCompanyId,
        fileName: 'test.pdf',
        fileSize: 1024,
        mimeType: 'application/pdf',
        presignedUrl: 'https://test-bucket.s3.amazonaws.com/test.pdf',
        s3Key: 'test/test.pdf',
        s3Bucket: 'test-bucket',
        status: 'completed',
        entityType: 'event',
        entityId: incident?.id!,
        uploadedBy: mockUser.id,
        expiresAt: new Date('2024-12-31'),
      });

      const incidentById = await caller.getById({ id: incident?.id! });

      expect(incidentById).toBeDefined();
      expect(incidentById?.media).toBeDefined();
      expect(incidentById?.media?.length).toBe(1);
      expect(incidentById?.media?.[0]?.name).toBe('test.pdf');
      expect(incidentById?.media?.[0]?.url).toBe('https://test-bucket.s3.amazonaws.com/test.pdf');
      expect(incidentById?.media?.[0]?.type).toBe('application/pdf');
      expect(incidentById?.media?.[0]?.size).toBe(1024);
    });
  });

  describe('list', () => {
    it('should list incidents', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      await caller.create(mockIncidentInput);
      const incidents = await caller.list({});

      expect(incidents).toBeDefined();
      expect(incidents.result.length).toBe(1);
      expect(incidents.result[0].title).toBe(mockIncidentInput.title);
      expect(incidents.result[0].slug).toBeDefined();
    });

    it('should prioritize mustIncludeObjectIds in results', async () => {
      const caller = incidentRouter.createCaller(mockContext);

      // Create 3 incidents with different dates
      const incident1 = await caller.create({
        ...mockIncidentInput,
        title: 'Recent',
        reportedAt: new Date('2024-03-15'),
      });
      const incident2 = await caller.create({
        ...mockIncidentInput,
        title: 'Must Include',
        reportedAt: new Date('2024-02-15'),
      });
      const incident3 = await caller.create({ ...mockIncidentInput, title: 'Old', reportedAt: new Date('2024-01-15') });

      // List with mustIncludeObjectIds, limit 2, and sorted by reportedAt desc
      const incidents = await caller.list({
        limit: 2,
        mustIncludeObjectIds: [incident2?.id!],
        sortBy: 'reportedAt',
        sortOrder: 'desc',
      });

      expect(incidents.result.length).toBe(2);
      // Must include incident should be first, despite being older
      expect(incidents.result[0].id).toBe(incident2?.id);
      expect(incidents.result[0].title).toBe('Must Include');
      // Most recent incident should be second
      expect(incidents.result[1].id).toBe(incident1?.id);
      expect(incidents.result[1].title).toBe('Recent');
      // Oldest incident should be excluded due to limit
      expect(incidents.result.find((i) => i.id === incident3?.id)).toBeUndefined();
    });
  });

  describe('update', () => {
    it('should update an incident', async () => {
      const caller = incidentRouter.createCaller(mockContext);
      const incident = await caller.create(mockIncidentInput);
      const updatedIncident = await caller.update({ id: incident?.id!, title: 'Updated title' });

      expect(updatedIncident).toBeDefined();
      expect(updatedIncident?.title).toBe('Updated title');
    });
  });
});
