import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { fileRouter } from '@server/trpc/router/file.router';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { files } from '@shared/schema';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';
import { eq } from 'drizzle-orm';

// Mock user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn(),
}));

// Mock AWS SDK
vi.mock('@aws-sdk/client-s3', () => ({
  S3Client: vi.fn().mockImplementation(() => ({
    send: vi.fn().mockResolvedValue({}),
  })),
  PutObjectCommand: vi.fn().mockImplementation((input) => ({ input })),
  GetObjectCommand: vi.fn().mockImplementation((input) => ({ input })),
  DeleteObjectsCommand: vi.fn().mockImplementation((input) => ({ input })),
}));

vi.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: vi.fn().mockResolvedValue('https://test-presigned-url.com?aws-signature-version=4'),
}));

// Mock environment variables
vi.mock('@server/env', () => ({
  env: {
    AWS_REGION: 'us-east-1',
    AWS_ACCESS_KEY_ID: 'test-key',
    AWS_SECRET_ACCESS_KEY: 'test-secret',
    S3_BUCKET_NAME: 'test-bucket',
  },
}));

describe('fileRouter', () => {
  const mockContext = createMockContext(mockUser) as any;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(files);
  });

  describe('getPresignedUrl', () => {
    it('should return a presigned url and create a file record', async () => {
      const mockFileInput = {
        fileName: 'test.pdf',
        fileSize: 1000,
        mimeType: 'application/pdf',
        entityType: 'accessPoint',
        entityId: '123',
      };

      const caller = fileRouter.createCaller(mockContext);
      const result = await caller.getPresignedUrl(mockFileInput);

      expect(result).toBeDefined();
      expect(result.presignedUrl).toBeDefined();
      expect(result.file).toBeDefined();
      expect(result.file?.presignedUrl).toContain('https://test-presigned-url.com');
    });
  });

  describe('removeFiles', () => {
    it('should remove files and delete from S3', async () => {
      const [mockFile] = await db
        .insert(files)
        .values({
          upkeepCompanyId: 'company-id',
          fileName: 'test.pdf',
          fileSize: 1000,
          mimeType: 'application/pdf',
          s3Key: 'test/key.pdf',
          status: 'completed',
          presignedUrl: 'https://test-presigned-url.com',
          s3Bucket: 'test-bucket',
          expiresAt: new Date(),
          uploadedBy: 'user-id',
          entityType: 'accessPoint',
          entityId: '123',
        })
        .returning();

      const caller = fileRouter.createCaller(mockContext);
      const result = await caller.removeFiles([mockFile.id]);

      expect(result).toBeDefined();

      const file = await db.query.files.findFirst({
        where: eq(files.id, mockFile.id),
      });

      expect(file).toBeUndefined();
    });
  });
});
