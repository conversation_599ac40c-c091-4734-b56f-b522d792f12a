import { describe, it, expect, beforeEach, afterEach, vi, afterAll, beforeAll } from 'vitest';
import { createMockContext } from '@server/trpc/router/__tests__/test-utils';
import { db } from '@server/db';
import { mockUser } from '@server/test/fixtures/user';
import { getUserById, getUsers, hasPermission } from '@server/services/user.service';
import { events, oshaAuditTrail, oshaCompanyInformation, oshaReports } from '@shared/schema';
import { oshaSummaryRouter } from '@server/trpc/router/osha-summary.router';
import { eq, desc } from 'drizzle-orm';
import { createId } from '@paralleldrive/cuid2';

// Mock the user service
vi.mock('@server/services/user.service', () => ({
  getUserById: vi.fn(),
  getUsers: vi.fn(),
  hasPermission: vi.fn().mockReturnValue(true),
  userHasIncidentPermission: vi.fn().mockReturnValue(true),
  canEditIncident: vi.fn().mockReturnValue(true),
  canExportIncident: vi.fn().mockReturnValue(true),
}));

let eventId: string;

describe('oshaSummaryRouter', () => {
  beforeAll(async () => {
    const event = await db
      .insert(events)
      .values({
        upkeepCompanyId: '123',
        title: 'Test incident',
        description: 'Test description',
        type: 'incident',
        category: 'other',
        severity: 'low',
        reportedAt: new Date(),
        locationId: '123',
        assetIds: ['123'],
        status: 'open',
      })
      .returning({ id: events.id });
    eventId = event.at(0)!.id;
  });

  const mockContext = createMockContext(mockUser);
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(getUserById).mockResolvedValue(mockUser);
    vi.mocked(getUsers).mockResolvedValue([mockUser]);
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  afterEach(async () => {
    await db.delete(oshaAuditTrail);
    await db.delete(oshaCompanyInformation);
  });

  afterAll(async () => {
    await db.delete(oshaReports);
    await db.delete(events);
    await db.delete(oshaAuditTrail);
    await db.delete(oshaCompanyInformation);
  });

  describe('upsertEstablishInformation', () => {
    it('should update an existing establishment information', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);

      const mockOshaSummaryInput = {
        companyName: 'Test company',
        companyFacilityId: '123',
        companyNAICSCode: 123456,
        companyEIN: '*********',
        year: 2025,
        companyAnnualAverageNumberOfEmployees: 100,
        companyTotalHoursWorked: 10000,
      };

      const inserted = await db
        .insert(oshaCompanyInformation)
        .values({
          ...mockOshaSummaryInput,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id!,
        })
        .returning({ id: oshaCompanyInformation.id });

      const summary = await caller.upsertEstablishmentInformation({
        ...mockOshaSummaryInput,
        companyName: 'test company updated',
      });
      expect(summary?.id).toBe(inserted.at(0)?.id);

      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail?.entityId, summary?.id ?? ''))
        .limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('updated');

      const summaryData = await db
        .select()
        .from(oshaCompanyInformation)
        .where(eq(oshaCompanyInformation?.id, summary?.id ?? ''))
        .limit(1);
      expect(summaryData).toBeDefined();
      expect(summaryData.length).toBe(1);
      expect(summaryData[0].companyName).toBe('test company updated');
      expect(summaryData[0].companyFacilityId).toBe(mockOshaSummaryInput.companyFacilityId);
      expect(summaryData[0].companyNAICSCode).toBe(mockOshaSummaryInput.companyNAICSCode);
      expect(summaryData[0].companyEIN).toBe(mockOshaSummaryInput.companyEIN);
    });

    it('should create a new establishment information', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);

      const mockOshaSummaryInput = {
        companyName: 'Test company',
        companyFacilityId: '123',
        companyNAICSCode: 123456,
        companyEIN: '*********',
        year: 2025,
        companyAnnualAverageNumberOfEmployees: 100,
        companyTotalHoursWorked: 10000,
      };
      const summary = await caller.upsertEstablishmentInformation(mockOshaSummaryInput);
      expect(summary?.id).toBeDefined();

      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail?.entityId, summary?.id ?? ''))
        .limit(1);
      expect(auditTrail).toBeDefined();
      expect(auditTrail.length).toBe(1);
      expect(auditTrail[0].action).toBe('created');

      const summaryData = await db
        .select()
        .from(oshaCompanyInformation)
        .where(eq(oshaCompanyInformation?.id, summary?.id ?? ''))
        .limit(1);
      expect(summaryData).toBeDefined();
      expect(summaryData.length).toBe(1);
      expect(summaryData[0].companyName).toBe(mockOshaSummaryInput.companyName);
      expect(summaryData[0].companyFacilityId).toBe(mockOshaSummaryInput.companyFacilityId);
      expect(summaryData[0].companyNAICSCode).toBe(mockOshaSummaryInput.companyNAICSCode);
      expect(summaryData[0].companyEIN).toBe(mockOshaSummaryInput.companyEIN);
    });
  });

  describe('upsertEstablishInformation - Validation Tests', () => {
    const validInput = {
      companyName: 'Test Company',
      companyFacilityId: 'FAC-123',
      companyNAICSCode: 123456,
      companyEIN: '*********',
      year: 2025,
      companyAnnualAverageNumberOfEmployees: 100,
      companyTotalHoursWorked: 10000,
    };

    describe('Required Fields Validation', () => {
      it('should fail when companyName is empty', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyName: '',
          }),
        ).rejects.toThrow();
      });

      it('should fail when companyFacilityId is empty', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyFacilityId: '',
          }),
        ).rejects.toThrow();
      });
    });

    describe('EIN Validation', () => {
      it('should fail when EIN is not exactly 9 digits', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyEIN: '12345678', // 8 digits
          }),
        ).rejects.toThrow();
      });

      it('should fail when EIN contains non-numeric characters', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyEIN: '12345678a', // contains letter
          }),
        ).rejects.toThrow();
      });

      it('should fail when EIN is too long', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyEIN: '*********0', // 10 digits
          }),
        ).rejects.toThrow();
      });

      it('should accept valid EIN with leading zeros', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        const result = await caller.upsertEstablishmentInformation({
          ...validInput,
          companyEIN: '*********', // leading zeros
        });

        expect(result).toBeDefined();

        const summary = await db
          .select()
          .from(oshaCompanyInformation)
          .where(eq(oshaCompanyInformation?.id, result?.id ?? ''))
          .limit(1);

        expect(summary[0].companyEIN).toBe('*********');
      });
    });

    describe('Employee Count Validation', () => {
      it('should fail when employee count is zero', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyAnnualAverageNumberOfEmployees: 0,
          }),
        ).rejects.toThrow();
      });

      it('should fail when employee count is negative', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyAnnualAverageNumberOfEmployees: -1,
          }),
        ).rejects.toThrow();
      });

      it('should accept positive employee count', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        const result = await caller.upsertEstablishmentInformation({
          ...validInput,
          companyAnnualAverageNumberOfEmployees: 1,
        });

        expect(result).toBeDefined();
      });
    });

    describe('Total Hours Worked Validation', () => {
      it('should fail when total hours is zero', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyTotalHoursWorked: 0,
          }),
        ).rejects.toThrow();
      });

      it('should fail when total hours is negative', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyTotalHoursWorked: -1,
          }),
        ).rejects.toThrow();
      });

      it('should accept positive total hours', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        const result = await caller.upsertEstablishmentInformation({
          ...validInput,
          companyTotalHoursWorked: 1,
        });

        expect(result).toBeDefined();
      });
    });

    describe('NAICS Code Validation', () => {
      it('should fail when NAICS code is too small', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyNAICSCode: 9, // less than 10
          }),
        ).rejects.toThrow();
      });

      it('should fail when NAICS code is too large', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyNAICSCode: 1000000, // greater than 999999
          }),
        ).rejects.toThrow();
      });

      it('should fail when NAICS code is not positive', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            companyNAICSCode: -1,
          }),
        ).rejects.toThrow();
      });

      it('should accept valid NAICS code range', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        const result = await caller.upsertEstablishmentInformation({
          ...validInput,
          companyNAICSCode: 10,
        });

        expect(result).toBeDefined();

        const result2 = await caller.upsertEstablishmentInformation({
          ...validInput,
          companyNAICSCode: 999999,
        });

        expect(result2).toBeDefined();
      });
    });

    describe('Year Validation', () => {
      it('should fail when year is too old', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            year: 1899,
          }),
        ).rejects.toThrow();
      });

      it('should fail when year is too far in the future', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        const currentYear = new Date().getFullYear();
        await expect(
          caller.upsertEstablishmentInformation({
            ...validInput,
            year: currentYear + 2,
          }),
        ).rejects.toThrow();
      });

      it('should accept current year', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        const currentYear = new Date().getFullYear();
        const result = await caller.upsertEstablishmentInformation({
          ...validInput,
          year: currentYear,
        });

        expect(result).toBeDefined();
      });

      it('should accept next year', async () => {
        const caller = oshaSummaryRouter.createCaller(mockContext);

        const nextYear = new Date().getFullYear() + 1;
        const result = await caller.upsertEstablishmentInformation({
          ...validInput,
          year: nextYear,
        });

        expect(result).toBeDefined();
      });
    });
  });

  describe('upsertExecutiveCertification', () => {
    let summaryId: string;

    beforeEach(async () => {
      // Create a summary record to be certified
      const inserted = await db
        .insert(oshaCompanyInformation)
        .values({
          companyName: 'Test Company for Certification',
          companyFacilityId: 'FAC-CERT',
          companyNAICSCode: 123456,
          companyEIN: '*********',
          year: 2026,
          companyAnnualAverageNumberOfEmployees: 150,
          companyTotalHoursWorked: 15000,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id!,
        })
        .returning({ id: oshaCompanyInformation.id });
      summaryId = inserted[0].id;
    });

    it('should successfully update executive certification information', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const certificationInput = {
        summaryId,
        executiveName: 'John Doe',
        executiveTitle: 'CEO',
        dateCertified: new Date(),
        digitalSignature: 'John Doe',
        year: 2026,
      };

      const result = await caller.upsertExecutiveCertification(certificationInput);

      expect(result).toBeDefined();

      // Verify the database was updated
      const updatedSummary = await db
        .select()
        .from(oshaCompanyInformation)
        .where(eq(oshaCompanyInformation.id, summaryId))
        .limit(1);

      expect(updatedSummary).toHaveLength(1);
      expect(updatedSummary[0].executiveName).toBe(certificationInput.executiveName);
      expect(updatedSummary[0].executiveTitle).toBe(certificationInput.executiveTitle);
      expect(updatedSummary[0].digitalSignature).toBe(certificationInput.digitalSignature);
      expect(updatedSummary[0].dateCertified).toEqual(expect.any(Date));

      // Verify audit trail
      const auditTrail = await db
        .select()
        .from(oshaAuditTrail)
        .where(eq(oshaAuditTrail.entityId, summaryId))
        .orderBy(desc(oshaAuditTrail.createdAt))
        .limit(1);

      expect(auditTrail).toHaveLength(1);
      expect(auditTrail[0].action).toBe('updated');
      expect(auditTrail[0].entityType).toBe('osha_company_information');
    });

    it('should throw a validation error if summaryId has an invalid format', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const certificationInput = {
        summaryId: 'invalid-id',
        executiveName: 'John Doe',
        executiveTitle: 'CEO',
        dateCertified: new Date(),
        digitalSignature: 'John Doe',
        year: 2026,
      };
      await expect(caller.upsertExecutiveCertification(certificationInput)).rejects.toThrow();
    });

    it('should not throw an error if summaryId does not exist in the database', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const nonExistentSummaryId = createId();
      const certificationInput = {
        summaryId: nonExistentSummaryId,
        executiveName: 'John Doe',
        executiveTitle: 'CEO',
        dateCertified: new Date(),
        digitalSignature: 'John Doe',
        year: 2026,
      };

      await expect(caller.upsertExecutiveCertification(certificationInput)).resolves.toBeDefined();

      const record = await db
        .select()
        .from(oshaCompanyInformation)
        .where(eq(oshaCompanyInformation.id, nonExistentSummaryId));
      expect(record).toHaveLength(0);
    });

    it('should fail validation if executiveName is missing', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const certificationInput = {
        summaryId,
        executiveName: '',
        executiveTitle: 'CEO',
        dateCertified: new Date(),
        digitalSignature: 'John Doe',
        year: 2026,
      };
      await expect(caller.upsertExecutiveCertification(certificationInput)).rejects.toThrow();
    });

    it('should fail validation if executiveTitle is missing', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const certificationInput = {
        summaryId,
        executiveName: 'John Doe',
        executiveTitle: '',
        dateCertified: new Date(),
        digitalSignature: 'John Doe',
        year: 2026,
      };
      await expect(caller.upsertExecutiveCertification(certificationInput)).rejects.toThrow();
    });

    it('should fail validation if digitalSignature is missing', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const certificationInput = {
        summaryId,
        executiveName: 'John Doe',
        executiveTitle: 'CEO',
        dateCertified: new Date(),
        digitalSignature: '',
        year: 2026,
      };
      await expect(caller.upsertExecutiveCertification(certificationInput)).rejects.toThrow();
    });
  });

  describe('getEstablishInformationByYear', () => {
    beforeEach(async () => {
      // Clear all existing data to ensure a clean slate
      await db.delete(oshaCompanyInformation);

      // Create records for multiple years for the primary mock user's company
      await db.insert(oshaCompanyInformation).values([
        {
          companyName: 'Test Company 2025',
          companyFacilityId: 'FAC-2025',
          companyNAICSCode: 123456,
          companyEIN: '*********',
          year: 2025,
          companyAnnualAverageNumberOfEmployees: 100,
          companyTotalHoursWorked: 200000,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id!,
        },
        {
          companyName: 'Test Company 2026',
          companyFacilityId: 'FAC-2026',
          companyNAICSCode: 123456,
          companyEIN: '*********',
          year: 2026,
          companyAnnualAverageNumberOfEmployees: 110,
          companyTotalHoursWorked: 220000,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          createdBy: mockUser.id!,
        },
        // Create a record for a different company in the same year to test isolation
        {
          companyName: 'Other Company 2026',
          companyFacilityId: 'FAC-OTHER-2026',
          companyNAICSCode: 654321,
          companyEIN: '*********',
          year: 2026,
          companyAnnualAverageNumberOfEmployees: 50,
          companyTotalHoursWorked: 100000,
          upkeepCompanyId: 'other-comp',
          createdBy: 'other-user',
        },
      ]);
    });

    it('should retrieve the correct establishment information for a given year', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const result = await caller.getEstablishmentInformationByYear({ year: 2026 });

      expect(result).toBeDefined();
      expect(result?.year).toBe(2026);
      expect(result?.companyName).toBe('Test Company 2026');
      expect(result?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
    });

    it('should return undefined if no information exists for the given year', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const result = await caller.getEstablishmentInformationByYear({ year: 2028 });

      expect(result).toBeNull();
    });

    it('should not retrieve information for a different company', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const resultFor2026 = await caller.getEstablishmentInformationByYear({ year: 2026 });

      // Ensure the retrieved record belongs to the user's company, not the "other" company
      expect(resultFor2026).toBeDefined();
      expect(resultFor2026?.upkeepCompanyId).toBe(mockUser.upkeepCompanyId);
      expect(resultFor2026?.companyName).not.toBe('Other Company 2026');
    });
  });

  describe('getOshaCasesSummary', () => {
    const testYear = 2025;
    beforeEach(async () => {
      await db.delete(oshaReports);
      await db.delete(oshaCompanyInformation);
      await db.insert(oshaCompanyInformation).values({
        companyName: 'Test Company',
        companyFacilityId: 'FAC-123',
        companyNAICSCode: 123456,
        companyEIN: '*********',
        year: testYear,
        companyAnnualAverageNumberOfEmployees: 100,
        companyTotalHoursWorked: 200000,
        upkeepCompanyId: mockUser.upkeepCompanyId,
        createdBy: mockUser.id!,
      });
      await db.insert(oshaReports).values([
        // Case 1: Death
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: true,
          createdAt: new Date(`${testYear}-01-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'fatality',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 2: Days Away
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: false,
          daysAwayFromWork: 10,

          createdAt: new Date(`${testYear}-02-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'days_away_from_work',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 3: Days Away
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: false,
          daysAwayFromWork: 5,

          createdAt: new Date(`${testYear}-03-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'days_away_from_work',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 4: Restricted Work
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: false,
          daysAwayFromWork: 0,
          daysRestrictedFromWork: 8,

          createdAt: new Date(`${testYear}-04-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'job_restriction',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 5: Restricted Work
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: false,
          daysAwayFromWork: 0,
          daysRestrictedFromWork: 2,

          createdAt: new Date(`${testYear}-05-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'job_restriction',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 6: Other Case
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: false,
          daysAwayFromWork: 0,
          daysRestrictedFromWork: 0,
          type: 'medical_treatment_beyond_first_aid',

          createdAt: new Date(`${testYear}-06-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 7: Draft (ignore)
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: true,
          createdAt: new Date(`${testYear}-07-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'fatality',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 8: Archived (ignore)
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: true,

          archivedAt: new Date(),
          createdAt: new Date(`${testYear}-08-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'fatality',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 9: Different Year (ignore)
        {
          eventId,
          upkeepCompanyId: mockUser.upkeepCompanyId,
          wasDeceased: true,

          createdAt: new Date(`${testYear + 1}-01-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'fatality',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
        // Case 10: Different Company (ignore)
        {
          eventId,
          upkeepCompanyId: 'other-comp',
          wasDeceased: true,

          createdAt: new Date(`${testYear}-09-15`),
          employeeWorkLocation: 'location',
          employeeDepartment: 'department',
          employeeJobTitle: 'title',
          employeeDateOfHire: new Date(),
          employeeShift: 'day',
          bodyPartInjured: 'head',
          typeOfInjury: 'trauma',
          treatmentLocation: 'hospital',
          typeOfMedicalCare: 'emergency_room',
          type: 'fatality',
          reasonForReport: 'reason',
          createdBy: mockUser.id!,
        },
      ]);
    });

    it('should correctly calculate all case summaries and rates', async () => {
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const summary = await caller.getOshaCasesSummary({ year: testYear });

      expect(summary.deaths).toBe(2);
      expect(summary.totalDaysAway).toBe(15);
      expect(summary.restrictedWorkCases).toBe(2);
      expect(summary.otherCases).toBe(1);
      expect(summary.totalCases).toBe(7);
      expect(summary.trcRate).toBe(7);
      expect(summary.dartRate).toBe(4);
    });

    it('should round rates to two decimal places', async () => {
      await db
        .update(oshaCompanyInformation)
        .set({ companyTotalHoursWorked: 123456 })
        .where(eq(oshaCompanyInformation.year, testYear));

      const caller = oshaSummaryRouter.createCaller(mockContext);
      const summary = await caller.getOshaCasesSummary({ year: testYear });

      expect(summary.trcRate).toBe(11.34);
      expect(summary.dartRate).toBe(6.48);
    });

    it('should return zero for rates if total hours worked is not available', async () => {
      await db.delete(oshaCompanyInformation);
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const summary = await caller.getOshaCasesSummary({ year: testYear });

      expect(summary.totalCases).toBe(7);
      expect(summary.trcRate).toBe(0);
      expect(summary.dartRate).toBe(0);
    });

    it('should return zero for rates if total hours worked is zero', async () => {
      await db
        .update(oshaCompanyInformation)
        .set({ companyTotalHoursWorked: 0 })
        .where(eq(oshaCompanyInformation.year, testYear));
      const caller = oshaSummaryRouter.createCaller(mockContext);
      const summary = await caller.getOshaCasesSummary({ year: testYear });

      expect(summary.totalCases).toBe(7);
      expect(summary.trcRate).toBe(0);
      expect(summary.dartRate).toBe(0);
    });
  });
});
