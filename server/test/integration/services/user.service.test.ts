import { describe, it, expect, afterEach, beforeEach, vi } from 'vitest';
import { deleteCache, getCache, setCache } from '@server/redis/cache';
import { getCurrentUser, getUserCacheKey } from '@server/services/user.service';
import api from '@server/utils/api';
import { UPKEEP_USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { User } from '@shared/schema.types';

// Mock the API module
vi.mock('@server/utils/api', () => ({
  default: {
    get: vi.fn(),
  },
  buildDefaultHeaders: vi.fn((headers) => ({
    'x-user-token': headers['x-user-token'],
    cookie: headers.cookie,
  })),
}));

describe('UserService', () => {
  describe('getCurrentUser', () => {
    const token = 'test-token';
    const cacheKey = getUserCacheKey(token);

    const mockApiUser = {
      id: '123',
      username: 'testuser',
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: 'Doe',
      roleId: '456',
      groupName: 'Test Group',
      groupId: 789,
      ehsAccountType: 'admin',
      userAccountType: UPKEEP_USER_ACCOUNT_TYPES.ADMIN,
      featureFlags: {},
      role: {
        specialSubscription: {
          hasEhsAccess: true,
        },
      },
    };

    beforeEach(() => {
      // Reset all mocks before each test
      vi.clearAllMocks();
    });

    afterEach(async () => {
      await deleteCache(cacheKey);
    });

    it('should get a user from cache', async () => {
      const cachedUser = {
        ...mockApiUser,
        groupId: '789',
        fullName: 'John Doe',
        userAccountType: 'Admin',
        permissions: {
          'ehs-incident': {
            create: 'full',
            view: 'full',
            edit: 'full',
          },
          'ehs-capa': {
            create: 'full',
            view: 'full',
            edit: 'full',
          },
          'ehs-access-point': {
            create: 'full',
            view: 'full',
            edit: 'full',
          },
        },
        featureFlags: {
          webEHS: true,
        },
      };

      await setCache(cacheKey, cachedUser);

      const user = await getCurrentUser({
        headers: {
          'x-user-token': token,
        },
      });

      expect(user).toStrictEqual(cachedUser);
      expect(api.get).not.toHaveBeenCalled();
    });

    it('should get a user from the api', async () => {
      vi.mocked(api.get).mockResolvedValueOnce({
        data: {
          result: mockApiUser,
        },
      });

      const user = await getCurrentUser({
        headers: {
          'x-user-token': token,
        },
      });

      expect(api.get).toHaveBeenCalledWith('/api/v1/users/me', {
        headers: expect.objectContaining({
          'x-user-token': token,
        }),
      });

      expect(user).toMatchObject({
        id: '123',
        username: 'testuser',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        upkeepCompanyId: '456',
        fullName: 'John Doe',
        role: 'admin',
        permissions: expect.any(Object),
        featureFlags: {
          webEHS: false,
        },
        hasEhsEnabled: true,
      });

      // Verify it was cached
      const cachedUser = await getCache<User>(cacheKey);
      expect(cachedUser).toEqual(user);
    });

    it('should throw error when no token provided', async () => {
      await expect(
        getCurrentUser({
          headers: {},
        }),
      ).rejects.toThrow('No token provided');

      expect(api.get).not.toHaveBeenCalled();
    });

    it('should handle API errors gracefully', async () => {
      vi.mocked(api.get).mockRejectedValueOnce(new Error('API Error'));

      await expect(
        getCurrentUser({
          headers: {
            'x-user-token': token,
          },
        }),
      ).rejects.toThrow();

      expect(api.get).toHaveBeenCalledTimes(1);
    });
  });
});
