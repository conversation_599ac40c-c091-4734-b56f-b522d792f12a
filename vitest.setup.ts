import { vi, beforeAll, afterAll, afterEach } from 'vitest';

// Mock environment variables
vi.stubGlobal('process', {
  ...process,
  env: {
    ...process.env,
    DATABASE_URL: 'postgres://postgres:postgres@localhost:5432/ehs_test',
    UPKEEP_API_URL: 'https://api.test.upkeep.com',
    UPKEEP_API_KEY: 'test-key',
    UPKEEP_APP_URL: 'https://test.upkeep.com',
    OPENAI_API_KEY: 'sk-test-key123456789',
    ENVIRONMENT_PREFIX: 'test',
    LOG_LEVEL: 'error',
    SHOW_LOGS: 'false',
    NODE_ENV: 'test',
    // AWS environment variables
    AWS_REGION: 'test-region',
    AWS_ACCESS_KEY_ID: 'test-key-id',
    AWS_SECRET_ACCESS_KEY: 'test-secret-key',
    S3_BUCKET_NAME: 'test-bucket',
    // Mandrill environment variables
    MANDRILL_API_KEY: 'test-mandrill-key',
    MANDRILL_FROM_EMAIL: '<EMAIL>',
    MANDRILL_FROM_NAME: 'Test EHS System',
    // Rate limiting for tests (important for middleware tests)
    RATE_LIMIT_MAX_REQUESTS: '5',
    RATE_LIMIT_WINDOW_MS: '60000',
    // Frontend URL
    EHS_URL: 'http://localhost:3000',
    REDIS_URL: 'redis://localhost:6379/777',
    // Dromo API keys for tests
    DROMO_API_KEY: 'test-dromo-api-key',
  },
});

// Global test lifecycle hooks
beforeAll(() => {
  console.log('Test environment setup complete');
});

afterEach(() => {
  vi.resetAllMocks();
});

afterAll(() => {
  // Cleanup after all tests complete
});
