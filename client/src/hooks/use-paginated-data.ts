import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/router.types';
import {
  AccessPointsFilters,
  CapasFilters,
  EventsFilters,
  OshaReportsFilters,
  PublicSearchSchema,
} from '@shared/schema.types';
import { GlobalLocationsFilters } from '@shared/location.types';
import { useCallback, useMemo } from 'react';
import z from 'zod';

const TABLE_LIMIT = 20;

type BaseSearchParams = {
  search?: string;
  enabled?: boolean;
  mustIncludeObjectIds?: string[];
};

type LocationSearchParams = BaseSearchParams;

type AssetSearchParams = BaseSearchParams & {
  locationId?: string;
};

type IncidentSearchParams = BaseSearchParams & {
  filters: EventsFilters;
  minimal?: boolean;
};

type CapaSearchParams = {
  search?: string;
  filters: CapasFilters;
  enabled?: boolean;
};

type AccessPointSearchParams = {
  search?: string;
  enabled?: boolean;
  filters: AccessPointsFilters;
};

type PublicLocationSearchParams = BaseSearchParams & {
  upkeepCompanyId: string;
};

type PublicAssetSearchParams = {
  upkeepCompanyId: string;
  search?: string;
  locationId?: string;
  objectId?: string | string[];
  enabled?: boolean;
};

/**
 * Cursor-based hooks using useInfiniteQuery for efficient pagination
 */

/**
 * Hook for cursor-based location search with infinite loading
 */
export function useInfiniteLocations({ search = '', enabled = true, mustIncludeObjectIds }: LocationSearchParams = {}) {
  const query = trpc.location.search.useInfiniteQuery(
    { search, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['location']['search']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return query.data?.pages.flatMap((page: RouterOutputs['location']['search']) => page.result) ?? [];
  }, [query.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return query.fetchNextPage();
  }, [query.fetchNextPage]);

  return {
    data: allData,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: query.error,
  };
}

/**
 * Hook for cursor-based asset search with infinite loading
 */
export function useInfiniteAssets({
  search = '',
  locationId,
  enabled = true,
  mustIncludeObjectIds,
}: AssetSearchParams = {}) {
  const query = trpc.asset.search.useInfiniteQuery(
    { search, locationId, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['asset']['search']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return query.data?.pages.flatMap((page: RouterOutputs['asset']['search']) => page.result) ?? [];
  }, [query.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return query.fetchNextPage();
  }, [query.fetchNextPage]);

  return {
    data: allData,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: query.error,
  };
}

/**
 * Hook for cursor-based incident list with infinite loading
 */
export function useInfiniteIncidents({
  search = '',
  filters,
  enabled = true,
  mustIncludeObjectIds,
}: IncidentSearchParams) {
  const response = trpc.incident.list.useInfiniteQuery(
    { search, ...filters, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['incident']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    data: allData,
    isLoading: response.isLoading,
    isFetchingNextPage: response.isFetchingNextPage,
    hasNextPage: response.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: response.error,
    isFetchedAfterMount: response.isFetchedAfterMount,
  };
}

/**
 * Hook for cursor-based minimal incident list with infinite loading
 */
export function useInfiniteMinimalIncidents({
  search = '',
  filters,
  enabled = true,
  mustIncludeObjectIds,
}: IncidentSearchParams) {
  const response = trpc.incident.minimalList.useInfiniteQuery(
    { search, ...filters, limit: TABLE_LIMIT, mustIncludeObjectIds },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['incident']['minimalList']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    data: allData,
    isLoading: response.isLoading,
    isFetchingNextPage: response.isFetchingNextPage,
    hasNextPage: response.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: response.error,
  };
}

/**
 * Hook for cursor-based CAPA list with infinite loading
 */
export function useInfiniteCapas({ search = '', filters, enabled = true }: CapaSearchParams) {
  const query = trpc.capa.list.useInfiniteQuery(
    { search, ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['capa']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return query.data?.pages.flatMap((page: RouterOutputs['capa']['list']) => page.result) ?? [];
  }, [query.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return query.fetchNextPage();
  }, [query.fetchNextPage]);

  return {
    data: allData,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: query.error,
  };
}

/**
 * Hook for cursor-based access points list with infinite loading
 */
export function useInfiniteAccessPoints({ search = '', enabled = true, filters }: AccessPointSearchParams) {
  const query = trpc.accessPoint.list.useInfiniteQuery(
    { search, ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['accessPoint']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return query.data?.pages.flatMap((page: RouterOutputs['accessPoint']['list']) => page.result) ?? [];
  }, [query.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return query.fetchNextPage();
  }, [query.fetchNextPage]);

  return {
    data: allData,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: query.error,
    isFetchedAfterMount: query.isFetchedAfterMount,
  };
}

/**
 * Hook for cursor-based public location search with infinite loading
 */
export function useInfiniteLocationsPublic({
  upkeepCompanyId,
  search = '',
  mustIncludeObjectIds,
  enabled = true,
}: PublicLocationSearchParams) {
  const query = trpc.location.searchPublic.useInfiniteQuery(
    { upkeepCompanyId, search, mustIncludeObjectIds, limit: TABLE_LIMIT },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['location']['searchPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return query.data?.pages.flatMap((page: RouterOutputs['location']['searchPublic']) => page.result) ?? [];
  }, [query.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return query.fetchNextPage();
  }, [query.fetchNextPage]);

  return {
    data: allData,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: query.error,
  };
}

/**
 * Hook for cursor-based public asset search with infinite loading
 */
export function useInfiniteAssetsPublic({
  upkeepCompanyId,
  search = '',
  locationId,
  objectId,
  enabled = true,
}: PublicAssetSearchParams) {
  const query = trpc.asset.searchPublic.useInfiniteQuery(
    { upkeepCompanyId, search, locationId, objectId, limit: 50 },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['asset']['searchPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < 50) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return query.data?.pages.flatMap((page: RouterOutputs['asset']['searchPublic']) => page.result) ?? [];
  }, [query.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return query.fetchNextPage();
  }, [query.fetchNextPage]);

  return {
    data: allData,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: query.error,
  };
}

/**
 * Hook for cursor-based public user search with infinite loading
 */
export function useInfiniteUsersPublic({
  search = '',
  enabled = true,
  mustIncludeObjectIds,
  upkeepCompanyId,
  limit = TABLE_LIMIT,
}: z.infer<typeof PublicSearchSchema> & { enabled?: boolean }) {
  const query = trpc.user.getUsersPublic.useInfiniteQuery(
    { search, mustIncludeObjectIds, upkeepCompanyId, limit },
    {
      enabled: enabled && !!upkeepCompanyId,
      getNextPageParam: (lastPage: RouterOutputs['user']['getUsersPublic']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: false,
      refetchOnMount: true,
      initialCursor: 0,
    },
  );

  const allData = useMemo(() => {
    return query.data?.pages.flatMap((page: RouterOutputs['user']['getUsersPublic']) => page.result) ?? [];
  }, [query.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return query.fetchNextPage();
  }, [query.fetchNextPage]);

  return {
    data: allData,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: query.error,
  };
}

/**
 * Hook for cursor-based incident list with infinite loading
 */
export function useInfiniteOshaReports({
  search = '',
  filters,
  enabled = true,
  mustIncludeObjectIds,
}: {
  search: string;
  filters: OshaReportsFilters;
  enabled: boolean;
  mustIncludeObjectIds?: string[];
}) {
  const response = trpc.oshaReport.list.useInfiniteQuery(
    { search, ...filters, limit: TABLE_LIMIT, mustIncludeObjectIds, sortBy: 'createdAt', sortOrder: 'desc' },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['oshaReport']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return response.data?.pages.flatMap((page) => page.result) ?? [];
  }, [response.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return response.fetchNextPage();
  }, [response.fetchNextPage]);

  return {
    data: allData,
    isLoading: response.isLoading,
    isFetchingNextPage: response.isFetchingNextPage,
    hasNextPage: response.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    isError: response.isError,
    error: response.error,
  };
}

export interface GlobalLocationSearchParams {
  search?: string;
  filters?: GlobalLocationsFilters;
  enabled?: boolean;
}

/**
 * Hook for cursor-based global locations list with infinite loading
 */
export function useInfiniteGlobalLocations({ search = '', filters, enabled = true }: GlobalLocationSearchParams) {
  const query = trpc.globalLocation.list.useInfiniteQuery(
    { search, ...filters, limit: TABLE_LIMIT },
    {
      enabled,
      getNextPageParam: (lastPage: RouterOutputs['globalLocation']['list']) => {
        if (!lastPage.nextCursor) return undefined;
        if (lastPage.result.length < TABLE_LIMIT) return undefined;
        return lastPage.nextCursor;
      },
      refetchOnWindowFocus: false,
      staleTime: 1000,
      retry: false,
      refetchOnMount: true,
    },
  );

  const allData = useMemo(() => {
    return query.data?.pages.flatMap((page: RouterOutputs['globalLocation']['list']) => page.result) ?? [];
  }, [query.data]);

  const wrappedFetchNextPage = useCallback(() => {
    return query.fetchNextPage();
  }, [query.fetchNextPage]);

  return {
    data: allData,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    fetchNextPage: wrappedFetchNextPage,
    error: query.error,
  };
}
