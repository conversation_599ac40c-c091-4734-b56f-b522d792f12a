import type { Mixpanel } from 'mixpanel-browser';
import { AnalyticsEvents } from './analytics-events';
import { ANALYTICS_EVENTS } from './event-names';
import { ConsentManager } from './consent';
import { User } from 'shared/schema.types';

// Core analytics state
let mixpanel: Mixpanel | null = null;
let isInitialized = false;
let anonymousId: string | null = null;
let isIdentified = false;
let debugEnabled = false;

const consentManager = ConsentManager();

// Initialize Mixpanel when needed
const initializeMixpanel = async (token: string, debug: boolean = false): Promise<void> => {
  try {
    if (!isInitialized && token && token.trim() !== '') {
      const { default: mixpanelLib } = await import('mixpanel-browser');
      mixpanelLib.init(token, {
        debug,
        track_pageview: false,
        persistence: 'localStorage',
      });

      mixpanel = mixpanelLib;
      anonymousId = mixpanel.get_distinct_id();
      isInitialized = true;
    }

    debugEnabled = debug;
    console.log('[EHS Analytics] mixpanel initialized', token, debug);
  } catch (error) {
    // Silently fail - don't break the app
    console.error('[EHS Analytics] Initialization failed:', error);
  }
};

const getSessionId = (): string => {
  if (typeof window !== 'undefined' && window.sessionStorage) {
    let sessionId = window.sessionStorage.getItem('ehs_analytics_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
      window.sessionStorage.setItem('ehs_analytics_session_id', sessionId);
    }
    return sessionId;
  }
  return `session_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
};

// Extract business identifiers that are GDPR/CCPA compliant
const getCompliantUserData = (user?: User) => {
  if (!user) return {};

  return {
    user_id: user.id,
    organization_id: user.upkeepCompanyId,
    role: user.role,
    platform: 'web',
  };
};

// Extract personal data that requires explicit consent
const getPersonalUserData = (user: User) => {
  if (!user) return {};

  return {
    email: user.email,
    first_name: user.firstName,
    last_name: user.lastName,
    full_name: user.fullName,
    username: user.username,
    created_at: user.createdAt,
    last_login_at: user.lastLoginAt,
  };
};

// Simple component mapping for the 3 main event categories
const COMPONENT_MAP = {
  incident: 'Incidents',
  capa: 'CAPAs',
  access_point: 'QR Access Points',
  notification: 'Notifications',
};

const getComponentFromEvent = (eventName: string): string => {
  // Loop through ANALYTICS_EVENTS to find which key contains this event
  for (const [componentKey, events] of Object.entries(ANALYTICS_EVENTS)) {
    if (Object.values(events).includes(eventName as keyof AnalyticsEvents)) {
      const lowercaseKey = componentKey.toLowerCase();
      return COMPONENT_MAP[lowercaseKey as keyof typeof COMPONENT_MAP] || 'Unknown';
    }
  }

  return 'Unknown';
};

// Get base properties that should be included in all events
const getBaseEventProperties = (eventName: string, userContext?: User) => {
  if (!userContext) {
    return {
      platform: 'web',
      role: 'Public',
      access_scope: 'public',
    };
  }

  const component = getComponentFromEvent(eventName);

  return {
    platform: 'web' as const,
    role: userContext?.role || undefined,
    organization_id: userContext?.upkeepCompanyId || undefined,
    component,
  };
};

const track = async <T extends keyof AnalyticsEvents>(
  eventName: T,
  properties: AnalyticsEvents[T] = {} as AnalyticsEvents[T],
  userContext?: User,
): Promise<void> => {
  try {
    const consent = consentManager.getConsent();

    const compliantData = getCompliantUserData(userContext);
    const baseProperties = getBaseEventProperties(eventName, userContext);

    const eventData = {
      ...baseProperties,
      ...compliantData,
      ...properties,
      timestamp: new Date().toISOString(),
      session_id: getSessionId(),
    };

    if (consent.analytics && userContext) {
      const personalData = getPersonalUserData(userContext);
      Object.assign(eventData, personalData);
    }

    if (debugEnabled) {
      console.log(`[EHS Analytics] track ${eventName}`, {
        properties: eventData,
        consent_state: consent.analytics ? 'granted' : 'denied',
        has_personal_data: consent.analytics && !!userContext,
        mixpanel_initialized: isInitialized,
      });
    }

    // Send to Mixpanel if initialized and available
    if (isInitialized && mixpanel) {
      mixpanel.track(`ehs.${eventName}`, eventData);
      if (debugEnabled) {
        console.log(`[EHS Analytics] Sent to Mixpanel: ${eventName}`);
      }
    }
  } catch (error) {
    // Silently fail - don't break the app
    console.error('[EHS Analytics] Track failed:', error);
  }
};

const handleConsentUpdate = async (consentChoices: { analytics?: boolean }, userContext?: User): Promise<void> => {
  try {
    if (!mixpanel) {
      return;
    }

    const previousConsent = consentManager.getConsent();
    consentManager.updateConsent(consentChoices);
    const newConsent = consentManager.getConsent();

    // User granted analytics consent - link their anonymous history
    if (newConsent.analytics && !previousConsent.analytics && userContext) {
      if (!isIdentified && anonymousId) {
        mixpanel.alias(userContext.id);
      }

      mixpanel.identify(userContext.id);

      const personalData = getPersonalUserData(userContext);
      const compliantData = getCompliantUserData(userContext);

      if (Object.keys({ ...personalData, ...compliantData }).length > 0) {
        mixpanel.people.set({ ...compliantData, ...personalData });
      }

      isIdentified = true;
    } else if (!newConsent.analytics && previousConsent.analytics) {
      // User revoked consent - back to anonymous
      mixpanel.reset();
      isIdentified = false;
      anonymousId = mixpanel.get_distinct_id();
    } else if (newConsent.analytics && previousConsent.analytics && isIdentified && userContext) {
      // User already consented, just updating traits
      const personalData = getPersonalUserData(userContext);
      const compliantData = getCompliantUserData(userContext);

      if (Object.keys({ ...personalData, ...compliantData }).length > 0) {
        mixpanel.people.set({ ...compliantData, ...personalData });
      }
    }
  } catch (error) {
    console.error('[EHS Analytics] Consent update failed:', error);
  }
};

const updateConsent = (consent: { analytics?: boolean }): void => {
  try {
    consentManager.updateConsent(consent);
  } catch (error) {
    console.error('[EHS Analytics] Consent update failed:', error);
  }
};

const getConsent = () => {
  try {
    return consentManager.getConsent();
  } catch (error) {
    console.error('[EHS Analytics] Get consent failed:', error);
    return { analytics: true, functional: true, lastUpdated: null };
  }
};

const shouldShowConsentBanner = (): boolean => {
  try {
    return consentManager.shouldShowConsentBanner();
  } catch (error) {
    console.error('[EHS Analytics] Consent banner check failed:', error);
    return false;
  }
};

// Public function to initialize with config
const initialize = async (token: string, debug: boolean = false): Promise<void> => {
  await initializeMixpanel(token, debug);
};

export const analytics = {
  track,
  handleConsentUpdate,
  updateConsent,
  getConsent,
  shouldShowConsentBanner,
  initialize,
};
