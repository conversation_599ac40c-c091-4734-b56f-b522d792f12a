import { REPORT_TYPE_MAP } from '../constants';
import { ANALYTICS_EVENTS } from '../event-names';
import { BaseEventProperties } from './base-event-properties';

export interface EventEvents {
  // Incident Creation & Form Interaction Events
  [ANALYTICS_EVENTS.EVENT.FORM_VIEWED]: {
    form_entry_point?: 'QR Code' | 'Global Create' | 'Event Log';
    access_point_id?: string;
    location_id?: string;
    is_prefilled_from_qr?: boolean;
  } & BaseEventProperties;

  [ANALYTICS_EVENTS.EVENT.VOICE_STARTED]: {
    access_point_id?: string;
  } & BaseEventProperties;

  [ANALYTICS_EVENTS.EVENT.VOICE_SUCCESSFUL]: {
    duration_ms?: number;
    fields_populated_count?: number;
    access_point_id?: string;
  } & BaseEventProperties;

  [ANALYTICS_EVENTS.EVENT.VOICE_FAILED]: {
    reason?: 'no_speech_detected' | 'mic_denied' | 'api_error';
    duration_ms?: number;
    access_point_id?: string;
  } & BaseEventProperties;

  [ANALYTICS_EVENTS.EVENT.FIELD_AI_POPULATED]: {
    field_name?: string;
    was_edited?: boolean;
    ai_confidence_score?: number;
    access_point_id?: string;
  } & BaseEventProperties;

  [ANALYTICS_EVENTS.EVENT.FORM_VALIDATION_FAILED]: {
    validation_errors?: string[];
    error_message?: string;
    access_point_id?: string;
  } & BaseEventProperties;

  [ANALYTICS_EVENTS.EVENT.FORM_SUBMITTED]: {
    event_id?: string;
    report_type?: (typeof REPORT_TYPE_MAP)[keyof typeof REPORT_TYPE_MAP];
    severity_level?: 'Low' | 'Medium' | 'High' | 'Critical';
    location?: string;
    asset?: string;
    hazard_category?: string;
    root_cause?: string;
    media_attached_count?: number;
    is_ai_assisted?: boolean;
    first_name?: string;
    last_name?: string;
    reporter_email?: string;
    form_entry_point?: 'QR Code' | 'Global Create' | 'Event Log';
    is_prefilled_from_qr?: boolean;
    access_point_id?: string;
  } & BaseEventProperties;

  [ANALYTICS_EVENTS.EVENT.VOICE_TO_FORM_ABANDONED]: {
    duration_ms?: number;
    last_field_interacted?: string;
    access_point_id?: string;
  } & BaseEventProperties;

  [ANALYTICS_EVENTS.EVENT.FORM_ABANDONED]: {
    form_entry_point?: string;
    duration_on_form_seconds?: number;
    access_point_id?: string;
  } & BaseEventProperties;

  // Incident View & Interaction Events
  [ANALYTICS_EVENTS.EVENT.LOG_VIEW_OPENED]: {
    default_sort_by?: string;
    default_sort_order?: string;
  };

  [ANALYTICS_EVENTS.EVENT.DETAIL_VIEW_OPENED]: {
    event_id?: string;
    current_status?: string;
    severity?: string;
    report_type?: string;
    access_scope?: 'global' | 'submitted';
    is_ai_summary_present?: boolean;
    entry_point?: 'log_table' | 'notification_link' | 'direct_url';
  };

  [ANALYTICS_EVENTS.EVENT.FILTER_APPLIED]: {
    filter_name?: string;
    filter_value?: string | string[];
    include_archived_toggle_state?: boolean;
  };

  [ANALYTICS_EVENTS.EVENT.FILTER_RESET]: {};

  [ANALYTICS_EVENTS.EVENT.SEARCH_PERFORMED]: {
    search_term?: string;
    result_count?: number;
  };

  [ANALYTICS_EVENTS.EVENT.ROW_ACTION_CLICKED]: {
    event_id?: string;
    action?: 'View' | 'Edit' | 'Create CAPA' | 'Archive';
  };

  [ANALYTICS_EVENTS.EVENT.ACTION_TAKEN]: {
    event_id?: string;
    action_type?: 'Create CAPA' | 'Create OSHA Log' | 'Edit Event' | 'Mark as in Review' | 'Close Without Action';
  };

  [ANALYTICS_EVENTS.EVENT.EDIT_INITIATED]: {
    event_id?: string;
    source?: 'detail_page' | 'log_table';
  };

  [ANALYTICS_EVENTS.EVENT.CAPA_CREATED_FROM_EVENT]: {
    event_id?: string;
    capa_id?: string;
  };

  [ANALYTICS_EVENTS.EVENT.OSHA_LOG_CREATED_FROM_EVENT]: {
    event_id?: string;
    osha_log_id?: string;
  };

  [ANALYTICS_EVENTS.EVENT.REVIEWED]: {
    event_id?: string;
    previous_status?: string;
  };

  [ANALYTICS_EVENTS.EVENT.CLOSED_WITHOUT_ACTION]: {
    event_id?: string;
    previous_status?: string;
    reason?: string;
  };

  [ANALYTICS_EVENTS.EVENT.ARCHIVED]: {
    event_id?: string;
    source?: 'detail_page' | 'log_table';
    action?: 'archived' | 'unarchived';
  };

  [ANALYTICS_EVENTS.EVENT.PRINT_TRIGGERED]: {
    event_id?: string;
  };

  [ANALYTICS_EVENTS.EVENT.EXPORT_TRIGGERED]: {
    export_format?: 'PDF' | 'CSV';
    event_id?: string;
    source?: 'detail_page' | 'log_table';
  };
}
