import MainLayout from '@/components/layout/main-layout';
import { Gatekeeper } from '@/components/layout/gatekeeper';
import { Toaster } from '@/components/ui/sonner';
import { AppContextProvider } from '@/contexts/app-context';
import { useConfig } from '@/hooks/use-config';
import AccessPointsView from '@/pages/AccessPointsView';
import CapaDetails from '@/pages/CapaDetails';
import CapaLog from '@/pages/CapaLog';
import EditCapa from '@/pages/EditCapa';
import EditIncident from '@/pages/EditIncident';
import GlobalLocationsView from '@/pages/GlobalLocationsView';
import IncidentDetails from '@/pages/IncidentDetails';
import IncidentLog from '@/pages/IncidentLog';
import NewCapa from '@/pages/NewCapa';
import NewIncident from '@/pages/NewIncident';
import NewIncidentReport from '@/pages/NewIncidentReport';
import { NewIncidentReportSuccess } from '@/pages/NewIncidentReportSuccess';
import OshaForm301 from '@/pages/NewOshaReport';
import NotFound from '@/pages/NotFound';
import OshaLogs from '@/pages/OshaLogs';
import { trpc, trpcClient } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { StrictMode } from 'react';
import { Route, Switch, useLocation } from 'wouter';

function Router() {
  return (
    <Switch>
      <Route path={ROUTES.INCIDENT_NEW} component={NewIncident} />
      <Route path={ROUTES.INCIDENT_EDIT} component={EditIncident} />
      <Route path={ROUTES.INCIDENT_DETAILS} component={IncidentDetails} />
      <Route path={ROUTES.INCIDENT_LIST} component={IncidentLog} />
      <Route path={ROUTES.ACCESS_POINTS_LIST} component={AccessPointsView} />
      <Route path={ROUTES.ACCESS_POINTS_NEW} component={AccessPointsView} />
      <Route path={ROUTES.GLOBAL_LOCATIONS_LIST} component={GlobalLocationsView} />
      <Route path={ROUTES.GLOBAL_LOCATIONS_NEW} component={GlobalLocationsView} />
      <Route path={ROUTES.CAPA_LIST} component={CapaLog} />
      <Route path={ROUTES.CAPA_NEW} component={NewCapa} />
      <Route path={ROUTES.CAPA_EDIT} component={EditCapa} />
      <Route path={ROUTES.CAPA_DETAILS} component={CapaDetails} />
      <Route path={ROUTES.OSHA_REPORTS} component={OshaLogs} />
      <Route path={ROUTES.OSHA_REPORT_NEW} component={OshaForm301} />
      {/* <Route path={ROUTES.OSHA_SUMMARY} component={OshaSummary} />
      <Route path={ROUTES.OSHA_AGENCY_REPORTS} component={OshaAgencyReports} /> */}
      <Route path={ROUTES.NOT_FOUND} component={NotFound} />
      <Route path="*" component={NotFound} />
    </Switch>
  );
}

function PublicRouter() {
  return (
    <Switch>
      <Route path={ROUTES.INCIDENT_PUBLIC_REPORT} component={NewIncidentReport} />
      <Route path={ROUTES.INCIDENT_PUBLIC_REPORT_SUCCESS} component={NewIncidentReportSuccess} />
    </Switch>
  );
}

function AppContent() {
  const [location] = useLocation();

  // Initialize config
  useConfig();

  // Check if current location is a public route
  const isPublicRoute = location.startsWith(ROUTES.PUBLIC_BASE);

  if (isPublicRoute) {
    return (
      <>
        <Toaster />
        <PublicRouter />
      </>
    );
  }

  return (
    <AppContextProvider>
      <Toaster />
      <MainLayout>
        <Gatekeeper>
          <Router />
        </Gatekeeper>
      </MainLayout>
    </AppContextProvider>
  );
}

const queryClient = new QueryClient();

export function App() {
  return (
    <StrictMode>
      <trpc.Provider client={trpcClient} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          <AppContent />
        </QueryClientProvider>
      </trpc.Provider>
    </StrictMode>
  );
}
