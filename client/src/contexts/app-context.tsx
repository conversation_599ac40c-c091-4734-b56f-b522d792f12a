import { trpc } from '@/providers/trpc';
import { AppRouter } from '@server/trpc/router';
import { RouterOutputs } from '@shared/router.types';
import { TRPCClientErrorLike } from '@trpc/react-query';
import { createContext, ReactNode, useContext } from 'react';

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppContextProvider');
  }
  return context;
};

export type AppContextType = {
  user: RouterOutputs['user']['me'] | undefined;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  error: TRPCClientErrorLike<AppRouter> | null;
};

const AppContext = createContext<AppContextType>({} as AppContextType);

export const AppContextProvider = ({ children }: { children: ReactNode }) => {
  const {
    data: user,
    isPending,
    isError,
    isSuccess,
    error,
  } = trpc.user.me.useQuery(undefined, {
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 15, // 15 minutes
  });

  return (
    <AppContext.Provider
      value={{
        user,
        isLoading: isPending,
        isError,
        isSuccess,
        error,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export { AppContext };
