import { RouterOutputs } from '@shared/router.types';
import { jsPDF } from 'jspdf';

type OshaSummary = RouterOutputs['oshaSummary']['getOshaCasesSummary'];
type OshaSummaryEstablishmentInfo = RouterOutputs['oshaSummary']['getEstablishmentInformationByYear'];

interface GenerateOshaSummaryPdfOptions {
  data: OshaSummary;
  establishmentInfo?: OshaSummaryEstablishmentInfo;
  year: number;
}

export const generateOshaSummaryPdf = (options: GenerateOshaSummaryPdfOptions) => {
  const { data, establishmentInfo, year } = options;

  // Create new PDF document
  const doc = new jsPDF('p', 'mm', 'a4');
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;

  // Colors and styles
  const primaryColor = '#000000';
  const borderColor = '#000000';

  // Helper function to draw a rectangle
  const drawRect = (x: number, y: number, width: number, height: number, fill = false) => {
    doc.setDrawColor(borderColor);
    doc.setLineWidth(0.5);
    if (fill) {
      doc.setFillColor(240, 240, 240);
      doc.rect(x, y, width, height, 'FD');
    } else {
      doc.rect(x, y, width, height);
    }
  };

  // Helper function to add centered text
  const addCenteredText = (text: string, y: number, fontSize = 12) => {
    doc.setFontSize(fontSize);
    doc.setTextColor(primaryColor);
    const textWidth = doc.getTextWidth(text);
    const x = (pageWidth - textWidth) / 2;
    doc.text(text, x, y);
  };

  // Helper function to add left-aligned text
  const addText = (text: string, x: number, y: number, fontSize = 10) => {
    doc.setFontSize(fontSize);
    doc.setTextColor(primaryColor);
    doc.text(text, x, y);
  };

  // Header section
  let currentY = 15; // Reduced from 20

  // Date and form identifier
  addText(`${new Date().toLocaleDateString('en-US')}`, 15, currentY);
  addText(
    `OSHA_Form300A_Summary_${establishmentInfo?.companyName?.replace(/\s+/g, '_')}_${year}`,
    pageWidth - 80,
    currentY,
  );

  currentY += 12; // Reduced from 15

  // Main title
  addCenteredText('OSHA Form 300A', currentY, 16);
  currentY += 6; // Reduced from 8
  addCenteredText('Summary of Work-Related Injuries and Illnesses', currentY, 14);
  currentY += 6; // Reduced from 8
  addCenteredText(
    'All establishments covered by Part 1904 must complete this Summary page, even if no injuries or illnesses occurred during the year.',
    currentY,
    10,
  );
  currentY += 6; // Reduced from 8
  addCenteredText(`Year: ${year}`, currentY, 14);

  currentY += 15; // Reduced from 20

  // Establishment Information section
  addText('Establishment Information', 15, currentY, 12);
  doc.setFont('helvetica', 'bold');
  currentY += 6; // Reduced from 8

  // Company info - left column
  addText(`Company Name: ${establishmentInfo?.companyName || 'Not specified'}`, 15, currentY, 10);
  addText(
    `Annual Average Number of Employees: ${establishmentInfo?.companyAnnualAverageNumberOfEmployees || 'Not specified'}`,
    pageWidth / 2 + 10,
    currentY,
    10,
  );
  currentY += 6; // Reduced from 8

  addText(`Facility ID: ${establishmentInfo?.companyFacilityId || 'Not specified'}`, 15, currentY, 10);
  addText(
    `Total Hours Worked by All Employees: ${establishmentInfo?.companyTotalHoursWorked?.toLocaleString() || 'Not specified'}`,
    pageWidth / 2 + 10,
    currentY,
    10,
  );
  currentY += 6; // Reduced from 8

  addText(`NAICS Code: ${establishmentInfo?.companyNAICSCode || 'Not specified'}`, 15, currentY, 10);

  currentY += 15; // Reduced from 20

  // Number of Cases table
  addText('Number of Cases', 15, currentY, 12);
  doc.setFont('helvetica', 'bold');
  currentY += 6; // Reduced from 8

  const tableWidth = pageWidth - 30;
  const col1Width = tableWidth * 0.7;
  const col2Width = tableWidth * 0.3;

  // Table header
  drawRect(15, currentY, col1Width, 8, true);
  drawRect(15 + col1Width, currentY, col2Width, 8, true);

  addText('Type of Case', 17, currentY + 5, 10);
  addText('Number', 15 + col1Width + 5, currentY + 5, 10);
  currentY += 8;

  // Table rows
  const cases = [
    { label: 'Deaths', value: data.deaths || 0 },
    { label: 'Cases with days away from work', value: data.daysAwayCases || 0 },
    { label: 'Cases with job transfer or restriction', value: data.restrictedWorkCases || 0 },
    { label: 'Other recordable cases', value: data.otherCases || 0 },
    { label: 'Total', value: data.totalCases || 0 },
  ];

  cases.forEach((caseItem, index) => {
    const isTotal = index === cases.length - 1;
    if (isTotal) {
      drawRect(15, currentY, col1Width, 8, true);
      drawRect(15 + col1Width, currentY, col2Width, 8, true);
      doc.setFont('helvetica', 'bold');
    } else {
      drawRect(15, currentY, col1Width, 8);
      drawRect(15 + col1Width, currentY, col2Width, 8);
      doc.setFont('helvetica', 'normal');
    }

    addText(caseItem.label, 17, currentY + 5, 10);
    addText(
      caseItem.value.toString(),
      15 + col1Width + col2Width - 10 - doc.getTextWidth(caseItem.value.toString()),
      currentY + 5,
      10,
    );
    currentY += 8;
  });

  currentY += 12; // Reduced from 15

  // Injury and Illness Rates table
  addText('Injury and Illness Rates', 15, currentY, 12);
  doc.setFont('helvetica', 'bold');
  currentY += 6; // Reduced from 8

  // Rates table header
  drawRect(15, currentY, col1Width, 8, true);
  drawRect(15 + col1Width, currentY, col2Width, 8, true);

  addText('Rate', 17, currentY + 5, 10);
  addText('Value', 15 + col1Width + 5, currentY + 5, 10);
  currentY += 8;

  // Rates table rows
  const rates = [
    { label: 'Total Recordable Case Rate (per 200,000 hours)', value: data.trcRate?.toFixed(2) || '0.00' },
    { label: 'DART Rate (per 200,000 hours)', value: data.dartRate?.toFixed(2) || '0.00' },
  ];

  rates.forEach((rate) => {
    drawRect(15, currentY, col1Width, 8);
    drawRect(15 + col1Width, currentY, col2Width, 8);

    addText(rate.label, 17, currentY + 5, 10);
    addText(rate.value, 15 + col1Width + col2Width - 10 - doc.getTextWidth(rate.value), currentY + 5, 10);
    currentY += 8;
  });

  currentY += 15; // Reduced from 20

  // Certification section
  addText('Certification', 15, currentY, 12);
  doc.setFont('helvetica', 'bold');
  currentY += 6; // Reduced from 8

  doc.setFont('helvetica', 'normal');

  // Add posting requirement text before certification
  addText(
    'Post this Summary page from February 1 to April 30 of the year following the year covered by the form.',
    15,
    currentY,
    9,
  );
  currentY += 6; // Reduced from 8

  const certText =
    'I certify that I have examined this document and that to the best of my knowledge the entries are true, accurate, and complete.';
  const splitText = doc.splitTextToSize(certText, pageWidth - 30);
  doc.text(splitText, 15, currentY);
  currentY += splitText.length * 4 + 10; // Reduced spacing

  // Executive information with proper spacing
  addText(`Company Executive: ${establishmentInfo?.executiveName || '________________________'}`, 15, currentY, 10);
  addText('Signature:', pageWidth / 2 + 10, currentY, 10);
  currentY += 10; // Reduced from 12

  addText(`Title: ${establishmentInfo?.executiveTitle || '________________________'}`, 15, currentY, 10);
  currentY += 6; // Reduced from 8

  // Add signature box - ensure it fits on the page
  const signatureBoxHeight = 20;
  const minBottomMargin = 40; // Reduced from 60
  const dateFieldHeight = 12; // Reduced from 15

  // Check if signature box and date field will fit
  const totalNeededSpace = signatureBoxHeight + dateFieldHeight + 15; // Reduced spacing
  if (currentY + totalNeededSpace > pageHeight - minBottomMargin) {
    // Adjust the entire certification section upward if needed
    const availableSpace = pageHeight - minBottomMargin - totalNeededSpace;
    if (availableSpace > 0) {
      currentY = availableSpace;
    }
  }

  // Remove the signature box - just keep the spacing
  // drawRect(pageWidth / 2 + 10, currentY, 60, signatureBoxHeight);

  currentY += signatureBoxHeight + 6; // Reduced from 8

  addText(
    `Date: ${establishmentInfo?.dateCertified ? new Date(establishmentInfo.dateCertified).toLocaleDateString('en-US') : '________________________'}`,
    15,
    currentY,
    10,
  );

  currentY += 10; // Reduced from 12

  // Footer with proper spacing from content - ensure minimum 30 units from bottom
  const footerY = Math.max(currentY + 6, pageHeight - 30); // Reduced margins

  // Add the footer text about posting requirements first
  addText(`Generated by UpKeep EHS on ${new Date().toLocaleDateString('en-US')}`, 15, footerY, 9);

  // Save the PDF
  const filename = `OSHA_Form300A_Summary_${establishmentInfo?.companyName?.replace(/\s+/g, '_') || 'Company'}_${year}.pdf`;
  doc.save(filename);
};
