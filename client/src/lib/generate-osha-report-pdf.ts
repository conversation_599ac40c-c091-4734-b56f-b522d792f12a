import { jsPDF } from 'jspdf';
import { format } from 'date-fns';
import { RouterOutputs } from '@shared/router.types';
import { formatDate } from '@shared/date-utils';
import { OSHA_TYPE_MAP, REPORT_TYPE_MAP, SHIFTS_MAP, STATUS_MAP, TYPE_OF_MEDICAL_CARE_MAP } from '@shared/schema.types';

type OshaReport = RouterOutputs['oshaReport']['getById'];

// Main export function
export const generateOshaReportPdf = (data: OshaReport): void => {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 20;
  const contentWidth = pageWidth - margin * 2;

  // Header
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('UpKeep EHS System', pageWidth / 2, 25, { align: 'center' });

  doc.setFontSize(12);
  doc.text('OSHA Form 301 Report', margin, 40);

  // Form title section
  let yPos = 50;
  doc.setFillColor(220, 220, 220);
  doc.rect(margin, yPos, contentWidth, 15, 'F');
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('OSHA Form 301', margin + 5, yPos + 7);
  doc.setFontSize(9);
  doc.text('Injury and Illness Incident Report', margin + 5, yPos + 12);

  // Section 1: Linked Safety Event
  yPos += 20;
  doc.setFillColor(70, 70, 70);
  doc.rect(margin, yPos, contentWidth, 12, 'F');
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(9);
  doc.setFont('helvetica', 'bold');
  doc.text('1    Linked Safety Event', margin + 5, yPos + 8);

  yPos += 15;
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  // Event details in two columns
  const leftCol = margin + 5;
  const rightCol = pageWidth / 2 + 10;

  doc.text(`Safety Event ID:`, leftCol, yPos);
  doc.text(`${data.event?.slug || 'SE-0001'}`, leftCol + 35, yPos);
  doc.text(`Status:`, rightCol, yPos);
  doc.text(`${data.event?.status ? STATUS_MAP[data.event.status] : 'Open'}`, rightCol + 20, yPos);

  yPos += 10;
  doc.text(`Title:`, leftCol, yPos);
  doc.text(`${data.event?.title || 'Something Wild'}`, leftCol + 35, yPos);
  doc.text(`Report Type:`, rightCol, yPos);
  doc.text(`${data.event?.type ? REPORT_TYPE_MAP[data.event.type] : 'Incident'}`, rightCol + 30, yPos);

  yPos += 10;
  doc.text(`Date/Time:`, leftCol, yPos);
  doc.text(
    `${data.event?.reportedAt ? formatDate(data.event.reportedAt) : 'July 12, 2025 at 1:03 AM'}`,
    leftCol + 35,
    yPos,
  );

  // Section 2: Employee Information
  yPos += 18;
  doc.setFillColor(70, 70, 70);
  doc.rect(margin, yPos, contentWidth, 12, 'F');
  doc.setTextColor(255, 255, 255);
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('2    Employee Information', margin + 5, yPos + 8);

  yPos += 15;
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  doc.text(`Employee Name:`, leftCol, yPos);
  doc.text(`${data.employeeName || 'asdasd'}`, leftCol + 40, yPos);
  doc.text(`Job Title:`, rightCol, yPos);
  doc.text(`${data.employeeJobTitle || 'asdasd'}`, rightCol + 25, yPos);

  yPos += 10;
  doc.text(`Department:`, leftCol, yPos);
  doc.text(`${data.employeeDepartment || 'asdasd'}`, leftCol + 40, yPos);
  doc.text(`Date of Hire:`, rightCol, yPos);
  doc.text(`${data.employeeDateOfHire ? formatDate(data.employeeDateOfHire) : '07/10/2025'}`, rightCol + 30, yPos);

  yPos += 10;
  doc.text(`Shift:`, leftCol, yPos);
  doc.text(`${data.employeeShift ? SHIFTS_MAP[data.employeeShift] : 'Day'}`, leftCol + 40, yPos);

  yPos += 10;
  doc.text(`OSHA Location:`, leftCol, yPos);
  doc.text(`${data.employeeWorkLocation || 'asdasd'}`, leftCol + 40, yPos);

  // Privacy case checkbox
  yPos += 10;
  const checkboxSize = 3;
  doc.rect(leftCol, yPos - 3, checkboxSize, checkboxSize);
  if (data.privacyCase) {
    doc.text('✓', leftCol + 0.5, yPos - 0.5);
  }
  doc.text('Privacy Case:', leftCol + 8, yPos);

  // Section 3: Medical Information
  yPos += 18;
  doc.setFillColor(70, 70, 70);
  doc.rect(margin, yPos, contentWidth, 12, 'F');
  doc.setTextColor(255, 255, 255);
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('3    Medical Information', margin + 5, yPos + 8);

  yPos += 15;
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  doc.text(`Body Part Affected:`, leftCol, yPos);
  doc.text(`${data.bodyPartInjured || 'fgdfg'}`, leftCol + 45, yPos);
  doc.text(`Type of Injury:`, rightCol, yPos);
  doc.text(`${data.typeOfInjury || 'asd'}`, rightCol + 35, yPos);

  yPos += 10;
  doc.text(`Treatment Location:`, leftCol, yPos);
  doc.text(`${data.treatmentLocation || 'Bay Medical Center'}`, leftCol + 45, yPos);
  doc.text(`Type of Care:`, rightCol, yPos);
  doc.text(
    `${data.typeOfMedicalCare ? TYPE_OF_MEDICAL_CARE_MAP[data.typeOfMedicalCare] : 'First Aid'}`,
    rightCol + 35,
    yPos,
  );

  // Medical checkboxes
  yPos += 12;
  doc.rect(leftCol, yPos - 3, checkboxSize, checkboxSize);
  if (data.prescribedMedication) {
    doc.text('✓', leftCol + 0.5, yPos - 0.5);
  }
  doc.text('Prescription Given:', leftCol + 8, yPos);

  doc.rect(rightCol, yPos - 3, checkboxSize, checkboxSize);
  if (data.wasDeceased) {
    doc.text('✓', rightCol + 0.5, yPos - 0.5);
  }
  doc.text('Resulted in Death:', rightCol + 8, yPos);

  yPos += 10;
  doc.rect(leftCol, yPos - 3, checkboxSize, checkboxSize);
  if (data.wasHospitalized) {
    doc.text('✓', leftCol + 0.5, yPos - 0.5);
  }
  doc.text('Was Hospitalized:', leftCol + 8, yPos);

  yPos += 10;
  doc.text(`Days Away from Work:`, leftCol, yPos);
  doc.text(`${data.daysAwayFromWork || 12}`, leftCol + 50, yPos);
  doc.text(`Days of Restricted Duty:`, rightCol, yPos);
  doc.text(`${data.daysRestrictedFromWork || 26}`, rightCol + 55, yPos);

  // Section 4: Work-Related Details
  yPos += 18;
  doc.setFillColor(70, 70, 70);
  doc.rect(margin, yPos, contentWidth, 12, 'F');
  doc.setTextColor(255, 255, 255);
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('4    Work-Related Details', margin + 5, yPos + 8);

  yPos += 15;
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  doc.text(`Date of Injury:`, leftCol, yPos);
  doc.text(
    `${data.event?.reportedAt ? format(new Date(data.event.reportedAt), 'MM/dd/yyyy') : '07/12/2025'}`,
    leftCol + 35,
    yPos,
  );
  doc.text(`Time of Event:`, rightCol, yPos);
  doc.text(
    `${data.event?.reportedAt ? format(new Date(data.event.reportedAt), 'HH:mm') : '01:03'}`,
    rightCol + 35,
    yPos,
  );

  yPos += 10;
  doc.text(`Location:`, leftCol, yPos);
  doc.text(`${data.employeeWorkLocation || 'asdasd'}`, leftCol + 35, yPos);

  // Section 5: Reporting Details
  yPos += 18;
  doc.setFillColor(70, 70, 70);
  doc.rect(margin, yPos, contentWidth, 12, 'F');
  doc.setTextColor(255, 255, 255);
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('5    Reporting Details', margin + 5, yPos + 8);

  yPos += 15;
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  doc.text(`Reported By:`, leftCol, yPos);
  doc.text(`${data.event?.reportedByName || 'Dhananjay Naik'}`, leftCol + 35, yPos);
  doc.text(`Prepared By:`, rightCol, yPos);
  doc.text(`${data.event?.reportedByName || 'Dhananjay Naik'}`, rightCol + 35, yPos);

  // Section 6: Corrective Actions
  yPos += 18;
  doc.setFillColor(70, 70, 70);
  doc.rect(margin, yPos, contentWidth, 12, 'F');
  doc.setTextColor(255, 255, 255);
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('6    Corrective Actions', margin + 5, yPos + 8);

  yPos += 15;
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  if (data.correctiveActions) {
    const splitText = doc.splitTextToSize(data.correctiveActions, contentWidth - 10);
    doc.text(splitText, leftCol, yPos);
    yPos += splitText.length * 4;
  }

  // Section 7: OSHA Reporting
  yPos += 18;
  doc.setFillColor(70, 70, 70);
  doc.rect(margin, yPos, contentWidth, 12, 'F');
  doc.setTextColor(255, 255, 255);
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('7    OSHA Reporting', margin + 5, yPos + 8);

  yPos += 15;
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  doc.text(`Primary Recordable Outcome:`, leftCol, yPos);
  if (data.type && OSHA_TYPE_MAP[data.type]) {
    doc.text(`${OSHA_TYPE_MAP[data.type]}`, leftCol + 65, yPos);
  }

  // Footer
  yPos = pageHeight - 30;
  doc.setFontSize(8);
  doc.text(`Generated: ${format(new Date(), 'MM/dd/yyyy HH:mm')}`, margin, yPos);
  doc.text('UpKeep EHS System', pageWidth / 2, yPos, { align: 'center' });
  doc.text(`Form ID: ${data.slug || 'N/A'}`, pageWidth - margin, yPos, { align: 'right' });

  // Save the PDF
  doc.save(`OSHA-301-${data.slug || 'report'}.pdf`);
};
