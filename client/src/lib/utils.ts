import { TransientAccessPoint } from '@/components/access-points/access-point.types';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const generatePublicIncidentUrl = (accessPoint: TransientAccessPoint) => {
  // Construct the full URL
  const baseUrl = window.location.origin;
  const queryParams = new window.URLSearchParams();
  queryParams.append('accessPointId', accessPoint.id);
  queryParams.append('upkeepCompanyId', accessPoint.upkeepCompanyId);

  return `${baseUrl}${ROUTES.INCIDENT_PUBLIC_REPORT}?${queryParams.toString()}`;
};
