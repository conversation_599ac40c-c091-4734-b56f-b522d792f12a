import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { Filters } from '@/components/capas/list/capa-filters';
import { CapaMobileFilters } from '@/components/capas/list/capa-mobile-filters';
import { CapaMobileView } from '@/components/capas/list/capa-mobile-view';
import { CapaTable } from '@/components/capas/list/capa-table';
import { CapasEmpty } from '@/components/capas/list/capas-empty';
import { CapasError } from '@/components/capas/list/capas-error';
import { CapasLoading } from '@/components/capas/list/capas-loading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteCapas } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { CapasFilters } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { useDebounce } from '@uidotdev/usehooks';
import { Search, X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'wouter';

export default function CapaLog() {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();
  const analytics = useAnalytics();

  const [searchTerm, setSearchTerm] = useState<string>('');

  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const { hasPermission } = usePermissions();

  const [filters, setFilters] = useState<CapasFilters>({
    status: [],
    type: [],
    priority: [],
    owner: [],
    includeArchived: false,
    dueDateRange: undefined,
    tags: [],
  });

  // Fetch capas data
  const {
    data: capas,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteCapas({
    search: debouncedSearchTerm,
    filters,
    enabled: true,
  });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Track page view on component mount
  useEffect(() => {
    analytics.track(ANALYTICS_EVENTS.CAPA.TRACKER_VIEW_OPENED, {
      default_sort_by: 'createdAt',
      default_sort_order: 'desc',
    });
  }, [analytics]);

  // Track search when debounced search term changes
  useEffect(() => {
    if (debouncedSearchTerm.trim() && capas) {
      analytics.track(ANALYTICS_EVENTS.CAPA.SEARCH_PERFORMED, {
        search_term: debouncedSearchTerm,
        result_count: capas.length,
      });
    }
  }, [debouncedSearchTerm, capas, analytics]);

  // Handle adding or removing a filter
  const toggleFilter = (type: 'status' | 'type' | 'priority' | 'owner' | 'tags', value: string) => {
    setFilters((prev) => {
      const currentFilters = [...(prev[type] ?? [])];
      const index = currentFilters.indexOf(value);

      if (index > -1) {
        currentFilters.splice(index, 1);
      } else {
        currentFilters.push(value);
      }

      return { ...prev, [type]: currentFilters };
    });
  };

  const trackFilterApplied = (
    type: 'status' | 'type' | 'priority' | 'owner' | 'tags' | 'dueDateRange' | 'includeArchived',
    value: string,
  ) => {
    analytics.track(ANALYTICS_EVENTS.CAPA.FILTER_APPLIED, {
      filter_name: type,
      filter_value: value,
      include_archived_toggle_state: filters.includeArchived,
    });
  };

  // Reset all filters
  const resetFilters = () => {
    // Track filter reset
    analytics.track(ANALYTICS_EVENTS.CAPA.FILTER_RESET, {});

    setFilters({
      status: [],
      type: [],
      priority: [],
      owner: [],
      includeArchived: false,
      dueDateRange: undefined,
      tags: [],
    });
  };

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      (filters.status?.length ?? 0) +
      (filters.type?.length ?? 0) +
      (filters.priority?.length ?? 0) +
      (filters.owner?.length ?? 0) +
      (filters.includeArchived ? 1 : 0) +
      (filters.dueDateRange?.from || filters.dueDateRange?.to ? 1 : 0) +
      (filters.tags?.length ?? 0)
    );
  }, [filters]);

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">CAPA Tracker</h1>
          <p className="text-muted-foreground">Track and manage all corrective and preventive actions</p>
        </div>

        <div className="flex items-center w-full md:w-auto gap-4">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search CAPAs..."
              className="pl-8 pr-4"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button variant="ghost" size="icon" className="absolute right-0 top-0 " onClick={() => setSearchTerm('')}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE) && (
            <Button onClick={() => navigate(ROUTES.CAPA_NEW)} className="whitespace-nowrap">
              + Create CAPA
            </Button>
          )}
          {/* Mobile filters */}
          <CapaMobileFilters
            filters={filters}
            toggleFilter={toggleFilter}
            activeFilterCount={activeFilterCount}
            resetFilters={resetFilters}
            setFilters={setFilters}
            trackFilterApplied={trackFilterApplied}
          />
        </div>
      </div>

      {/* Desktop Dropdown filters section */}
      <Filters
        filters={filters}
        toggleFilter={toggleFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        setFilters={setFilters}
        trackFilterApplied={trackFilterApplied}
      />

      {error ? <CapasError /> : null}

      {isLoading ? <CapasLoading /> : null}

      {/* Empty state */}
      {capas && capas.length === 0 && !isLoading && !error && (
        <CapasEmpty hasActiveFilters={activeFilterCount > 0} onResetFilters={resetFilters} />
      )}

      {!isMobile && capas && capas.length > 0 ? <CapaTable capas={capas} /> : null}

      {isMobile && capas && capas.length > 0 ? (
        <CapaMobileView capas={capas} activeFilterCount={activeFilterCount} resetFilters={resetFilters} />
      ) : null}

      {/* Load More Button for Infinite Scrolling */}
      {capas && capas.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${capas.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
