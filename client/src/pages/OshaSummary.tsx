import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { YearSelect } from '@/components/composite/year-select';
import { CasesSummary } from '@/components/osha-summary/cases-summary';
import { CompanyExecutiveCertificationDetails } from '@/components/osha-summary/company-executive-certification-details';
import { CompanyExecutiveCertificationForm } from '@/components/osha-summary/company-executive-certification-form';
import { EstablishmentInformationDetails } from '@/components/osha-summary/establishment-information-details';
import { EstablishmentInformationForm } from '@/components/osha-summary/establishment-information-form';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { generateOshaSummaryPdf } from '@/lib/generate-osha-summary-pdf';
import { trpc } from '@/providers/trpc';
import { Archive, Calendar, Download, FileText } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function OshaSummary() {
  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [showDialog, setShowDialog] = useState(false);

  const [isEditingCompanyInformation, setIsEditingCompanyInformation] = useState(false);
  const [isEditingCompanyExecutiveCertification, setIsEditingCompanyExecutiveCertification] = useState(false);

  const { data: establishmentInfo } = trpc.oshaSummary.getEstablishmentInformationByYear.useQuery({
    year: year,
  });

  const { data: summary } = trpc.oshaSummary.getOshaCasesSummary.useQuery({
    year,
  });

  const handleExport300A = () => {
    if (!summary || !establishmentInfo) {
      toast.error('Failed to generate PDF', {
        description: 'Please try again later',
      });
      return;
    }

    generateOshaSummaryPdf({
      data: summary,
      establishmentInfo: establishmentInfo,
      year: year,
    });
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <ArchiveConfirmationDialog
        archived={establishmentInfo?.archived ?? false}
        showDialog={showDialog}
        setShowDialog={setShowDialog}
        entityId={establishmentInfo?.id ?? ''}
        entityType="oshaSummary"
      />
      {/* Header Section */}
      <div className="mb-8">
        {/* Title and Icon */}

        {establishmentInfo?.archived && (
          <Alert variant="destructive" className="mb-6 border-red-200 bg-red-50">
            <AlertTitle>
              <div className="flex items-center gap-2">
                <Archive className="h-4 w-4 text-red-600" />
                <span>ARCHIVED - This year's OSHA logs are archived and read-only</span>
              </div>
            </AlertTitle>
            <AlertDescription>
              These records cannot be modified. To edit current year data, please navigate back to the main view.
              <Button variant="outline" size="sm" onClick={() => setShowDialog(true)} className="mt-1">
                Restore Year
              </Button>
            </AlertDescription>
          </Alert>
        )}
        <div className="flex items-center mb-6">
          <Calendar className="h-8 w-8 text-green-600 mr-3 bg-green-100 rounded p-1 flex-shrink-0" />
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">
            OSHA Form 300A Summary of Work-Related Injuries and Illnesses
          </h1>
        </div>

        {/* Controls Section */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          {/* Year Filter */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
            <span className="text-sm text-muted-foreground whitespace-nowrap">Select a year to filter</span>
            <YearSelect value={year} onChange={(year) => setYear(year)} />
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
            {/* Secondary Actions */}
            <div className="flex flex-col sm:flex-row gap-2">
              {!establishmentInfo?.archived ? (
                <Button
                  variant="outline"
                  onClick={() => setShowDialog(true)}
                  className="w-full sm:w-auto font-medium border-gray-300 hover:bg-gray-50"
                >
                  <Archive className="h-4 w-4 mr-2" />
                  Archive Year
                </Button>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => setShowDialog(true)}
                  className="w-full sm:w-auto font-medium border-gray-300 hover:bg-gray-50"
                >
                  <Archive className="h-4 w-4 mr-2" />
                  Restore Year
                </Button>
              )}
              <Button
                variant="outline"
                onClick={handleExport300A}
                className="w-full sm:w-auto font-medium border-gray-300 hover:bg-gray-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </Button>
              {/* Primary Action - Submit OSHA Report */}
              <Button onClick={() => {}} className="w-full sm:w-auto">
                <FileText className="h-4 w-4 mr-2" />
                Submit OSHA Report
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Grid */}
      <div className="space-y-4 lg:space-y-6">
        {/* Top Row - Company Info and Cases Summary */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 lg:gap-6">
          {/* Establishment Information Card */}
          <div>
            {isEditingCompanyInformation ? (
              <EstablishmentInformationForm
                year={year}
                establishmentInfo={establishmentInfo}
                onCancel={() => setIsEditingCompanyInformation(false)}
                onSave={() => setIsEditingCompanyInformation(false)}
              />
            ) : (
              <EstablishmentInformationDetails
                year={year}
                establishmentInfo={establishmentInfo}
                onEdit={() => setIsEditingCompanyInformation(true)}
              />
            )}
          </div>

          {/* Cases Summary Card */}
          <div>
            <CasesSummary summary={summary} />
          </div>

          {/* Certification Section - Full width */}
          <div>
            {isEditingCompanyExecutiveCertification ? (
              <CompanyExecutiveCertificationForm
                year={year}
                onSave={() => setIsEditingCompanyExecutiveCertification(false)}
                establishmentInfo={establishmentInfo}
              />
            ) : (
              <CompanyExecutiveCertificationDetails
                onEdit={() => setIsEditingCompanyExecutiveCertification(true)}
                establishmentInfo={establishmentInfo}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
