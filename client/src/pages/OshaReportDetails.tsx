import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { OshaAuditTrail } from '@/components/composite/osha-audit-trail';
import { StatusBadge } from '@/components/composite/status-badge';
import { OshaReportError } from '@/components/osha-reports/details/osha-report-details-error';
import { OshaReportLoading } from '@/components/osha-reports/details/osha-report-details-loading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { usePermissions } from '@/hooks/use-permissions';
import { generateOshaReportPdf } from '@/lib/generate-osha-report-pdf';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { OSHA_TYPE_MAP, REPORT_TYPE_MAP, SHIFTS_MAP, TYPE_OF_MEDICAL_CARE_MAP } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  AlertTriangle,
  Archive,
  Calendar,
  CheckCircle,
  ChevronLeft,
  Clock,
  Download,
  Edit,
  LinkIcon,
  Loader2,
  MoreVertical,
  ShieldAlert,
  Stethoscope,
  User,
  Users,
  WrenchIcon,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useLocation, useParams } from 'wouter';

export default function OshaReportDetails() {
  const { id } = useParams();
  const [_, navigate] = useLocation();
  const [loading, setLoading] = useState(false);
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const { hasPermission } = usePermissions();

  // Fetch OSHA report details
  const {
    data: report,
    isLoading,
    error,
  } = trpc.oshaReport.getById.useQuery(
    {
      id: id!,
    },
    {
      enabled: !!id,
    },
  );

  // If there's a linked incident, fetch its details
  const {
    data: linkedIncident,
    isLoading: isLoadingIncident,
    isError: isErrorIncident,
  } = trpc.incident.getById.useQuery(
    {
      id: report?.eventId!,
    },
    {
      enabled: !!report?.eventId,
    },
  );

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Handle export of OSHA 301 form as PDF
  const handleDownloadPdf = () => {
    if (!report) return;

    setLoading(true);

    try {
      generateOshaReportPdf(report);
      setLoading(false);

      toast.success('PDF Downloaded', {
        description: `OSHA Form 301 for report ${report.slug} has been downloaded.`,
      });
    } catch (error) {
      console.error('Error generating OSHA Form 301 PDF:', error);
      setLoading(false);

      toast.error('Export failed', {
        description: 'There was an error creating the PDF download.',
      });
    }
  };

  // Handle archive
  const handleArchive = () => {
    setShowArchiveConfirm(true);
  };

  if (isLoading) {
    return <OshaReportLoading />;
  }

  if (error || !report) {
    return <OshaReportError />;
  }

  return (
    <div className="container mx-auto py-4 px-4">
      <ArchiveConfirmationDialog
        showDialog={showArchiveConfirm}
        setShowDialog={setShowArchiveConfirm}
        entityType="oshaReport"
        entityId={report.id}
        archived={!!report.archivedAt}
      />
      {/* Back button */}
      <div className="mb-3">
        <Button variant="ghost" onClick={() => navigate(ROUTES.OSHA_REPORTS)}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>Back</span>
        </Button>
      </div>

      {/* OSHA Report Header */}
      <div className="flex flex-col md:flex-row justify-between items-start gap-3 mb-3">
        {/* Desktop View */}
        <div className="hidden md:block w-full">
          <div className="flex flex-wrap items-center gap-2 mb-1">
            <span className="text-sm font-medium text-muted-foreground">{report.slug}</span>
            <Badge className="bg-red-50 text-red-600 border border-red-200" variant="outline">
              OSHA Reportable
            </Badge>
            <Badge className="bg-blue-50 text-blue-600 border border-blue-200" variant="outline">
              Form 301
            </Badge>
            {report.archivedAt && (
              <Badge className="bg-amber-50 text-amber-600 border-amber-200 font-medium" variant="outline">
                <Archive className="h-3 w-3 mr-1" />
                Archived
              </Badge>
            )}
          </div>
          <h1 className="text-2xl md:text-3xl font-bold mb-2">OSHA 301 Incident Report</h1>
        </div>

        {/* Mobile View - with menu in title area */}
        <div className="md:hidden w-full">
          <div className="flex flex-wrap items-center gap-2 mb-1">
            <span className="text-sm font-medium text-muted-foreground">OSHA-{report.id}</span>
            <Badge className="bg-red-50 text-red-600 border border-red-200" variant="outline">
              OSHA Reportable
            </Badge>
            <Badge className="bg-blue-50 text-blue-600 border border-blue-200" variant="outline">
              Form 301
            </Badge>
            {report.archivedAt && (
              <Badge className="bg-amber-50 text-amber-600 border-amber-200 font-medium" variant="outline">
                <Archive className="h-3 w-3 mr-1" />
                Archived
              </Badge>
            )}
          </div>

          {/* Mobile title row with menu */}
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold mb-2 pr-3 flex-1">OSHA 301 Incident Report</h1>

            {/* Mobile menu in the title row */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 flex items-center justify-center"
                  aria-label="Open actions menu"
                >
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>OSHA Report Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />

                {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && (
                  <DropdownMenuItem
                    className="h-11 cursor-pointer"
                    onClick={() => navigate(ROUTES.BUILD_OSHA_REPORT_EDIT_PATH(report.id))}
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    <span>Edit OSHA Report</span>
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem className="h-11 cursor-pointer" onClick={handleDownloadPdf}>
                  <Download className="mr-2 h-4 w-4" />
                  <span>Download PDF</span>
                </DropdownMenuItem>

                <DropdownMenuSeparator />

                {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && (
                  <DropdownMenuItem className="h-11 cursor-pointer" onClick={handleArchive}>
                    <Archive className="mr-2 h-4 w-4" />
                    <span>{report.archivedAt ? 'Unarchive Report' : 'Archive Report'}</span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Desktop buttons - only shown on desktop */}
        <div className="hidden md:flex gap-2 self-start">
          {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && (
            <Button size="sm" onClick={() => navigate(ROUTES.BUILD_OSHA_REPORT_EDIT_PATH(report.id))}>
              <Edit className="h-4 w-4" />
              <span>Edit</span>
            </Button>
          )}

          <Button size="sm" onClick={handleDownloadPdf} disabled={loading}>
            <Download className="h-4 w-4" />
            <span>{loading ? 'Downloading...' : 'Download PDF'}</span>
          </Button>

          {/* Desktop-only Options Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {hasPermission(MODULES.EHS_OSHA_REPORTS, ALLOWED_ACTIONS.EDIT) && (
                <DropdownMenuItem onClick={handleArchive}>
                  <Archive className="mr-2 h-4 w-4" />
                  <span>{report.archivedAt ? 'Unarchive Report' : 'Archive Report'}</span>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Context bar with metadata */}
      <div className="flex flex-wrap items-center text-sm text-muted-foreground mb-4 gap-y-2">
        <div className="flex items-center">
          <Calendar className="h-4 w-4 mr-1.5" />
          <span>{formatDate(report.createdAt)}</span>
        </div>

        <div className="hidden sm:block text-muted-foreground mx-2">•</div>

        <div className="flex items-center">
          <User className="h-4 w-4 mr-1.5" />
          <span>{report.event.reportedBy?.fullName || 'Unknown'}</span>
        </div>

        <div className="hidden sm:block text-muted-foreground mx-2">•</div>

        <div className="flex items-center">
          <Clock className="h-4 w-4 mr-1.5" />
          <span>Prepared: {formatDate(report.createdAt)}</span>
        </div>
      </div>

      {/* Archived Status Banner */}
      {report.archivedAt && (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mb-4 flex items-center">
          <Archive className="h-5 w-5 text-amber-500 mr-2" />
          <p className="text-amber-800 font-medium">This report is archived and no longer appears in active logs.</p>
        </div>
      )}

      {/* Main content grid */}
      <div
        className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${
          report.archivedAt ? 'p-3 bg-amber-50/30 border border-amber-200 rounded-lg' : ''
        }`}
      >
        {/* Left column - main content */}
        <div className="md:col-span-2 space-y-4">
          {/* Section 1: Linked Safety Event Reference */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  1
                </span>
                <LinkIcon className="h-5 w-5 text-blue-600 mr-2" />
                Linked Safety Event Reference
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingIncident && (
                <div className="flex items-center justify-center p-6">
                  <Loader2 className="animate-spin h-4 w-4 mr-2" />
                  <p>Loading safety event details...</p>
                </div>
              )}

              {isErrorIncident && (
                <div className="flex items-center justify-center p-6">
                  <Loader2 className="animate-spin h-4 w-4 mr-2" />
                  <p>Loading safety event details...</p>
                </div>
              )}

              {!isLoadingIncident && !isErrorIncident && linkedIncident && (
                <div className="border rounded-md p-4 bg-blue-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Safety Event ID</h4>
                      <p className="font-medium">{linkedIncident.slug}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Title</h4>
                      <p className="font-medium">{linkedIncident.title}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Date & Time</h4>
                      <p>{formatDate(linkedIncident.reportedAt)}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Location</h4>
                      <p>{linkedIncident.location?.name || 'Not specified'}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Status</h4>
                      <StatusBadge status={linkedIncident.status} />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Type</h4>
                      <Badge variant="outline" className="border-blue-200">
                        {REPORT_TYPE_MAP[linkedIncident.type]}
                      </Badge>
                    </div>
                    <div className="col-span-2">
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Description</h4>
                      <div className="bg-white p-2 rounded-md">
                        <p>{linkedIncident.description || 'No description available'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Section 2: Employee Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  2
                </span>
                <User className="h-5 w-5 text-blue-600 mr-2" />
                Employee Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Employee Name</h4>
                  <p className="font-medium">
                    {report.privacyCase ? <span className="text-blue-600">Privacy Case</span> : report.employeeName}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Job Title</h4>
                  <p>{report.employeeJobTitle}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Department</h4>
                  <p>{report.employeeDepartment || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Date of Hire</h4>
                  <p>{report.employeeDateOfHire ? formatDate(report.employeeDateOfHire) : 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Shift</h4>
                  <p>{report.employeeShift ? SHIFTS_MAP[report.employeeShift] : 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Supervisor</h4>
                  <p>{report.employeeSupervisor || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Work Location</h4>
                  <p>{report.employeeWorkLocation || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Privacy Case</h4>
                  <div className="flex items-center">
                    {report.privacyCase ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        <span>Yes</span>
                      </div>
                    ) : (
                      <span className="text-gray-500">No</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 3: Medical Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  3
                </span>
                <Stethoscope className="h-5 w-5 text-blue-600 mr-2" />
                Medical Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Body Part Affected</h4>
                  <p>{report.bodyPartInjured || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Type of Injury</h4>
                  <p>{report.typeOfInjury || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Treatment Location</h4>
                  <p>{report.treatmentLocation || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Type of Medical Care</h4>
                  <p>{TYPE_OF_MEDICAL_CARE_MAP[report.typeOfMedicalCare] || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Prescription Given</h4>
                  <div className="flex items-center">
                    {report.prescribedMedication ? (
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                        <span>Yes</span>
                      </div>
                    ) : (
                      <span className="text-gray-500">No</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 4: OSHA Questions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  4
                </span>
                <ShieldAlert className="h-5 w-5 text-blue-600 mr-2" />
                OSHA Questions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Was hospitalized?</h4>
                  <div className="flex items-center">
                    {report.wasHospitalized ? (
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                        <span>Yes</span>
                      </div>
                    ) : (
                      <span className="text-gray-500">No</span>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Resulted in death?</h4>
                  <div className="flex items-center">
                    {report.wasDeceased ? (
                      <div className="flex items-center text-red-600">
                        <AlertTriangle className="h-4 w-4 mr-1" />
                        <span>Yes</span>
                      </div>
                    ) : (
                      <span className="text-gray-500">No</span>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Days Away from Work</h4>
                  <p className="font-medium">{report.daysAwayFromWork || '0'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Days of Restricted Work</h4>
                  <p className="font-medium">{report.daysRestrictedFromWork || '0'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 5: Witnesses & People */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  5
                </span>
                <Users className="h-5 w-5 text-blue-600 mr-2" />
                Witnesses & People
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Witnesses</h4>
                  {report.witnesses ? (
                    <p>{report.witnesses}</p>
                  ) : (
                    <p className="text-gray-500">No witnesses recorded</p>
                  )}
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Reported By</h4>
                  <p>{report.event.reportedBy?.fullName || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Form Prepared By</h4>
                  <p>{report.createdBy?.fullName || 'Not specified'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Prepared Date</h4>
                  <p>{formatDate(report.createdAt)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 6: Corrective Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  6
                </span>
                <WrenchIcon className="h-5 w-5 text-blue-600 mr-2" />
                Corrective Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Root Cause Analysis</h4>
                  <p>{report.rootCauseAnalysis || 'No root cause analysis available'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Corrective Actions</h4>
                  <p>{report.correctiveActions || 'No corrective actions recorded'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 7: OSHA Reporting */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  7
                </span>
                <ShieldAlert className="h-5 w-5 text-blue-600 mr-2" />
                OSHA Reporting
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Primary Recordable Outcome</h4>
                  <p className="font-medium text-gray-900">{OSHA_TYPE_MAP[report.type] || 'Not specified'}</p>
                </div>
                {report.reasonForReport && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Reason for Reportability</h4>
                    <p>{report.reasonForReport}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column - metadata & audit trail */}
        <div className="space-y-4">
          {/* Report Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Report Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Report ID</span>
                  <span className="text-sm font-medium">{report.slug}</span>
                </div>
                {report.updatedAt && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Last Updated</span>
                    <span className="text-sm">{formatDate(report.updatedAt)}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Audit Trail */}
          <OshaAuditTrail oshaReportId={report.id} />
        </div>
      </div>
    </div>
  );
}
