import { Filters } from '@/components/osha-reports/osha-reports-filters';
import { MobileFilters } from '@/components/osha-reports/osha-reports-mobile-filters';
import { OshaReportsMobileView } from '@/components/osha-reports/osha-reports-mobile-view';
import { OshaReportsTable } from '@/components/osha-reports/osha-reports-table';
import { OshaReportsFilters } from '@/components/osha-reports/osha-reports.types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteLocations, useInfiniteOshaReports } from '@/hooks/use-paginated-data';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { OshaCaseTypeSchema, OshaStatusSchema } from '@shared/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Search, X } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useLocation } from 'wouter';

export default function OshaLogs() {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();
  const [oshaSearch, setOshaSearch] = useState<string>('');
  const debouncedSearch = useDebounce(oshaSearch, 300);

  const [filters, setFilters] = useState<OshaReportsFilters>({
    status: OshaStatusSchema.enum.all,
    caseType: OshaCaseTypeSchema.enum.all,
    locationId: '',
    includeArchived: false,
    showPrivacyCases: false,
  });

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      filters.status.length +
      (filters.includeArchived ? 1 : 0) +
      (debouncedSearch.trim() ? 1 : 0) +
      filters.locationId.length
    );
  }, [filters, debouncedSearch]);

  const resetFilters = () => {
    setFilters({
      status: OshaStatusSchema.enum.all,
      caseType: OshaCaseTypeSchema.enum.all,
      locationId: '',
      includeArchived: false,
      showPrivacyCases: false,
    });
  };

  const {
    data: oshaReports,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteOshaReports({
    filters,
    search: debouncedSearch,
    enabled: true,
  });

  const { data: locations } = useInfiniteLocations({});

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">OSHA Logs</h1>
          <p className="text-muted-foreground">Manage and track OSHA recordable incidents</p>
        </div>
        <div className="flex items-center w-full md:w-auto gap-4">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search incidents..."
              className="pl-8 pr-4"
              value={oshaSearch}
              onChange={(e) => setOshaSearch(e.target.value)}
            />
            {oshaSearch && (
              <Button variant="ghost" size="icon" className="absolute right-0 top-0 " onClick={() => setOshaSearch('')}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <Button
            onClick={() => navigate(ROUTES.OSHA_REPORT_NEW)}
            // disabled={isYearArchived}
          >
            + Create OSHA Record
          </Button>
        </div>
      </div>

      <MobileFilters
        filters={filters}
        setFilters={setFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        locations={locations}
      />

      <Filters
        filters={filters}
        setFilters={setFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        locations={locations}
      />

      {!isMobile && oshaReports && oshaReports.length > 0 ? (
        <OshaReportsTable oshaReports={oshaReports} showPrivacyCases={filters.showPrivacyCases} />
      ) : null}

      {isMobile && oshaReports && oshaReports.length > 0 ? (
        <OshaReportsMobileView oshaReports={oshaReports} showPrivacyCases={filters.showPrivacyCases} />
      ) : null}

      {oshaReports && oshaReports.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${oshaReports.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
