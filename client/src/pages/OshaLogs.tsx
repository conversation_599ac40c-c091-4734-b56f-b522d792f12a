import { YearSelect } from '@/components/composite/year-select';
import { OshaReportsEmpty } from '@/components/osha-reports/list/osha-reports-empty';
import { OshaReportsError } from '@/components/osha-reports/list/osha-reports-error';
import { Filters } from '@/components/osha-reports/list/osha-reports-filters';
import { OshaReportsLoading } from '@/components/osha-reports/list/osha-reports-loading';
import { MobileFilters } from '@/components/osha-reports/list/osha-reports-mobile-filters';
import { OshaReportsMobileView } from '@/components/osha-reports/list/osha-reports-mobile-view';
import { OshaReportsTable } from '@/components/osha-reports/list/osha-reports-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteOshaReports } from '@/hooks/use-paginated-data';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { OshaReportsFilters } from '@shared/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Search, X } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useLocation } from 'wouter';

export default function OshaLogs() {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();
  const [oshaSearch, setOshaSearch] = useState<string>('');
  const debouncedSearch = useDebounce(oshaSearch, 300);

  const [filters, setFilters] = useState<OshaReportsFilters>({
    caseType: undefined,
    locationIds: [],
    includeArchived: undefined,

    year: new Date().getFullYear(),
  });

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      (filters.caseType ? 1 : 0) +
      (filters.includeArchived ? 1 : 0) +
      (filters.locationIds?.length ?? 0) +
      (debouncedSearch.trim() ? 1 : 0)
    );
  }, [filters, debouncedSearch]);

  const resetFilters = () => {
    setFilters({
      caseType: undefined,
      locationIds: [],
      includeArchived: undefined,
      year: new Date().getFullYear(),
    });
  };

  const {
    data: oshaReports,
    isLoading,
    isError,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteOshaReports({
    filters,
    search: debouncedSearch,
    enabled: true,
  });

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col md:flex-row justify-between items-start gap-4 md:gap-0 md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">OSHA Logs</h1>
          <p className="text-muted-foreground">Manage and track OSHA recordable incidents</p>
        </div>
        <div className="flex flex-col md:flex-row md:items-center w-full md:w-auto gap-4">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search OSHA Reports..."
              className="pl-8 pr-4"
              value={oshaSearch}
              onChange={(e) => setOshaSearch(e.target.value)}
            />
            {oshaSearch && (
              <Button variant="ghost" size="icon" className="absolute right-0 top-0 " onClick={() => setOshaSearch('')}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <Button
            onClick={() => navigate(ROUTES.OSHA_REPORT_NEW)}
            // disabled={isYearArchived}
          >
            + Create OSHA Record
          </Button>
          <MobileFilters
            filters={filters}
            setFilters={setFilters}
            activeFilterCount={activeFilterCount}
            resetFilters={resetFilters}
          />
        </div>
      </div>

      <div className="flex md:flex-row md:justify-end items-center gap-2 mb-4">
        <div className="text-sm text-muted-foreground">Select a year to filter</div>
        <YearSelect value={filters.year} onChange={(year) => setFilters({ ...filters, year })} />
      </div>

      <Filters
        filters={filters}
        setFilters={setFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
      />

      {isError ? <OshaReportsError /> : null}

      {isLoading ? <OshaReportsLoading /> : null}

      {oshaReports && oshaReports.length === 0 && !isLoading && !isError && (
        <OshaReportsEmpty hasActiveFilters={activeFilterCount > 0} onResetFilters={resetFilters} />
      )}

      {!isMobile && oshaReports && oshaReports.length > 0 ? <OshaReportsTable oshaReports={oshaReports} /> : null}

      {isMobile && oshaReports && oshaReports.length > 0 ? <OshaReportsMobileView oshaReports={oshaReports} /> : null}

      {oshaReports && oshaReports.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${oshaReports.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
