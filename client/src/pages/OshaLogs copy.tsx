// eslint-disable
// @ts-nocheck

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, Sheet<PERSON>ontent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useIsMobile } from '@/hooks/use-mobile';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  AlertCircle,
  AlertTriangle,
  Archive,
  Building,
  Calendar,
  CheckCircle,
  ChevronDown,
  Download,
  Eye,
  FileText,
  Filter,
  MapPin,
  Plus,
  Search,
  User,
  X,
  XCircle,
} from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

// OSHA Log types for the application
export interface OshaLogEntry {
  id: number;
  logId: string;
  title: string;
  type: 'Death' | 'Days Away' | 'Restricted' | 'Other Recordable' | '';
  status: 'Draft' | 'Submitted' | 'Archived';
  employee: string;
  jobTitle: string; // New field for job title
  dateTime: string;
  location: string;
  linkedSafetyEvent: string | null;
  privacyCase: boolean;
  daysAwayFromWork: number; // New field for days away from work
  daysRestricted: number; // New field for days of job restriction
  oshaReportable: boolean; // New field for OSHA reportable status
  archived?: boolean; // Field to track if report is archived
  archivedAt?: string; // When the report was archived
  archivedBy?: string; // Who archived the report
}

// Form 300A summary data interface
export interface OshaSummaryData {
  companyName: string;
  facilityId: string;
  naicsCode: string;
  year: number;
  totalDeaths: number;
  totalDaysAway: number;
  totalRestricted: number;
  totalOtherRecordable: number;
  totalCases: number;
  totalEmployees: number;
  totalHoursWorked: number;
  trcRate: number;
  dartRate: number;
}

export default function OshaLogs() {
  const [location, setLocation] = useLocation();
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState<string>('log');
  const [filterYear, setFilterYear] = useState<string>('2025');
  const [filterType, setFilterType] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [filterFacility, setFilterFacility] = useState<string>('');

  const [showPrivacyCases, setShowPrivacyCases] = useState<boolean>(true);
  const [includeArchived, setIncludeArchived] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortField, setSortField] = useState<string>('dateTime');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // State for editable establishment information and calculated rates
  const [isEditingEstablishment, setIsEditingEstablishment] = useState<boolean>(false);
  const [editableEstablishmentInfo, setEditableEstablishmentInfo] = useState<{
    companyName: string;
    facilityId: string;
    naicsCode: string;
    ein: string;
    year: number;
    totalEmployees: number;
    totalHoursWorked: number;
  }>({
    companyName: '',
    facilityId: '',
    naicsCode: '',
    ein: '12-3456789',
    year: 2025,
    totalEmployees: 0,
    totalHoursWorked: 0,
  });

  // State for calculated rates
  const [calculatedRates, setCalculatedRates] = useState<{
    trcRate: number;
    dartRate: number;
  }>({
    trcRate: 0,
    dartRate: 0,
  });

  // State for certification section
  const [certificationData, setCertificationData] = useState<{
    executiveName: string;
    executiveTitle: string;
    dateCertified: string;
    signature: string | null;
  }>({
    executiveName: '',
    executiveTitle: '',
    dateCertified: '',
    signature: null,
  });

  // Signature canvas refs and state
  const canvasRef = React.useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = React.useState(false);
  const [isSignatureComplete, setIsSignatureComplete] = React.useState(false);

  // Signature drawing functions
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    setIsDrawing(true);
    const rect = canvas.getBoundingClientRect();
    const x = 'touches' in e ? e.touches[0].clientX - rect.left : e.clientX - rect.left;
    const y = 'touches' in e ? e.touches[0].clientY - rect.top : e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.beginPath();
      ctx.moveTo(x, y);
    }
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = 'touches' in e ? e.touches[0].clientX - rect.left : e.clientX - rect.left;
    const y = 'touches' in e ? e.touches[0].clientY - rect.top : e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.lineTo(x, y);
      ctx.stroke();
    }
  };

  const stopDrawing = () => {
    setIsDrawing(false);
    const canvas = canvasRef.current;
    if (canvas) {
      const signatureData = canvas.toDataURL();
      setCertificationData((prev) => ({ ...prev, signature: signatureData }));
    }
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
    setCertificationData((prev) => ({ ...prev, signature: null }));
    setIsSignatureComplete(false);
  };

  const completeSignature = () => {
    setIsSignatureComplete(true);
    toast({
      title: 'Signature saved',
      description: 'Your digital signature has been captured successfully.',
    });
  };

  const editSignature = () => {
    setIsSignatureComplete(false);
  };

  // Initialize canvas
  React.useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
      }
    }
  }, []);

  // State for year archiving
  const [isYearArchived, setIsYearArchived] = useState<boolean>(false);
  const [showArchiveModal, setShowArchiveModal] = useState<boolean>(false);
  const [showArchiveBanner, setShowArchiveBanner] = useState<boolean>(false);
  const [dismissedBanner, setDismissedBanner] = useState<boolean>(false);

  // State for empty state preview (development/testing)
  const [previewEmptyState, setPreviewEmptyState] = useState<boolean>(false);

  // State for OSHA submission confirmation modal
  const [showPreDownloadModal, setShowPreDownloadModal] = useState<boolean>(false);
  const [showOSHASubmissionModal, setShowOSHASubmissionModal] = useState<boolean>(false);

  // State for serious incident reporting modal
  const [showSeriousIncidentModal, setShowSeriousIncidentModal] = useState<boolean>(false);
  const [seriousIncidentData, setSeriousIncidentData] = useState({
    incidentId: `INC-2025-${Math.floor(Math.random() * 9999)
      .toString()
      .padStart(4, '0')}`,
    dateOfIncident: new Date().toISOString().slice(0, 10), // Current date
    timeOfIncident: new Date().toTimeString().slice(0, 5), // Current time
    location: '',
    incidentType: '',
    numFatalities: 1,
    numHospitalized: 1,
    description: '',
    employeesInvolved: '',
    contactPerson: '',
    contactPhone: '',
    datePrepared: new Date().toISOString().slice(0, 16),
  });

  // State for storing serious incident notifications with sample data
  const [seriousIncidentNotifications, setSeriousIncidentNotifications] = useState<any[]>([
    {
      id: 1001,
      incidentId: 'INC-2025-1001',
      dateOfIncident: '2025-05-15',
      timeOfIncident: '14:30',
      location: 'Manufacturing Floor - Assembly Line 3',
      incidentType: 'Fatality',
      numFatalities: 1,
      numHospitalized: 1,
      description:
        'Employee was caught in machinery during routine maintenance. Safety lockout/tagout procedures were not properly followed. Emergency response was immediate but employee succumbed to injuries at the scene.',
      employeesInvolved: 'Michael Rodriguez, Maintenance Technician',
      contactPerson: 'Sarah Johnson',
      contactPhone: '(*************',
      datePrepared: '2025-05-15T14:45',
      submittedAt: '2025-05-15T15:00:00.000Z',
    },
    {
      id: 1002,
      incidentId: 'INC-2025-1002',
      dateOfIncident: '2025-05-28',
      timeOfIncident: '09:15',
      location: 'Warehouse - Loading Dock B',
      incidentType: 'Inpatient Hospitalization',
      numFatalities: 1,
      numHospitalized: 2,
      description:
        'Two employees were struck by falling pallets from forklift during loading operations. Both employees were transported to Regional Medical Center with serious injuries requiring overnight hospitalization.',
      employeesInvolved: 'David Chen, Lisa Martinez',
      contactPerson: 'Sarah Johnson',
      contactPhone: '(*************',
      datePrepared: '2025-05-28T09:30',
      submittedAt: '2025-05-28T10:15:00.000Z',
    },
    {
      id: 1003,
      incidentId: 'INC-2025-1003',
      dateOfIncident: '2025-06-01',
      timeOfIncident: '16:45',
      location: 'Production Area - Cutting Station 7',
      incidentType: 'Amputation',
      numFatalities: 1,
      numHospitalized: 1,
      description:
        "Employee's left index finger was amputated while operating table saw. Safety guard had been removed for cleaning and was not properly reinstalled before operation resumed.",
      employeesInvolved: 'Robert Kim',
      contactPerson: 'Sarah Johnson',
      contactPhone: '(*************',
      datePrepared: '2025-06-01T17:00',
      submittedAt: '2025-06-01T17:30:00.000Z',
    },
    {
      id: 1004,
      incidentId: 'INC-2025-1004',
      dateOfIncident: '2025-06-02',
      timeOfIncident: '11:20',
      location: 'Chemical Processing - Tank Farm Area',
      incidentType: 'Loss of an Eye',
      numFatalities: 1,
      numHospitalized: 1,
      description:
        'Chemical splash resulted in severe eye injury to employee during tank cleaning operations. Personal protective equipment was not properly sealed, allowing caustic solution to enter behind safety goggles.',
      employeesInvolved: 'Amanda Thompson',
      contactPerson: 'Sarah Johnson',
      contactPhone: '(*************',
      datePrepared: '2025-06-02T11:35',
      submittedAt: '2025-06-02T12:00:00.000Z',
    },
  ]);

  // State for form validation errors
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  // State for OSHA certification modal
  const [certificationAgreed, setCertificationAgreed] = useState<boolean>(false);

  // Fetch OSHA logs from the API
  const { data: oshaLogs = [], isLoading: isLoadingLogs } = useQuery({
    queryKey: ['/api/osha-logs', { includeArchived }],
    // Remove auto-refresh to prevent constant screen refreshing
    queryFn: async ({ queryKey }) => {
      const [endpoint, params] = queryKey as [string, { includeArchived: boolean }];
      const includeArchivedParam = params.includeArchived ? '?includeArchived=true' : '';
      const res = await fetch(`${endpoint}${includeArchivedParam}`);
      if (!res.ok) {
        throw new Error('Failed to fetch OSHA logs');
      }
      return res.json();
    },
  });

  // Mock data for initial rendering until API is ready
  const defaultSummaryData: OshaSummaryData = {
    companyName: 'UpKeep Manufacturing',
    facilityId: 'FAC-001',
    naicsCode: '336390',
    year: 2025,
    totalDeaths: 1,
    totalDaysAway: 2,
    totalRestricted: 1,
    totalOtherRecordable: 1,
    totalCases: 5,
    totalEmployees: 250,
    totalHoursWorked: 520000,
    trcRate: 1.92,
    dartRate: 0.77,
  };

  // Fetch OSHA summary data from the API
  const { data: apiSummaryData = defaultSummaryData, isLoading: isLoadingSummary } = useQuery<OshaSummaryData>({
    queryKey: ['/api/osha-summary', filterYear],
    // Remove auto-refresh to prevent constant screen refreshing
  });

  // Zero-case summary data for preview empty state
  const zeroCaseSummaryData: OshaSummaryData = {
    ...apiSummaryData,
    totalDeaths: 0,
    totalDaysAway: 0,
    totalRestricted: 0,
    totalOtherRecordable: 0,
    totalCases: 0,
    trcRate: 0,
    dartRate: 0,
  };

  // Use zero-case data when preview empty state is enabled
  const summaryData = previewEmptyState ? zeroCaseSummaryData : apiSummaryData;

  // Function to calculate TRC and DART rates
  const calculateRates = (hoursWorked: number, totalCases: number, daysAway: number, restricted: number) => {
    // Prevent division by zero
    if (hoursWorked <= 0) return { trcRate: 0, dartRate: 0 };

    // TRC Rate = (Total Recordable Cases × 200,000) ÷ Total Hours Worked
    const trcRate = (totalCases * 200000) / hoursWorked;

    // DART Rate = (Days Away + Job Transfer/Restriction × 200,000) ÷ Total Hours Worked
    const dartRate = ((daysAway + restricted) * 200000) / hoursWorked;

    return {
      trcRate: Number(trcRate.toFixed(2)),
      dartRate: Number(dartRate.toFixed(2)),
    };
  };

  // Check if current year is in the past (for showing the archive prompt banner)
  const currentYear = new Date().getFullYear();
  const isViewingPastYear = parseInt(filterYear) < currentYear;

  // Function to handle archiving a year
  const handleArchiveYear = () => {
    setShowArchiveModal(true);
  };

  // Function to confirm and process archive action
  const confirmArchiveYear = () => {
    // In a real implementation, this would call an API endpoint to archive all logs for the year
    setIsYearArchived(true);
    setShowArchiveModal(false);
    setShowArchiveBanner(false);

    // Show success toast
    toast({
      title: `Year ${filterYear} Archived`,
      description: 'All OSHA logs for this year are now read-only.',
    });
  };

  // Function to restore (unarchive) a year
  const handleRestoreYear = () => {
    // In a real implementation, this would call an API endpoint to restore all logs for the year
    setIsYearArchived(false);

    // Show success toast
    toast({
      title: `Year ${filterYear} Restored`,
      description: 'You can now edit OSHA logs for this year.',
    });
  };

  // Function to open pre-download modal
  const openPreDownloadModal = () => {
    setCertificationAgreed(false); // Reset certification checkbox when modal opens
    setShowPreDownloadModal(true);
  };

  // Function to open serious incident reporting modal
  const openSeriousIncidentModal = () => {
    setFormErrors({}); // Clear any existing errors
    setShowSeriousIncidentModal(true);
  };

  // Function to navigate to agency report details page
  const openAgencyReportView = (report: any) => {
    setLocation(`/agency-reports/${report.id}`);
  };

  // Function to handle exporting the OSHA Log (Form 300)
  const handleExportLog = () => {
    try {
      // Create CSV content for Form 300 log
      const csvHeaders = [
        'Case No.',
        'Employee Name',
        'Job Title',
        'Date of Injury/Illness',
        'Where Event Occurred',
        'Description of Injury/Illness',
        'Classification',
        'Days Away from Work',
        'Days of Job Transfer/Restriction',
        'OSHA Reportable',
      ];

      const csvData = filteredLogs.map((log) => [
        log.logId,
        log.privacyCase ? 'Privacy Case' : log.employee,
        log.jobTitle || '',
        format(new Date(log.dateTime), 'MM/dd/yyyy'),
        log.location || '',
        log.title || '',
        log.type || '',
        log.daysAwayFromWork || 0,
        log.daysRestricted || 0,
        log.oshaReportable ? 'Yes' : 'No',
      ]);

      // Create CSV string
      const csvContent = [csvHeaders.join(','), ...csvData.map((row) => row.map((cell) => `"${cell}"`).join(','))].join(
        '\n',
      );

      // Create and trigger download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `OSHA_Form_300_Log_${filterYear}_${format(new Date(), 'yyyyMMdd')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Export Complete',
        description: `OSHA Form 300 log exported successfully for ${filterYear}.`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error exporting OSHA log:', error);
      toast({
        title: 'Export Error',
        description: 'There was a problem exporting the OSHA log. Please try again.',
        variant: 'destructive',
        duration: 3000,
      });
    }
  };

  // Function to validate form and set error states
  const validateSeriousIncidentForm = () => {
    const errors: { [key: string]: string } = {};

    if (!seriousIncidentData.dateOfIncident) {
      errors.dateOfIncident = 'Date of incident is required';
    }
    if (!seriousIncidentData.timeOfIncident) {
      errors.timeOfIncident = 'Time of incident is required';
    }
    if (!seriousIncidentData.location.trim()) {
      errors.location = 'Location is required';
    }
    if (!seriousIncidentData.incidentType) {
      errors.incidentType = 'Please select an incident type';
    }
    if (!seriousIncidentData.description.trim()) {
      errors.description = 'Description is required';
    }
    if (!seriousIncidentData.contactPerson.trim()) {
      errors.contactPerson = 'Contact person is required';
    }
    if (!seriousIncidentData.contactPhone.trim()) {
      errors.contactPhone = 'Contact phone is required';
    }

    // Validate conditional fields
    if (seriousIncidentData.incidentType === 'Fatality' && seriousIncidentData.numFatalities <= 0) {
      errors.numFatalities = 'Number of fatalities must be greater than 0';
    }
    if (seriousIncidentData.incidentType === 'Inpatient Hospitalization' && seriousIncidentData.numHospitalized <= 0) {
      errors.numHospitalized = 'Number of hospitalized employees must be greater than 0';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Function to handle serious incident form submission
  const handleSeriousIncidentSubmit = () => {
    if (!validateSeriousIncidentForm()) {
      toast({
        title: 'Form Validation Failed',
        description: 'Please correct the errors below before submitting.',
        variant: 'destructive',
      });
      return;
    }

    // Add the incident data to the notifications array
    const newNotification = {
      ...seriousIncidentData,
      id: Date.now(), // Simple ID for prototype
      submittedAt: new Date().toISOString(),
    };

    console.log('Saving serious incident:', newNotification);
    console.log('Current notifications before save:', seriousIncidentNotifications);

    setSeriousIncidentNotifications((prev) => {
      const updated = [...prev, newNotification];
      console.log('Updated notifications:', updated);
      return updated;
    });

    // Show success message
    toast({
      title: 'Serious Incident Details Saved',
      description: 'Remember to complete the actual agency report using the provided links/phone numbers!',
      variant: 'default',
    });

    // Close modal and reset form
    setShowSeriousIncidentModal(false);
    setSeriousIncidentData({
      incidentId: `INC-2025-${Math.floor(Math.random() * 9999)
        .toString()
        .padStart(4, '0')}`,
      dateOfIncident: '',
      timeOfIncident: '',
      location: '',
      incidentType: '',
      numFatalities: 0,
      numHospitalized: 0,
      description: '',
      employeesInvolved: '',
      contactPerson: '',
      contactPhone: '',
      datePrepared: new Date().toISOString().slice(0, 16),
    });
  };

  // Function to handle OSHA report generation and download
  const handleGenerateAndDownloadReport = () => {
    if (!summaryData) {
      toast({
        title: 'Error',
        description: 'Summary data is not available. Please try again later.',
        variant: 'destructive',
      });
      return;
    }

    if (!certificationAgreed) {
      toast({
        title: 'Certification Required',
        description: 'You must affirm the certification statements before proceeding.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Capture audit trail metadata for compliance
      const auditMetadata = {
        actionType: 'OSHA_300A_CSV_DOWNLOAD',
        timestamp: new Date().toISOString(),
        userCertification: {
          agreed: certificationAgreed,
          agreedAt: new Date().toISOString(),
          ipAddress: 'USER_IP_PLACEHOLDER', // Would be captured server-side in production
          userAgent: navigator.userAgent,
        },
        reportDetails: {
          year: summaryData.year,
          companyName: summaryData.companyName,
          facilityId: summaryData.facilityId,
          totalCases: summaryData.totalCases,
          totalEmployees: summaryData.totalEmployees,
        },
        certificationStatements: [
          'Information accuracy and completeness certified',
          'User responsibility for data accuracy acknowledged',
          'UpKeep submission limitations acknowledged',
          'Data review completion certified',
        ],
      };

      // Log audit trail (in production, this would be sent to a secure audit service)
      console.log('OSHA Submission Audit Trail:', auditMetadata);

      // Store audit trail in localStorage for demo purposes
      const existingAuditTrail = JSON.parse(localStorage.getItem('oshaAuditTrail') || '[]');
      existingAuditTrail.push(auditMetadata);
      localStorage.setItem('oshaAuditTrail', JSON.stringify(existingAuditTrail));

      // Generate CSV content using OSHA ITA compliant format
      const csvHeaders = [
        'company_name',
        'ein',
        'establishment_name',
        'street_address',
        'city',
        'state',
        'zip',
        'naics_code',
        'size',
        'year_filing_for',
        'annual_average_employees',
        'total_hours_worked',
        'no_injuries_illnesses',
        'total_deaths',
        'total_dafw_cases',
        'total_dafw_days',
        'total_djtr_cases',
        'total_djtr_days',
        'total_other_cases',
      ];

      // Determine establishment size code based on employee count
      const getSizeCode = (employees: number) => {
        if (employees < 20) return 1;
        if (employees <= 99) return 21;
        if (employees <= 249) return 22;
        return 3; // 250 or more
      };

      // Determine if there were injuries/illnesses
      const hasInjuries = summaryData.totalCases > 0 ? 1 : 2;

      // Calculate total days (for prototype, estimate based on case counts)
      const estimatedDaysAway = summaryData.totalDaysAway * 15; // Estimate 15 days per case
      const estimatedDaysRestricted = summaryData.totalRestricted * 10; // Estimate 10 days per case

      const csvData = [
        summaryData.companyName,
        editableEstablishmentInfo.ein || '12-3456789', // Use EIN from form
        'Main Manufacturing Facility',
        '1234 Industrial Way',
        'Seattle',
        'WA',
        '98101',
        summaryData.naicsCode,
        getSizeCode(summaryData.totalEmployees),
        summaryData.year,
        summaryData.totalEmployees,
        summaryData.totalHoursWorked,
        hasInjuries,
        summaryData.totalDeaths,
        summaryData.totalDaysAway,
        estimatedDaysAway,
        summaryData.totalRestricted,
        estimatedDaysRestricted,
        summaryData.totalOtherRecordable,
      ];

      // Create CSV string
      const csvContent = [csvHeaders.join(','), csvData.join(',')].join('\n');

      // Create and trigger download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `osha_300a_summary_${summaryData.year}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Close pre-download modal and show post-download confirmation modal
      setShowPreDownloadModal(false);
      setShowOSHASubmissionModal(true);
    } catch (error) {
      toast({
        title: 'Export Error',
        description: 'Failed to generate OSHA report. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Check if we should show the archive banner for past years
  useEffect(() => {
    if (isViewingPastYear && !isYearArchived && !dismissedBanner) {
      setShowArchiveBanner(true);
    } else {
      setShowArchiveBanner(false);
    }
  }, [isViewingPastYear, isYearArchived, filterYear, dismissedBanner]);

  // Archive confirmation modal component
  const ArchiveConfirmationModal = () => (
    <AlertDialog open={showArchiveModal} onOpenChange={setShowArchiveModal}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Archive OSHA Logs for {filterYear}?</AlertDialogTitle>
          <AlertDialogDescription>
            This will lock all logs for {filterYear}, making them read-only. You can still view and export data. You can
            restore the year if needed.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={confirmArchiveYear}>
            <Archive className="h-4 w-4 mr-2 inline" />
            Confirm Archive
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );

  // Initialize the editable establishment info when summary data changes
  useEffect(() => {
    if (summaryData) {
      setEditableEstablishmentInfo({
        companyName: summaryData.companyName,
        facilityId: summaryData.facilityId,
        naicsCode: summaryData.naicsCode,
        ein: '12-3456789', // Default EIN value
        year: summaryData.year,
        totalEmployees: summaryData.totalEmployees,
        totalHoursWorked: summaryData.totalHoursWorked,
      });

      // Initialize calculated rates from the summary data
      setCalculatedRates({
        trcRate: summaryData.trcRate,
        dartRate: summaryData.dartRate,
      });
    }
  }, [summaryData]);

  // Separate click handler for the edit button to ensure form is populated
  const handleEditEstablishment = () => {
    // Ensure we populate with latest data
    if (summaryData) {
      setEditableEstablishmentInfo({
        companyName: summaryData.companyName,
        facilityId: summaryData.facilityId,
        naicsCode: summaryData.naicsCode,
        ein: '12-3456789', // Default EIN value
        year: summaryData.year,
        totalEmployees: summaryData.totalEmployees,
        totalHoursWorked: summaryData.totalHoursWorked,
      });
    }
    setIsEditingEstablishment(true);
  };

  // Apply filters to the OSHA logs
  const filteredLogs = useMemo(() => {
    if (!Array.isArray(oshaLogs)) return [];

    // If preview empty state is enabled, return empty array to show empty state
    if (previewEmptyState) {
      console.log('Preview empty state is enabled, returning empty array');
      return [];
    }

    // First filter the logs
    const filtered = oshaLogs.filter((log: OshaLogEntry) => {
      // Archive filter - only show archived logs if includeArchived is true
      if (log.archived && !includeArchived) {
        return false;
      }

      // Year filter
      if (filterYear && !log.logId.includes(filterYear)) {
        return false;
      }

      // Case Type filter
      if (filterType && filterType !== 'all' && log.type !== filterType) {
        return false;
      }

      // Status filter
      if (filterStatus && filterStatus !== 'all' && log.status !== filterStatus) {
        return false;
      }

      // Facility filter
      if (filterFacility && filterFacility !== 'all') {
        // In a real implementation, we would filter by facility
        // For now, we keep all records if the filter is for "all"
        return false;
      }

      // Privacy Cases filter
      if (!showPrivacyCases && log.privacyCase) {
        return false;
      }

      // Archived filter
      if (!includeArchived && log.status === 'Archived') {
        return false;
      }

      // Search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          log.title.toLowerCase().includes(query) ||
          log.employee.toLowerCase().includes(query) ||
          log.jobTitle.toLowerCase().includes(query) ||
          log.logId.toLowerCase().includes(query) ||
          log.location.toLowerCase().includes(query) ||
          (log.linkedSafetyEvent && log.linkedSafetyEvent.toLowerCase().includes(query))
        );
      }

      return true;
    });

    // Then sort the filtered logs
    return [...filtered].sort((a, b) => {
      // Handle different field types
      switch (sortField) {
        case 'dateTime':
          const dateA = new Date(a.dateTime);
          const dateB = new Date(b.dateTime);
          return sortDirection === 'asc' ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();

        case 'daysAwayFromWork':
          return sortDirection === 'asc'
            ? a.daysAwayFromWork - b.daysAwayFromWork
            : b.daysAwayFromWork - a.daysAwayFromWork;

        case 'daysRestricted':
          return sortDirection === 'asc' ? a.daysRestricted - b.daysRestricted : b.daysRestricted - a.daysRestricted;

        case 'type':
          return sortDirection === 'asc' ? a.type.localeCompare(b.type) : b.type.localeCompare(a.type);

        default:
          // Default sort by date (newest first)
          const defaultDateA = new Date(a.dateTime);
          const defaultDateB = new Date(b.dateTime);
          return defaultDateB.getTime() - defaultDateA.getTime();
      }
    });
  }, [
    oshaLogs,
    filterYear,
    filterType,
    filterStatus,
    filterFacility,
    showPrivacyCases,
    includeArchived,
    searchQuery,
    sortField,
    sortDirection,
    previewEmptyState,
  ]);

  const handleRowClick = (logId: string) => {
    // Find the OSHA form with this logId to get its ID
    if (Array.isArray(oshaLogs)) {
      const form = oshaLogs.find((log: OshaLogEntry) => log.logId === logId);
      if (form) {
        // Navigate to the OSHA 301 Report View with logId
        setLocation(`/osha/reports/${logId}`);
      } else {
        // Fallback to query parameter if the form is not found (should not happen)
        toast({
          title: 'Error',
          description: 'Could not find the requested OSHA form',
          variant: 'destructive',
        });
      }
    } else {
      toast({
        title: 'Loading...',
        description: 'Please wait while we load the OSHA forms data',
      });
    }
  };

  const handleExport300A = () => {
    if (!summaryData) {
      toast({
        title: 'Error',
        description: 'Summary data is not available. Please try again later.',
        variant: 'destructive',
      });
      return;
    }

    try {
      toast({
        title: 'Export initiated',
        description: 'OSHA Form 300A export started. Your file will download shortly.',
      });

      // Create a printable div for the OSHA Form 300A - optimized for single page
      const printDiv = document.createElement('div');
      printDiv.style.width = '8.5in';
      printDiv.style.height = '11in';
      printDiv.style.margin = '0 auto';
      printDiv.style.padding = '0.3in';
      printDiv.style.fontFamily = 'Arial, sans-serif';
      printDiv.style.fontSize = '9pt';
      printDiv.style.position = 'relative';
      printDiv.style.lineHeight = '1.2';

      // Form title section
      const title = document.createElement('div');
      title.style.textAlign = 'center';
      title.style.marginBottom = '0.1in';
      title.innerHTML = `
        <h1 style="font-size: 14pt; margin: 0;">OSHA Form 300A</h1>
        <h2 style="font-size: 11pt; margin: 2px 0; font-weight: normal;">Summary of Work-Related Injuries and Illnesses</h2>
        <p style="margin: 2px 0; font-size: 8pt;">All establishments covered by Part 1904 must complete this Summary page, even if no injuries or illnesses occurred during the year.</p>
        <p style="margin: 2px 0; font-size: 10pt;"><strong>Year: ${summaryData.year}</strong></p>
      `;
      printDiv.appendChild(title);

      // Establishment information section - compact
      const estInfo = document.createElement('div');
      estInfo.style.marginBottom = '0.1in';
      estInfo.style.borderTop = '1px solid #000';
      estInfo.style.borderBottom = '1px solid #000';
      estInfo.style.padding = '0.1in 0';
      estInfo.innerHTML = `
        <h3 style="font-size: 10pt; margin: 0 0 5px 0; font-weight: bold;">Establishment Information</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.15in; font-size: 8pt;">
          <div>
            <p style="margin: 0 0 3px 0;"><strong>Company Name:</strong> ${summaryData.companyName}</p>
            <p style="margin: 0 0 3px 0;"><strong>Facility ID:</strong> ${summaryData.facilityId}</p>
            <p style="margin: 0 0 3px 0;"><strong>NAICS Code:</strong> ${summaryData.naicsCode}</p>
          </div>
          <div>
            <p style="margin: 0 0 3px 0;"><strong>Annual Average Number of Employees:</strong> ${summaryData.totalEmployees}</p>
            <p style="margin: 0 0 3px 0;"><strong>Total Hours Worked by All Employees:</strong> ${summaryData.totalHoursWorked.toLocaleString()}</p>
          </div>
        </div>
      `;
      printDiv.appendChild(estInfo);

      // Summary of Work-Related Injuries and Illnesses - compact
      const summaryTable = document.createElement('div');
      summaryTable.style.marginBottom = '0.1in';
      summaryTable.innerHTML = `
        <h3 style="font-size: 10pt; margin: 0 0 5px 0; font-weight: bold;">Number of Cases</h3>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #000; font-size: 8pt;">
          <tr>
            <th style="border: 1px solid #000; padding: 3px; width: 50%; text-align: left;">Type of Case</th>
            <th style="border: 1px solid #000; padding: 3px; width: 50%; text-align: center;">Number</th>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 2px;">Deaths</td>
            <td style="border: 1px solid #000; padding: 2px; text-align: center;">${summaryData.totalDeaths}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 2px;">Cases with days away from work</td>
            <td style="border: 1px solid #000; padding: 2px; text-align: center;">${summaryData.totalDaysAway}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 2px;">Cases with job transfer or restriction</td>
            <td style="border: 1px solid #000; padding: 2px; text-align: center;">${summaryData.totalRestricted}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 2px;">Other recordable cases</td>
            <td style="border: 1px solid #000; padding: 2px; text-align: center;">${summaryData.totalOtherRecordable}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 2px; font-weight: bold;">Total</td>
            <td style="border: 1px solid #000; padding: 2px; text-align: center; font-weight: bold;">${summaryData.totalCases}</td>
          </tr>
        </table>
      `;
      printDiv.appendChild(summaryTable);

      // Injury Rates Section - compact
      const ratesSection = document.createElement('div');
      ratesSection.style.marginBottom = '0.1in';
      ratesSection.innerHTML = `
        <h3 style="font-size: 10pt; margin: 0 0 5px 0; font-weight: bold;">Injury and Illness Rates</h3>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #000; font-size: 8pt;">
          <tr>
            <th style="border: 1px solid #000; padding: 3px; width: 70%; text-align: left;">Rate</th>
            <th style="border: 1px solid #000; padding: 3px; width: 30%; text-align: center;">Value</th>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 2px;">Total Recordable Case Rate (per 200,000 hours)</td>
            <td style="border: 1px solid #000; padding: 2px; text-align: center;">${summaryData.trcRate.toFixed(2)}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #000; padding: 2px;">DART Rate (per 200,000 hours)</td>
            <td style="border: 1px solid #000; padding: 2px; text-align: center;">${summaryData.dartRate.toFixed(2)}</td>
          </tr>
        </table>
      `;
      printDiv.appendChild(ratesSection);

      // Certification Section - compact
      const certSection = document.createElement('div');
      certSection.style.marginBottom = '0.1in';
      certSection.style.borderTop = '1px solid #000';
      certSection.style.padding = '0.1in 0';
      certSection.innerHTML = `
        <h3 style="font-size: 10pt; margin: 0 0 5px 0; font-weight: bold;">Certification</h3>
        <p style="margin: 0 0 8px 0; font-size: 8pt;">I certify that I have examined this document and that to the best of my knowledge the entries are true, accurate, and complete.</p>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.15in; font-size: 8pt;">
          <div>
            <p style="margin: 0 0 8px 0;"><strong>Company Executive:</strong> ${certificationData.executiveName || '________________________'}</p>
            <p style="margin: 0 0 3px 0;"><strong>Title:</strong> ${certificationData.executiveTitle || '________________________'}</p>
          </div>
          <div>
            <p style="margin: 0 0 5px 0;"><strong>Signature:</strong></p>
            <div style="border: 1px solid #ccc; width: 200px; height: 50px; margin-bottom: 5px; padding: 2px; background: white;">
              ${
                certificationData.signature
                  ? `<img src="${certificationData.signature}" style="max-width: 100%; max-height: 100%; object-fit: contain;" />`
                  : '<div style="height: 100%; border-bottom: 1px solid #000; margin-top: 40px;"></div>'
              }
            </div>
            <p style="margin: 0 0 3px 0;"><strong>Date:</strong> ${certificationData.dateCertified || '________________________'}</p>
          </div>
        </div>
      `;
      printDiv.appendChild(certSection);

      // Footer note
      const footerNote = document.createElement('div');
      footerNote.style.fontSize = '8pt';
      footerNote.style.color = '#666';
      footerNote.style.position = 'absolute';
      footerNote.style.bottom = '0.25in';
      footerNote.style.left = '0.5in';
      footerNote.style.right = '0.5in';
      footerNote.innerHTML = `
        <p style="margin: 0;">Post this Summary page from February 1 to April 30 of the year following the year covered by the form.</p>
        <p style="margin: 5px 0 0 0;">Generated by UpKeep EHS on ${new Date().toLocaleDateString()}</p>
      `;
      printDiv.appendChild(footerNote);

      // Add to document, print, then remove
      document.body.appendChild(printDiv);

      const originalContents = document.body.innerHTML;
      const originalTitle = document.title;

      // Set a clean title for the PDF and add print styles
      document.title = `OSHA Form 300A - ${summaryData.year}`;

      // Add CSS to hide browser headers/footers
      const printStyle = document.createElement('style');
      printStyle.innerHTML = `
        @media print {
          @page {
            margin: 0.5in;
            size: letter;
          }
          body {
            margin: 0;
            padding: 0;
          }
        }
      `;
      document.head.appendChild(printStyle);

      document.body.innerHTML = printDiv.innerHTML;

      setTimeout(() => {
        window.print();
        document.body.innerHTML = originalContents;
        document.title = originalTitle;

        // Remove the added print style
        document.head.removeChild(printStyle);

        toast({
          title: 'Export complete',
          description: 'OSHA Form 300A has been sent to the printer or saved as PDF.',
          duration: 3000,
        });
      }, 500);
    } catch (error) {
      console.error('Error exporting OSHA Form 300A:', error);
      toast({
        title: 'Export failed',
        description: 'There was an error exporting the OSHA Form 300A.',
        variant: 'destructive',
      });
    }
  };

  // Format a date string for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MM/dd/yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  };

  // Handle column sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle direction if already sorting by this field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort field with default desc direction
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Get sort direction indicator for column header
  const getSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };

  // Handle privacy case display
  const getPrivacyAdjustedValue = (log: OshaLogEntry, field: 'employee' | 'jobTitle' | 'location'): string => {
    if (log.privacyCase && !showPrivacyCases) {
      // Mask data based on field type
      if (field === 'employee') return 'Unknown Employee';
      return 'Restricted';
    }
    return log[field];
  };

  // Get badge color based on case type
  const getCaseTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'Death':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'Days Away':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'Restricted':
        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
      case 'Other Recordable':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Get badge color based on status
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Draft':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      case 'Submitted':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'Archived':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Create OSHA Record function
  const handleCreateOshaRecord = () => {
    setLocation('/osha-form-301');
  };

  // Empty State Component
  const EmptyStateComponent = () => (
    <div className="flex flex-col items-center justify-center py-16 px-6">
      <div className="text-center max-w-md">
        <div className="text-4xl mb-4">📝</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No OSHA Records Yet</h3>
        <p className="text-gray-600 mb-6 leading-relaxed">
          It looks like you haven't recorded any work-related injuries or illnesses for {filterYear}. Start by creating
          your first OSHA record to maintain compliance with workplace safety requirements.
        </p>
        <div className="space-y-4">
          <Button onClick={handleCreateOshaRecord} className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2">
            <Plus className="h-4 w-4 mr-2" />
            Create New OSHA Record
          </Button>
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-1">Need to learn more about OSHA recordkeeping?</p>
            <a
              href="https://www.osha.gov/recordkeeping"
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-primary-600 hover:text-primary-800 underline"
            >
              View OSHA Guidelines
            </a>
          </div>
        </div>
      </div>
    </div>
  );

  // Render mobile card view for OSHA logs
  const renderMobileCard = (log: OshaLogEntry) => {
    return (
      <Card
        key={log.id}
        className="mb-4 cursor-pointer transition-colors hover:bg-muted/50"
        onClick={() => handleRowClick(log.logId)}
      >
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div className="flex items-center">
              <p className="font-medium text-sm mr-2">{log.logId}</p>
            </div>
          </div>

          <h3 className="font-medium mb-2 line-clamp-2">{log.title}</h3>

          <div className="text-sm text-muted-foreground mb-3">
            <div className="flex items-center mb-1">
              <Badge className={getCaseTypeBadgeColor(log.type)}>{log.type}</Badge>
              <span className="flex items-center ml-2">
                <Calendar className="h-3 w-3 mr-1" />
                {formatDate(log.dateTime)}
              </span>
            </div>

            <div className="flex items-center mb-1">
              <User className="h-3 w-3 mr-1" />
              {getPrivacyAdjustedValue(log, 'employee')}
            </div>

            <div className="flex items-center mb-1">
              <MapPin className="h-3 w-3 mr-1" />
              {getPrivacyAdjustedValue(log, 'location')}
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Badge className={getStatusBadgeColor(log.status)}>{log.status}</Badge>
              {log.oshaReportable && (
                <Badge className="bg-red-50 text-red-600 border-red-200" variant="outline">
                  OSHA
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleRowClick(log.logId);
              }}
            >
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Archive confirmation modal */}
      <AlertDialog open={showArchiveModal} onOpenChange={setShowArchiveModal}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Archive OSHA Logs for {filterYear}?</AlertDialogTitle>
            <AlertDialogDescription>
              This will lock all logs for {filterYear}, making them read-only. You can still view and export data. You
              can restore the year if needed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmArchiveYear}>
              <Archive className="h-4 w-4 mr-2 inline" />
              Confirm Archive
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Pre-Download Confirmation Modal */}
      <AlertDialog open={showPreDownloadModal} onOpenChange={setShowPreDownloadModal}>
        <AlertDialogContent className="max-w-3xl">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-2xl font-bold text-gray-900 mb-4">
              Important: Confirm OSHA Report Generation & Submission
            </AlertDialogTitle>
            <AlertDialogDescription className="text-base leading-relaxed space-y-6">
              {/* Certification & Responsibility Section */}
              <div className="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-r-lg">
                <div className="font-bold text-blue-800 text-lg mb-3">📋 Your Responsibility & Certification</div>
                <div className="text-blue-900 space-y-3">
                  <p className="font-semibold mb-4">Please affirm the following statements before proceeding:</p>
                  <ul className="list-disc pl-5 space-y-3">
                    <li>
                      <strong>
                        I certify that the information I have entered into this system is accurate and complete to the
                        best of my knowledge.
                      </strong>{' '}
                      (This links to the executive certification on the 300A)
                    </li>
                    <li>
                      <strong>
                        I understand that UpKeep provides this data in an applicable format for my convenience, but I
                        remain solely responsible for the accuracy and completeness of the data, and to review that the
                        form is formatted correctly, prior to submission.
                      </strong>
                    </li>
                    <li>
                      <strong>
                        I understand that UpKeep is not responsible for the actual electronic submission to OSHA's
                        Injury Tracking Application (ITA). I am solely responsible for uploading the generated file and
                        ensuring full compliance with all applicable OSHA regulations and deadlines.
                      </strong>
                    </li>
                    <li>
                      <strong>I have reviewed all data carefully, and agree to these terms before proceeding.</strong>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Mandatory Agreement Checkbox */}
              <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="certification-agreement"
                    checked={certificationAgreed}
                    onCheckedChange={(checked) => setCertificationAgreed(checked === true)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="certification-agreement"
                      className="text-sm font-medium text-amber-900 cursor-pointer leading-relaxed"
                    >
                      I accept and agree with the statements above.
                    </label>
                  </div>
                </div>
              </div>

              {/* Action Confirmation Section */}
              <div className="bg-blue-50 border border-blue-200 p-6 rounded-lg">
                <div className="font-semibold text-blue-900 text-lg mb-3">What Happens Next</div>
                <div className="text-blue-800 space-y-3">
                  <p>
                    Upon clicking <strong>'Generate & Download Report'</strong>, a CSV file containing your OSHA Form
                    300A data will be generated and downloaded to your device.
                  </p>
                  <p>
                    <strong>File Name:</strong> osha_300a_summary_{summaryData?.year || '2025'}.csv
                  </p>
                  <p>
                    <strong>Next Step:</strong> You must upload this file to OSHA's official Injury Tracking Application
                    (ITA) website by March 2nd for your applicable reporting year.
                  </p>
                </div>
              </div>

              {/* Important Reminders */}
              <div className="bg-gray-50 border border-gray-200 p-6 rounded-lg">
                <div className="font-semibold text-gray-800 text-lg mb-3">Important Reminders</div>
                <div className="text-gray-700 space-y-2">
                  <p>• Review all data carefully before proceeding with submission</p>
                  <p>• Ensure your company executive has certified the information</p>
                  <p>• Keep records of your submission for compliance purposes</p>
                  <p>• The submission deadline is annually, March 2nd</p>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="gap-4 pt-6">
            <AlertDialogCancel
              onClick={() => {
                setShowPreDownloadModal(false);
                setCertificationAgreed(false); // Reset checkbox when modal is closed
              }}
              className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 border-gray-300"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleGenerateAndDownloadReport}
              disabled={!certificationAgreed}
              className={`px-8 py-3 font-semibold shadow-lg ${
                certificationAgreed
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              <FileText className="h-4 w-4 mr-2" />
              Generate & Download Report
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Serious Incident Reporting Modal */}
      <AlertDialog open={showSeriousIncidentModal} onOpenChange={setShowSeriousIncidentModal}>
        <AlertDialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-2xl font-bold text-red-700 mb-4">
              <AlertTriangle className="h-6 w-6 inline mr-2" />
              Report Serious Incident to Regulatory Agencies
            </AlertDialogTitle>

            {/* Critical Deadline Warnings */}
            <div className="bg-red-50 border-l-4 border-red-500 p-6 rounded-r-lg mb-6">
              <div className="font-bold text-red-800 text-xl mb-3">🚨 IMMEDIATE REPORTING REQUIRED</div>
              <div className="text-red-900 space-y-2 font-semibold">
                <p>FATALITIES, HOSPITALIZATIONS, AMPUTATIONS, LOSS OF AN EYE MUST BE REPORTED TO OSHA IMMEDIATELY</p>
                <p>
                  • Federal OSHA: Within 8 hours for fatalities, inpatient hospitalizations, amputations, or loss of an
                  eye
                </p>
                <p>
                  • California (Cal/OSHA) & Other State Plans: Report immediately (as soon as feasible, no longer than 8
                  hours)
                </p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-blue-50 border border-blue-200 p-6 rounded-lg mb-6">
              <div className="font-semibold text-blue-900 text-lg mb-3">How to Report - Contact OSHA Now:</div>
              <div className="text-blue-800 space-y-2">
                <p className="font-semibold">📞 Call OSHA toll-free at 1-800-321-OSHA (6742)</p>
                <p>• Or contact your nearest OSHA Area Office directly</p>
                <p>• For State Plans (e.g., Cal/OSHA), contact your specific state agency</p>
                <p>
                  <a
                    href="https://www.osha.gov/severeinjury/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline font-semibold"
                  >
                    🔗 OSHA Severe Injury Reporting Guidelines →
                  </a>
                </p>
              </div>
            </div>
          </AlertDialogHeader>

          <AlertDialogDescription className="text-base">
            <div className="space-y-6">
              {/* Form Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Incident ID */}
                <div>
                  <Label htmlFor="incident-id" className="text-sm font-semibold text-gray-700">
                    Incident ID
                  </Label>
                  <Input
                    id="incident-id"
                    value={seriousIncidentData.incidentId}
                    readOnly
                    className="mt-1 bg-gray-50 font-mono"
                  />
                </div>

                {/* Date of Incident */}
                <div>
                  <Label htmlFor="date-incident" className="text-sm font-semibold text-gray-700">
                    Date of Incident*
                  </Label>
                  <Input
                    id="date-incident"
                    type="date"
                    value={seriousIncidentData.dateOfIncident}
                    onChange={(e) => setSeriousIncidentData({ ...seriousIncidentData, dateOfIncident: e.target.value })}
                    className={`mt-1 ${formErrors.dateOfIncident ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  {formErrors.dateOfIncident && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.dateOfIncident}</p>
                  )}
                </div>

                {/* Time of Incident */}
                <div>
                  <Label htmlFor="time-incident" className="text-sm font-semibold text-gray-700">
                    Time of Incident*
                  </Label>
                  <Input
                    id="time-incident"
                    type="time"
                    value={seriousIncidentData.timeOfIncident}
                    onChange={(e) => setSeriousIncidentData({ ...seriousIncidentData, timeOfIncident: e.target.value })}
                    className={`mt-1 ${formErrors.timeOfIncident ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  {formErrors.timeOfIncident && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.timeOfIncident}</p>
                  )}
                </div>

                {/* Location */}
                <div>
                  <Label htmlFor="location" className="text-sm font-semibold text-gray-700">
                    Location of Incident*
                  </Label>
                  <Input
                    id="location"
                    placeholder="e.g., Plant 3, Assembly Line"
                    value={seriousIncidentData.location}
                    onChange={(e) => setSeriousIncidentData({ ...seriousIncidentData, location: e.target.value })}
                    className={`mt-1 ${formErrors.location ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  {formErrors.location && <p className="text-red-500 text-sm mt-1">{formErrors.location}</p>}
                </div>
              </div>

              {/* Type of Serious Incident */}
              <div>
                <Label className="text-sm font-semibold text-gray-700 mb-3 block">Type of Serious Incident*</Label>
                <div
                  className={`grid grid-cols-2 gap-3 ${formErrors.incidentType ? 'border border-red-500 rounded p-3' : ''}`}
                >
                  {['Fatality', 'Inpatient Hospitalization', 'Amputation', 'Loss of an Eye'].map((type) => (
                    <label key={type} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="radio"
                        name="incidentType"
                        value={type}
                        checked={seriousIncidentData.incidentType === type}
                        onChange={(e) =>
                          setSeriousIncidentData({ ...seriousIncidentData, incidentType: e.target.value })
                        }
                        className="text-red-600"
                      />
                      <span className="text-sm">{type}</span>
                    </label>
                  ))}
                </div>
                {formErrors.incidentType && <p className="text-red-500 text-sm mt-1">{formErrors.incidentType}</p>}
              </div>

              {/* Conditional Fields */}
              {seriousIncidentData.incidentType === 'Fatality' && (
                <div>
                  <Label htmlFor="num-fatalities" className="text-sm font-semibold text-gray-700">
                    Number of Fatalities*
                  </Label>
                  <Input
                    id="num-fatalities"
                    type="number"
                    min="1"
                    value={seriousIncidentData.numFatalities}
                    onChange={(e) =>
                      setSeriousIncidentData({ ...seriousIncidentData, numFatalities: parseInt(e.target.value) || 0 })
                    }
                    className={`mt-1 w-32 ${formErrors.numFatalities ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  {formErrors.numFatalities && <p className="text-red-500 text-sm mt-1">{formErrors.numFatalities}</p>}
                </div>
              )}

              {seriousIncidentData.incidentType === 'Inpatient Hospitalization' && (
                <div>
                  <Label htmlFor="num-hospitalized" className="text-sm font-semibold text-gray-700">
                    Number of Hospitalized Employees*
                  </Label>
                  <Input
                    id="num-hospitalized"
                    type="number"
                    min="1"
                    value={seriousIncidentData.numHospitalized}
                    onChange={(e) =>
                      setSeriousIncidentData({ ...seriousIncidentData, numHospitalized: parseInt(e.target.value) || 0 })
                    }
                    className={`mt-1 w-32 ${formErrors.numHospitalized ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  {formErrors.numHospitalized && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.numHospitalized}</p>
                  )}
                </div>
              )}

              {/* Description */}
              <div>
                <Label htmlFor="description" className="text-sm font-semibold text-gray-700">
                  Brief Description of Incident*
                </Label>
                <textarea
                  id="description"
                  placeholder="What happened and how it happened..."
                  value={seriousIncidentData.description}
                  onChange={(e) => setSeriousIncidentData({ ...seriousIncidentData, description: e.target.value })}
                  className={`mt-1 w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${formErrors.description ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'}`}
                  rows={4}
                  required
                />
                {formErrors.description && <p className="text-red-500 text-sm mt-1">{formErrors.description}</p>}
              </div>

              {/* Additional Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="employees-involved" className="text-sm font-semibold text-gray-700">
                    Employee(s) Involved (Optional)
                  </Label>
                  <Input
                    id="employees-involved"
                    placeholder="Comma-separated names"
                    value={seriousIncidentData.employeesInvolved}
                    onChange={(e) =>
                      setSeriousIncidentData({ ...seriousIncidentData, employeesInvolved: e.target.value })
                    }
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="contact-person" className="text-sm font-semibold text-gray-700">
                    Company Contact Person for OSHA*
                  </Label>
                  <Input
                    id="contact-person"
                    placeholder="Contact person name"
                    value={seriousIncidentData.contactPerson}
                    onChange={(e) => setSeriousIncidentData({ ...seriousIncidentData, contactPerson: e.target.value })}
                    className={`mt-1 ${formErrors.contactPerson ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  {formErrors.contactPerson && <p className="text-red-500 text-sm mt-1">{formErrors.contactPerson}</p>}
                </div>

                <div>
                  <Label htmlFor="contact-phone" className="text-sm font-semibold text-gray-700">
                    Contact Person's Phone*
                  </Label>
                  <Input
                    id="contact-phone"
                    placeholder="(*************"
                    value={seriousIncidentData.contactPhone}
                    onChange={(e) => setSeriousIncidentData({ ...seriousIncidentData, contactPhone: e.target.value })}
                    className={`mt-1 ${formErrors.contactPhone ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                  {formErrors.contactPhone && <p className="text-red-500 text-sm mt-1">{formErrors.contactPhone}</p>}
                </div>

                <div>
                  <Label htmlFor="date-prepared" className="text-sm font-semibold text-gray-700">
                    Date/Time Prepared
                  </Label>
                  <Input
                    id="date-prepared"
                    type="datetime-local"
                    value={seriousIncidentData.datePrepared}
                    readOnly
                    className="mt-1 bg-gray-50"
                  />
                </div>
              </div>
            </div>
          </AlertDialogDescription>

          <AlertDialogFooter className="gap-4 pt-6">
            <AlertDialogCancel
              onClick={() => setShowSeriousIncidentModal(false)}
              className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 border-gray-300"
            >
              Cancel
            </AlertDialogCancel>
            <Button
              onClick={(e) => {
                e.preventDefault();
                handleSeriousIncidentSubmit();
              }}
              className="px-8 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold shadow-lg"
            >
              <FileText className="h-4 w-4 mr-2" />
              Save & Mark as Reported
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="space-y-3">
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">OSHA Logs</h1>
          <p className="text-lg text-gray-600">Manage and track OSHA recordable incidents</p>
          {isYearArchived && (
            <Badge className="mt-2 bg-gray-100 text-gray-800 px-3 py-1">
              <Archive className="h-3 w-3 mr-1 inline" />
              Archived
            </Badge>
          )}
        </div>

        {/* Consolidated Top-Right Action Area */}
        <div className="mt-6 md:mt-0 flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
          {/* Compact Search Bar */}
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search OSHA logs..."
              className="pl-10 h-10 bg-white border-gray-200 shadow-sm rounded-lg text-sm placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Year Dropdown */}
          <div className="flex items-center gap-2 bg-gray-50 px-3 py-2 rounded-lg border border-gray-200 min-w-fit">
            <Label htmlFor="year-filter" className="text-sm font-medium text-gray-700 whitespace-nowrap">
              Year:
            </Label>
            <Select value={filterYear} onValueChange={setFilterYear}>
              <SelectTrigger className="w-20 h-6 border-0 bg-white shadow-sm text-sm p-1" id="year-filter">
                <SelectValue placeholder="Year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2025">2025</SelectItem>
                <SelectItem value="2024">2024</SelectItem>
                <SelectItem value="2023">2023</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Create OSHA Record Button */}
          <Button
            onClick={handleCreateOshaRecord}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 h-10 font-medium shadow-sm flex items-center gap-2 whitespace-nowrap"
            disabled={isYearArchived}
          >
            <Plus className="h-4 w-4" />
            Create OSHA Record
          </Button>
        </div>
      </div>

      {/* Archive banner for past years */}
      {showArchiveBanner && (
        <Alert className="mb-4 bg-yellow-50 border-yellow-200">
          <AlertCircle className="h-4 w-4 text-yellow-600" />
          <AlertTitle className="text-yellow-800 flex items-center justify-between">
            <span>
              <Archive className="h-4 w-4 mr-1 inline" /> You're viewing OSHA logs for {filterYear}
            </span>
            <Button variant="ghost" size="sm" className="ml-auto p-1 h-auto" onClick={() => setDismissedBanner(true)}>
              <X className="h-4 w-4" />
            </Button>
          </AlertTitle>
          <AlertDescription className="text-yellow-700 flex items-center flex-wrap">
            <span>Would you like to archive this year's records now that reporting is complete?</span>
            <Button
              variant="outline"
              size="sm"
              className="ml-2 mt-1 bg-white hover:bg-yellow-100"
              onClick={handleArchiveYear}
            >
              Archive Year {filterYear}
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Archived status banner - Enhanced prominence */}
      {isYearArchived && (
        <Alert className="mb-6 bg-gradient-to-r from-red-50 to-orange-50 border-2 border-red-400 shadow-lg">
          <Archive className="h-6 w-6 text-red-600" />
          <AlertTitle className="text-red-800 text-lg font-bold flex items-center">
            <Archive className="h-5 w-5 mr-2" />
            ARCHIVED - This year's OSHA logs are archived and read-only
          </AlertTitle>
          <AlertDescription className="text-red-700 font-medium text-base mt-2">
            These records cannot be modified. To edit current year data, please navigate back to the main view.
            <Button variant="outline" size="sm" className="ml-2 mt-1" onClick={handleRestoreYear}>
              Restore Year
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="log" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6 bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 shadow-md rounded-lg p-1 h-12">
          <TabsTrigger
            value="log"
            className="px-5 py-2.5 rounded-md font-semibold text-sm transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-blue-200 text-gray-600 hover:text-gray-800 hover:bg-white/70"
          >
            <FileText className="h-4 w-4 mr-2" />
            OSHA Log (Form 300)
          </TabsTrigger>
          <TabsTrigger
            value="summary"
            className="px-5 py-2.5 rounded-md font-semibold text-sm transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-blue-200 text-gray-600 hover:text-gray-800 hover:bg-white/70"
          >
            <Calendar className="h-4 w-4 mr-2" />
            Summary (Form 300A)
          </TabsTrigger>

          <TabsTrigger
            value="agency"
            className="px-5 py-2.5 rounded-md font-semibold text-sm transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-blue-200 text-gray-600 hover:text-gray-800 hover:bg-white/70"
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Agency Reports
          </TabsTrigger>
        </TabsList>

        {/* OSHA Log (Form 300) Tab */}
        <TabsContent value="log">
          {/* Horizontal Filter Bar */}
          <div className="mb-6 overflow-x-auto pb-2 hidden md:block">
            <div className="flex flex-wrap gap-3 items-center">
              {/* Case Type Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 gap-1">
                    Case Type
                    <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                      {filterType && filterType !== 'all' ? filterType : 'All'}
                    </Badge>
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-[180px]">
                  <DropdownMenuLabel>Filter by Case Type</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {['All Types', 'Death', 'Days Away', 'Restricted', 'Other Recordable'].map((type) => (
                    <DropdownMenuItem
                      key={type}
                      className="flex items-center gap-2"
                      onSelect={() => setFilterType(type === 'All Types' ? '' : type)}
                    >
                      <div
                        className={`h-4 w-4 border rounded-sm flex items-center justify-center ${
                          (type === 'All Types' && !filterType) || filterType === type
                            ? 'bg-primary border-primary text-primary-foreground'
                            : 'border-primary/20'
                        }`}
                      >
                        {((type === 'All Types' && !filterType) || filterType === type) && (
                          <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                            <path
                              d="M8.33334 2.5L3.75 7.08333L1.66667 5"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        )}
                      </div>
                      <span>{type}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Status Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 gap-1">
                    Status
                    <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                      {filterStatus && filterStatus !== 'all' ? filterStatus : 'All'}
                    </Badge>
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-[180px]">
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {['All Statuses', 'Draft', 'Submitted', 'Archived'].map((status) => (
                    <DropdownMenuItem
                      key={status}
                      className="flex items-center gap-2"
                      onSelect={() => setFilterStatus(status === 'All Statuses' ? '' : status)}
                    >
                      <div
                        className={`h-4 w-4 border rounded-sm flex items-center justify-center ${
                          (status === 'All Statuses' && !filterStatus) || filterStatus === status
                            ? 'bg-primary border-primary text-primary-foreground'
                            : 'border-primary/20'
                        }`}
                      >
                        {((status === 'All Statuses' && !filterStatus) || filterStatus === status) && (
                          <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                            <path
                              d="M8.33334 2.5L3.75 7.08333L1.66667 5"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        )}
                      </div>
                      <span>{status}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Location Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 gap-1">
                    Location
                    <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                      {filterFacility || 'All'}
                    </Badge>
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-[180px]">
                  <DropdownMenuLabel>Filter by Location</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {['All Locations', 'Assembly Line', 'Warehouse', 'Office', 'Production Floor', 'Loading Dock'].map(
                    (location) => (
                      <DropdownMenuItem
                        key={location}
                        className="flex items-center gap-2"
                        onSelect={() => setFilterFacility(location === 'All Locations' ? '' : location)}
                      >
                        <div
                          className={`h-4 w-4 border rounded-sm flex items-center justify-center ${
                            (location === 'All Locations' && !filterFacility) || filterFacility === location
                              ? 'bg-primary border-primary text-primary-foreground'
                              : 'border-primary/20'
                          }`}
                        >
                          {((location === 'All Locations' && !filterFacility) || filterFacility === location) && (
                            <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                              <path
                                d="M8.33334 2.5L3.75 7.08333L1.66667 5"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          )}
                        </div>
                        <span>{location}</span>
                      </DropdownMenuItem>
                    ),
                  )}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Sort By Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 gap-1">
                    Sort By
                    <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                      {sortField === 'dateTime' && sortDirection === 'desc'
                        ? 'Newest'
                        : sortField === 'dateTime' && sortDirection === 'asc'
                          ? 'Oldest'
                          : sortField === 'employee'
                            ? 'Employee A-Z'
                            : 'Custom'}
                    </Badge>
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-[180px]">
                  <DropdownMenuLabel>Sort Order</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { label: 'Newest First', field: 'dateTime', direction: 'desc' },
                    { label: 'Oldest First', field: 'dateTime', direction: 'asc' },
                    { label: 'Employee A-Z', field: 'employee', direction: 'asc' },
                    { label: 'Days Away Desc', field: 'daysAwayFromWork', direction: 'desc' },
                  ].map((sort) => (
                    <DropdownMenuItem
                      key={sort.label}
                      className="flex items-center gap-2"
                      onSelect={() => {
                        setSortField(sort.field);
                        setSortDirection(sort.direction as 'asc' | 'desc');
                      }}
                    >
                      <div
                        className={`h-4 w-4 border rounded-sm flex items-center justify-center ${
                          sortField === sort.field && sortDirection === sort.direction
                            ? 'bg-primary border-primary text-primary-foreground'
                            : 'border-primary/20'
                        }`}
                      >
                        {sortField === sort.field && sortDirection === sort.direction && (
                          <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                            <path
                              d="M8.33334 2.5L3.75 7.08333L1.66667 5"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        )}
                      </div>
                      <span>{sort.label}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Privacy Cases Toggle */}
              <div className="flex items-center gap-2 px-3 py-2">
                <Switch
                  id="show-privacy-cases"
                  checked={showPrivacyCases}
                  onCheckedChange={setShowPrivacyCases}
                  className="data-[state=checked]:bg-primary"
                />
                <Label htmlFor="show-privacy-cases" className="text-sm font-medium cursor-pointer">
                  Show Privacy Cases
                </Label>
              </div>

              {/* Include Archived Toggle */}
              <div className="flex items-center gap-2 px-3 py-2">
                <Switch
                  id="include-archived"
                  checked={includeArchived}
                  onCheckedChange={setIncludeArchived}
                  className="data-[state=checked]:bg-primary"
                />
                <Label htmlFor="include-archived" className="text-sm font-medium cursor-pointer">
                  Include Archived
                </Label>
              </div>

              {/* Clear Filters Button */}
              {(filterType || filterStatus || filterFacility || !showPrivacyCases || includeArchived) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setFilterType('');
                    setFilterStatus('');

                    setFilterFacility('');
                    setShowPrivacyCases(true);
                    setIncludeArchived(false);
                    setSortField('dateTime');
                    setSortDirection('desc');
                  }}
                  className="h-9 text-gray-600 hover:text-gray-900"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear Filters
                </Button>
              )}
            </div>
          </div>

          {/* Mobile Filter Button */}
          <div className="md:hidden mb-4">
            {/* Filters Row */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              {/* Mobile Filter Button */}
              {isMobile ? (
                <div className="flex justify-between items-center w-full">
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button variant="outline" className="flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        Filter
                        {(filterType || filterStatus || !showPrivacyCases || includeArchived) && (
                          <Badge variant="secondary" className="ml-1 px-1.5 py-0.5 text-xs">
                            {
                              [
                                filterType,
                                filterStatus,
                                !showPrivacyCases && 'Privacy',
                                includeArchived && 'Archived',
                              ].filter(Boolean).length
                            }
                          </Badge>
                        )}
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="bottom" className="max-h-[80vh]">
                      <SheetHeader>
                        <SheetTitle>Filter OSHA Logs</SheetTitle>
                        <SheetDescription>Choose filters to narrow down your view</SheetDescription>
                      </SheetHeader>
                      <div className="space-y-6 py-6">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Case Type</Label>
                          <Select value={filterType} onValueChange={setFilterType}>
                            <SelectTrigger>
                              <SelectValue placeholder="All case types" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Types</SelectItem>
                              <SelectItem value="Death">Death</SelectItem>
                              <SelectItem value="Days Away">Days Away</SelectItem>
                              <SelectItem value="Restricted">Restricted</SelectItem>
                              <SelectItem value="Other Recordable">Other Recordable</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Status</Label>
                          <Select value={filterStatus} onValueChange={setFilterStatus}>
                            <SelectTrigger>
                              <SelectValue placeholder="All statuses" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All Statuses</SelectItem>
                              <SelectItem value="Draft">Draft</SelectItem>
                              <SelectItem value="Submitted">Submitted</SelectItem>
                              <SelectItem value="Archived">Archived</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-3">
                          <Label className="text-sm font-medium">Display Options</Label>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <Label htmlFor="show-privacy-mobile" className="text-sm">
                                Show Privacy Cases
                              </Label>
                              <Switch
                                id="show-privacy-mobile"
                                checked={showPrivacyCases}
                                onCheckedChange={setShowPrivacyCases}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="include-archived-mobile" className="text-sm">
                                Include Archived
                              </Label>
                              <Switch
                                id="include-archived-mobile"
                                checked={includeArchived}
                                onCheckedChange={setIncludeArchived}
                              />
                            </div>
                          </div>
                        </div>

                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => {
                            setFilterType('all');
                            setFilterStatus('all');

                            setShowPrivacyCases(true);
                            setIncludeArchived(false);
                          }}
                        >
                          Clear All Filters
                        </Button>
                      </div>
                    </SheetContent>
                  </Sheet>

                  <div className="text-sm text-muted-foreground">
                    {filteredLogs.length} result{filteredLogs.length !== 1 ? 's' : ''}
                  </div>
                </div>
              ) : (
                /* Desktop Filters */
                <>
                  <div className="flex flex-wrap gap-2">
                    <Select value={filterType} onValueChange={setFilterType}>
                      <SelectTrigger className="w-36">
                        <SelectValue placeholder="Case Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="Death">Death</SelectItem>
                        <SelectItem value="Days Away">Days Away</SelectItem>
                        <SelectItem value="Restricted">Restricted</SelectItem>
                        <SelectItem value="Other Recordable">Other Recordable</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={filterStatus} onValueChange={setFilterStatus}>
                      <SelectTrigger className="w-36">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="Draft">Draft</SelectItem>
                        <SelectItem value="Submitted">Submitted</SelectItem>
                        <SelectItem value="Archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>

                    <div className="flex items-center space-x-2">
                      <Switch id="show-privacy" checked={showPrivacyCases} onCheckedChange={setShowPrivacyCases} />
                      <Label htmlFor="show-privacy" className="text-sm">
                        Privacy Cases
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch id="include-archived" checked={includeArchived} onCheckedChange={setIncludeArchived} />
                      <Label htmlFor="include-archived" className="text-sm">
                        Include Archived
                      </Label>
                    </div>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    {filteredLogs.length} result{filteredLogs.length !== 1 ? 's' : ''}
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Content Area */}
          {isMobile ? (
            /* Mobile View - Cards outside component like incidents */
            <div className="space-y-4">
              {isLoadingLogs ? (
                <div className="flex justify-center py-8">
                  <div className="animate-pulse text-muted-foreground">Loading OSHA logs...</div>
                </div>
              ) : filteredLogs.length === 0 ? (
                <EmptyStateComponent />
              ) : (
                filteredLogs.map((log: OshaLogEntry) => renderMobileCard(log))
              )}
            </div>
          ) : (
            /* Desktop View - Table in Card */
            <Card className="shadow-lg border-0 bg-white">
              <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white px-6 py-5">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-bold flex items-center text-gray-900">
                    <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg mr-3">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                    OSHA Form 300 Log of Work-Related Injuries and Illnesses
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportLog}
                    className="bg-white border-gray-300 hover:bg-gray-50 text-gray-700 font-medium"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export Log
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <ScrollArea className="max-h-[600px] w-full">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50 border-b-2 border-gray-100">
                        <TableHead
                          className="cursor-pointer font-semibold text-gray-700 py-4 px-6 hover:bg-gray-100 transition-colors"
                          onClick={() => handleSort('logId')}
                        >
                          OSHA Log ID {getSortIndicator('logId')}
                        </TableHead>
                        <TableHead className="font-semibold text-gray-700 py-4 px-4">Employee</TableHead>
                        <TableHead className="font-semibold text-gray-700 py-4 px-4">Job Title</TableHead>
                        <TableHead
                          className="cursor-pointer font-semibold text-gray-700 py-4 px-4 hover:bg-gray-100 transition-colors"
                          onClick={() => handleSort('type')}
                        >
                          Case Type {getSortIndicator('type')}
                        </TableHead>
                        <TableHead className="text-center font-semibold text-gray-700 py-4 px-4">Fatality</TableHead>
                        <TableHead
                          className="cursor-pointer font-semibold text-gray-700 py-4 px-4 hover:bg-gray-100 transition-colors"
                          onClick={() => handleSort('dateTime')}
                        >
                          Date {getSortIndicator('dateTime')}
                        </TableHead>
                        <TableHead className="font-semibold text-gray-700 py-4 px-4">Location</TableHead>
                        <TableHead
                          className="cursor-pointer font-semibold text-gray-700 py-4 px-4 text-center hover:bg-gray-100 transition-colors"
                          onClick={() => handleSort('daysAwayFromWork')}
                        >
                          Days Away {getSortIndicator('daysAwayFromWork')}
                        </TableHead>
                        <TableHead
                          className="cursor-pointer font-semibold text-gray-700 py-4 px-4 text-center hover:bg-gray-100 transition-colors"
                          onClick={() => handleSort('daysRestricted')}
                        >
                          Days Restricted {getSortIndicator('daysRestricted')}
                        </TableHead>
                        <TableHead className="text-center font-semibold text-gray-700 py-4 px-4">Reportable</TableHead>
                        <TableHead className="font-semibold text-gray-700 py-4 px-4">Status</TableHead>
                        <TableHead className="text-center font-semibold text-gray-700 py-4 px-4">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoadingLogs ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-4">
                            Loading OSHA logs...
                          </TableCell>
                        </TableRow>
                      ) : filteredLogs.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={12} className="p-0">
                            <EmptyStateComponent />
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredLogs.map((log: OshaLogEntry) => (
                          <TableRow
                            key={log.id}
                            className="cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-100"
                            onClick={() => handleRowClick(log.logId)}
                          >
                            <TableCell className="max-w-[350px] py-4 px-6">
                              <div className="flex flex-col">
                                <div className="font-medium">{log.logId}</div>
                                <div className="text-sm text-muted-foreground truncate">{log.title}</div>
                              </div>
                            </TableCell>
                            <TableCell className="py-4 px-4 text-sm text-gray-900">
                              {getPrivacyAdjustedValue(log, 'employee')}
                            </TableCell>
                            <TableCell className="py-4 px-4 text-sm text-gray-600">
                              {getPrivacyAdjustedValue(log, 'jobTitle')}
                            </TableCell>
                            <TableCell className="py-4 px-4">
                              <Badge className={getCaseTypeBadgeColor(log.type)}>{log.type}</Badge>
                            </TableCell>
                            <TableCell className="text-center py-4 px-4">
                              {log.type === 'Death' ? (
                                <Badge className="bg-red-100 text-red-800 border-red-200" variant="outline">
                                  Yes
                                </Badge>
                              ) : (
                                <span className="text-gray-400">No</span>
                              )}
                            </TableCell>
                            <TableCell className="py-4 px-4 text-sm text-gray-600">
                              {formatDate(log.dateTime)}
                            </TableCell>
                            <TableCell className="py-4 px-4 text-sm text-gray-600">
                              {getPrivacyAdjustedValue(log, 'location')}
                            </TableCell>
                            <TableCell className="text-center py-4 px-4 text-sm text-gray-900 font-medium">
                              {log.daysAwayFromWork || 0}
                            </TableCell>
                            <TableCell className="text-center py-4 px-4 text-sm text-gray-900 font-medium">
                              {log.daysRestricted || 0}
                            </TableCell>
                            <TableCell className="text-center py-4 px-4">
                              {log.oshaReportable ? (
                                <CheckCircle className="mx-auto h-4 w-4 text-green-500" />
                              ) : (
                                <XCircle className="mx-auto h-4 w-4 text-gray-300" />
                              )}
                            </TableCell>
                            <TableCell className="py-4 px-4">
                              <Badge className={getStatusBadgeColor(log.status)}>{log.status}</Badge>
                            </TableCell>
                            <TableCell className="text-center py-4 px-4">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRowClick(log.logId);
                                }}
                                className="h-8 w-8 p-0 hover:bg-blue-50"
                              >
                                <Eye className="h-4 w-4 text-blue-600" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Summary (Form 300A) Tab */}
        <TabsContent value="summary">
          <Card className="shadow-lg border-0 bg-white">
            <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white px-6 py-5">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
                <CardTitle className="text-xl font-bold flex items-center text-gray-900">
                  <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg mr-3">
                    <Calendar className="h-5 w-5 text-green-600" />
                  </div>
                  OSHA Form 300A Summary of Work-Related Injuries and Illnesses
                </CardTitle>

                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto">
                  {/* Primary Action Group - Submit OSHA Report */}
                  <div className="flex items-center">
                    <Button
                      variant="default"
                      size="default"
                      onClick={openPreDownloadModal}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2.5 shadow-sm h-10"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Submit OSHA Report
                    </Button>
                  </div>

                  {/* Secondary Actions Group */}
                  <div className="flex items-center gap-3">
                    <Button
                      variant="outline"
                      size="default"
                      onClick={handleExport300A}
                      className="font-medium border-gray-300 hover:bg-gray-50 h-10 px-4"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export PDF
                    </Button>
                    {!isYearArchived ? (
                      <Button
                        variant="outline"
                        size="default"
                        onClick={handleArchiveYear}
                        className="font-medium border-gray-300 hover:bg-gray-50 h-10 px-4"
                      >
                        <Archive className="h-4 w-4 mr-2" />
                        Archive Year
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        size="default"
                        onClick={handleRestoreYear}
                        className="font-medium border-gray-300 hover:bg-gray-50 h-10 px-4"
                      >
                        <Archive className="h-4 w-4 mr-2" />
                        Restore Year
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingSummary ? (
                <div className="text-center py-8">Loading summary data...</div>
              ) : !summaryData ? (
                <div className="text-center py-8">No summary data available for {filterYear}.</div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Establishment Information Card */}
                  <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardHeader className="flex flex-row items-start justify-between pb-4 border-b border-gray-200">
                      <CardTitle className="text-xl font-bold flex items-center text-gray-900">
                        <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
                          <Building className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <div>Establishment Information</div>
                          <div className="text-sm font-normal text-gray-600 mt-1">Company details for {filterYear}</div>
                        </div>
                      </CardTitle>

                      {/* Edit/Save buttons */}
                      {isEditingEstablishment ? (
                        <div className="flex space-x-2">
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => {
                              // Validate inputs
                              if (editableEstablishmentInfo.totalHoursWorked <= 0) {
                                toast({
                                  title: 'Validation Error',
                                  description: 'Total Hours Worked must be greater than zero.',
                                  variant: 'destructive',
                                });
                                return;
                              }

                              if (editableEstablishmentInfo.totalEmployees <= 0) {
                                toast({
                                  title: 'Validation Error',
                                  description: 'Annual Average Number of Employees must be greater than zero.',
                                  variant: 'destructive',
                                });
                                return;
                              }

                              if (editableEstablishmentInfo.naicsCode.length !== 6) {
                                toast({
                                  title: 'Validation Error',
                                  description: 'NAICS Code must be a 6-digit number.',
                                  variant: 'destructive',
                                });
                                return;
                              }

                              // Recalculate rates based on edited values
                              // Use the existing case counts from summaryData with new hours worked
                              if (summaryData) {
                                const newRates = calculateRates(
                                  editableEstablishmentInfo.totalHoursWorked,
                                  summaryData.totalCases,
                                  summaryData.totalDaysAway,
                                  summaryData.totalRestricted,
                                );

                                // Update the calculated rates
                                setCalculatedRates(newRates);

                                // Here you would normally save to the API
                                // For demo purposes, we'll just update the UI state
                                toast({
                                  title: 'Changes saved',
                                  description: 'Establishment information and rates updated successfully.',
                                });
                              }

                              setIsEditingEstablishment(false);
                            }}
                          >
                            <Building className="h-4 w-4 mr-2 inline" /> Save Changes
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Reset form to original values
                              if (summaryData) {
                                setEditableEstablishmentInfo({
                                  companyName: summaryData.companyName,
                                  facilityId: summaryData.facilityId,
                                  naicsCode: summaryData.naicsCode,
                                  ein: '12-3456789', // Default EIN value
                                  year: summaryData.year,
                                  totalEmployees: summaryData.totalEmployees,
                                  totalHoursWorked: summaryData.totalHoursWorked,
                                });
                              }
                              setIsEditingEstablishment(false);
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      ) : (
                        <Button variant="outline" size="sm" onClick={handleEditEstablishment}>
                          Edit Summary
                        </Button>
                      )}
                    </CardHeader>

                    <CardContent className="pt-8">
                      <div className="space-y-6">
                        {/* Company Name */}
                        <div>
                          <Label className="text-sm text-gray-500" htmlFor="companyName">
                            Company Name
                          </Label>
                          {isEditingEstablishment ? (
                            <Input
                              id="companyName"
                              value={editableEstablishmentInfo.companyName}
                              placeholder="Enter company name"
                              onChange={(e) => {
                                setEditableEstablishmentInfo({
                                  ...editableEstablishmentInfo,
                                  companyName: e.target.value,
                                });
                              }}
                              className="mt-1 bg-gray-50"
                            />
                          ) : (
                            <p className="font-medium">{summaryData.companyName}</p>
                          )}
                        </div>

                        {/* Facility ID */}
                        <div>
                          <Label className="text-sm text-gray-500" htmlFor="facilityId">
                            Facility ID
                          </Label>
                          {isEditingEstablishment ? (
                            <Input
                              id="facilityId"
                              value={editableEstablishmentInfo.facilityId}
                              placeholder="Enter facility ID"
                              onChange={(e) => {
                                setEditableEstablishmentInfo({
                                  ...editableEstablishmentInfo,
                                  facilityId: e.target.value,
                                });
                              }}
                              className="mt-1 bg-gray-50"
                            />
                          ) : (
                            <p className="font-medium">{summaryData.facilityId}</p>
                          )}
                        </div>

                        {/* NAICS Code */}
                        <div>
                          <Label className="text-sm text-gray-500" htmlFor="naicsCode">
                            NAICS Code
                          </Label>
                          {isEditingEstablishment ? (
                            <Input
                              id="naicsCode"
                              value={editableEstablishmentInfo.naicsCode}
                              onChange={(e) => {
                                // Validate: only allow numbers and up to 6 digits
                                const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                                setEditableEstablishmentInfo({
                                  ...editableEstablishmentInfo,
                                  naicsCode: value,
                                });
                              }}
                              className="mt-1 bg-gray-50"
                              placeholder="6-digit code"
                              maxLength={6}
                            />
                          ) : (
                            <p className="font-medium">{summaryData.naicsCode}</p>
                          )}
                        </div>

                        {/* EIN */}
                        <div>
                          <Label className="text-sm text-gray-500" htmlFor="ein">
                            EIN
                          </Label>
                          {isEditingEstablishment ? (
                            <Input
                              id="ein"
                              type="text"
                              placeholder="Enter EIN (XX-XXXXXXX)"
                              value={editableEstablishmentInfo.ein}
                              onChange={(e) => {
                                // Allow EIN format: XX-XXXXXXX
                                const value = e.target.value;
                                if (value.length <= 10) {
                                  setEditableEstablishmentInfo({
                                    ...editableEstablishmentInfo,
                                    ein: value,
                                  });
                                }
                              }}
                              className="mt-1 bg-gray-50"
                              maxLength={10}
                            />
                          ) : (
                            <p className="font-medium">{editableEstablishmentInfo.ein || 'Not specified'}</p>
                          )}
                        </div>

                        {/* Year */}
                        <div>
                          <Label className="text-sm text-gray-500" htmlFor="year">
                            Year
                          </Label>
                          {isEditingEstablishment ? (
                            <Select
                              value={String(editableEstablishmentInfo.year)}
                              onValueChange={(value) =>
                                setEditableEstablishmentInfo({
                                  ...editableEstablishmentInfo,
                                  year: parseInt(value),
                                })
                              }
                            >
                              <SelectTrigger id="year" className="mt-1 bg-gray-50">
                                <SelectValue placeholder="Select Year" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="2023">2023</SelectItem>
                                <SelectItem value="2024">2024</SelectItem>
                                <SelectItem value="2025">2025</SelectItem>
                              </SelectContent>
                            </Select>
                          ) : (
                            <p className="font-medium">{summaryData.year}</p>
                          )}
                        </div>

                        {/* Annual Average Number of Employees */}
                        <div>
                          <Label className="text-sm text-gray-500" htmlFor="totalEmployees">
                            Annual Average Number of Employees
                          </Label>
                          {isEditingEstablishment ? (
                            <Input
                              id="totalEmployees"
                              type="text"
                              inputMode="numeric"
                              placeholder="Enter employee count"
                              value={editableEstablishmentInfo.totalEmployees.toString()}
                              onChange={(e) => {
                                // Only allow numeric input
                                if (e.target.value === '' || /^\d+$/.test(e.target.value)) {
                                  const value = e.target.value === '' ? 0 : parseInt(e.target.value);
                                  setEditableEstablishmentInfo({
                                    ...editableEstablishmentInfo,
                                    totalEmployees: value,
                                  });
                                }
                              }}
                              className="mt-1 bg-gray-50"
                            />
                          ) : (
                            <p className="font-medium">{summaryData.totalEmployees}</p>
                          )}
                        </div>

                        {/* Total Hours Worked */}
                        <div>
                          <Label className="text-sm text-gray-500" htmlFor="totalHoursWorked">
                            Total Hours Worked
                          </Label>
                          {isEditingEstablishment ? (
                            <Input
                              id="totalHoursWorked"
                              type="text"
                              inputMode="numeric"
                              placeholder="Enter total hours worked"
                              value={editableEstablishmentInfo.totalHoursWorked.toString()}
                              onChange={(e) => {
                                // Only allow numeric input
                                if (e.target.value === '' || /^\d+$/.test(e.target.value)) {
                                  const value = e.target.value === '' ? 0 : parseInt(e.target.value);
                                  setEditableEstablishmentInfo({
                                    ...editableEstablishmentInfo,
                                    totalHoursWorked: value,
                                  });
                                }
                              }}
                              className="mt-1 bg-gray-50"
                            />
                          ) : (
                            <p className="font-medium">{summaryData.totalHoursWorked.toLocaleString()}</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Cases Summary Card */}
                  <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardHeader className="pb-4 border-b border-gray-200">
                      <CardTitle className="text-xl font-bold flex items-center text-gray-900">
                        <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
                          <AlertCircle className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <div>Cases Summary</div>
                          <div className="text-sm font-normal text-gray-600 mt-1">Injury & illness statistics</div>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-8">
                      <div className="space-y-8">
                        <div className="grid grid-cols-2 gap-6">
                          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                            <div className="flex items-center justify-between">
                              <Label className="text-sm font-medium text-gray-600">Deaths</Label>
                              <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                                <XCircle className="h-4 w-4 text-gray-700" />
                              </div>
                            </div>
                            <p className="text-2xl font-bold text-gray-900 mt-2">{summaryData.totalDeaths || 0}</p>
                          </div>
                          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                            <div className="flex items-center justify-between">
                              <Label className="text-sm font-medium text-gray-600">Days Away</Label>
                              <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                                <Calendar className="h-4 w-4 text-blue-600" />
                              </div>
                            </div>
                            <p className="text-2xl font-bold text-blue-600 mt-2">{summaryData.totalDaysAway || 0}</p>
                          </div>
                          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                            <div className="flex items-center justify-between">
                              <Label className="text-sm font-medium text-gray-600">Restricted Work</Label>
                              <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                                <AlertCircle className="h-4 w-4 text-blue-600" />
                              </div>
                            </div>
                            <p className="text-2xl font-bold text-blue-600 mt-2">{summaryData.totalRestricted || 0}</p>
                          </div>
                          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                            <div className="flex items-center justify-between">
                              <Label className="text-sm font-medium text-gray-600">Other Cases</Label>
                              <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                                <FileText className="h-4 w-4 text-gray-700" />
                              </div>
                            </div>
                            <p className="text-2xl font-bold text-gray-900 mt-2">
                              {summaryData.totalOtherRecordable || 0}
                            </p>
                          </div>
                        </div>

                        <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-xl border border-gray-200">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full mr-3">
                                <CheckCircle className="h-5 w-5 text-blue-600" />
                              </div>
                              <Label className="text-lg font-semibold text-gray-800">Total Cases</Label>
                            </div>
                            <p className="text-3xl font-bold text-blue-600">{summaryData.totalCases || 0}</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white p-5 rounded-xl border border-gray-200 shadow-sm">
                            <div className="flex items-center justify-between mb-3">
                              <Label className="text-sm font-semibold text-gray-700">TRC Rate</Label>
                              <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                                <AlertCircle className="h-4 w-4 text-blue-600" />
                              </div>
                            </div>
                            <p className="text-2xl font-bold text-blue-600">
                              {/* Display recalculated rate if establishment info has been edited, otherwise use original rate */}
                              {isEditingEstablishment
                                ? /* When editing, show what the rate will be based on current edits */
                                  calculateRates(
                                    editableEstablishmentInfo.totalHoursWorked,
                                    summaryData.totalCases || 0,
                                    summaryData.totalDaysAway || 0,
                                    summaryData.totalRestricted || 0,
                                  ).trcRate.toFixed(2)
                                : /* When not editing, show either the new calculated rate or the original */
                                  calculatedRates.trcRate
                                  ? calculatedRates.trcRate.toFixed(2)
                                  : (summaryData.trcRate || 0).toFixed(2)}
                            </p>
                            {isEditingEstablishment && <p className="text-xs text-blue-500 mt-1">Projected rate</p>}
                            <p className="text-xs text-gray-500 mt-1">per 200,000 hours</p>
                          </div>
                          <div className="bg-white p-5 rounded-xl border border-gray-200 shadow-sm">
                            <div className="flex items-center justify-between mb-3">
                              <Label className="text-sm font-semibold text-gray-700">DART Rate</Label>
                              <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                                <Calendar className="h-4 w-4 text-blue-600" />
                              </div>
                            </div>
                            <p className="text-2xl font-bold text-blue-600">
                              {/* Display recalculated rate if establishment info has been edited, otherwise use original rate */}
                              {isEditingEstablishment
                                ? /* When editing, show what the rate will be based on current edits */
                                  calculateRates(
                                    editableEstablishmentInfo.totalHoursWorked,
                                    summaryData.totalCases || 0,
                                    summaryData.totalDaysAway || 0,
                                    summaryData.totalRestricted || 0,
                                  ).dartRate.toFixed(2)
                                : /* When not editing, show either the new calculated rate or the original */
                                  calculatedRates.dartRate
                                  ? calculatedRates.dartRate.toFixed(2)
                                  : (summaryData.dartRate || 0).toFixed(2)}
                            </p>
                            {isEditingEstablishment && <p className="text-xs text-blue-500 mt-1">Projected rate</p>}
                            <p className="text-xs text-gray-500 mt-1">per 200,000 hours</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Certification Section */}
                  <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardHeader className="pb-4 border-b border-gray-200">
                      <CardTitle className="text-xl font-bold flex items-center text-gray-900">
                        <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
                          <FileText className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <div>Company Executive Certification</div>
                          <div className="text-sm font-normal text-gray-600 mt-1">
                            Digital signature and certification
                          </div>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-8">
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <Label htmlFor="executive-name" className="text-sm text-gray-500">
                              Name of Company Executive
                            </Label>
                            <Input
                              id="executive-name"
                              value={certificationData.executiveName}
                              onChange={(e) =>
                                setCertificationData((prev) => ({ ...prev, executiveName: e.target.value }))
                              }
                              placeholder="Enter executive name"
                              className="mt-1"
                            />
                          </div>
                          <div>
                            <Label htmlFor="executive-title" className="text-sm text-gray-500">
                              Title
                            </Label>
                            <Input
                              id="executive-title"
                              value={certificationData.executiveTitle}
                              onChange={(e) =>
                                setCertificationData((prev) => ({ ...prev, executiveTitle: e.target.value }))
                              }
                              placeholder="Enter executive title"
                              className="mt-1"
                            />
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="date-certified" className="text-sm text-gray-500">
                                Date Certified
                              </Label>
                              <Input
                                id="date-certified"
                                type="date"
                                value={certificationData.dateCertified}
                                onChange={(e) =>
                                  setCertificationData((prev) => ({ ...prev, dateCertified: e.target.value }))
                                }
                                className="mt-1"
                              />
                            </div>
                          </div>

                          {/* Full-width signature section */}
                          <div className="w-full">
                            <Label className="text-sm text-gray-500">Digital Signature</Label>
                            <div className="mt-1 border border-gray-300 rounded-md bg-white">
                              {isSignatureComplete ? (
                                // Show completed signature view
                                <div className="p-4">
                                  <div className="flex justify-between items-center mb-4">
                                    <span className="text-sm text-gray-600">Signature captured:</span>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={editSignature}
                                      className="text-xs"
                                    >
                                      Edit Signature
                                    </Button>
                                  </div>
                                  {certificationData.signature && (
                                    <div className="border border-gray-200 rounded p-4 bg-gray-50">
                                      <img
                                        src={certificationData.signature}
                                        alt="Digital Signature"
                                        className="max-w-full h-auto"
                                        style={{ maxHeight: '150px' }}
                                      />
                                    </div>
                                  )}
                                </div>
                              ) : (
                                // Show signature drawing interface
                                <>
                                  <div className="p-3 border-b border-gray-200 bg-gray-50 rounded-t-md">
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm text-gray-600">Sign below:</span>
                                      <div className="flex gap-2">
                                        <Button
                                          type="button"
                                          variant="outline"
                                          size="sm"
                                          onClick={clearSignature}
                                          className="text-xs"
                                        >
                                          Clear
                                        </Button>
                                        {certificationData.signature && (
                                          <Button
                                            type="button"
                                            variant="default"
                                            size="sm"
                                            onClick={completeSignature}
                                            className="text-xs"
                                          >
                                            Done
                                          </Button>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                  <div className="p-4">
                                    <canvas
                                      ref={canvasRef}
                                      width={800}
                                      height={200}
                                      className="w-full border border-gray-200 rounded cursor-crosshair"
                                      style={{ touchAction: 'none', maxWidth: '100%' }}
                                      onMouseDown={startDrawing}
                                      onMouseMove={draw}
                                      onMouseUp={stopDrawing}
                                      onMouseLeave={stopDrawing}
                                      onTouchStart={startDrawing}
                                      onTouchMove={draw}
                                      onTouchEnd={stopDrawing}
                                    />
                                  </div>
                                </>
                              )}
                              <div className="p-3 text-xs text-gray-500 bg-gray-50 rounded-b-md border-t border-gray-200">
                                I certify that I have examined this document and that to the best of my knowledge the
                                information contained therein is true, accurate and complete.
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Agency Reports Tab */}
        <TabsContent value="agency">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <div>
                  <CardTitle className="flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                    Serious Incident Notifications to Regulatory Agencies
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-2">
                    Records of serious incidents reported to OSHA and other regulatory agencies
                  </p>
                </div>
                <Button
                  onClick={openSeriousIncidentModal}
                  className="bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-2.5 shadow-lg mt-4 sm:mt-0"
                  size="default"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Report New Serious Incident
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {seriousIncidentNotifications.length === 0 || previewEmptyState ? (
                <div className="text-center py-12">
                  <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Serious Incident Reports</h3>
                  <p className="text-gray-600">
                    No serious incidents have been reported yet. Use the "Report New Serious Incident" button above to
                    log fatalities, hospitalizations, amputations, or loss of eye incidents.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex justify-between items-center mb-4">
                    <p className="text-sm text-gray-600">
                      {seriousIncidentNotifications.length} serious incident report
                      {seriousIncidentNotifications.length !== 1 ? 's' : ''} on file
                    </p>
                  </div>

                  <ScrollArea className="max-h-96 w-full">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Incident ID</TableHead>
                          <TableHead>Date & Time</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Location</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Prepared</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {seriousIncidentNotifications.map((notification) => (
                          <TableRow
                            key={notification.id}
                            className="hover:bg-gray-50 cursor-pointer"
                            onClick={() => openAgencyReportView(notification)}
                          >
                            <TableCell className="font-medium">{notification.incidentId}</TableCell>
                            <TableCell>
                              <div className="text-sm">
                                <div>{notification.dateOfIncident}</div>
                                <div className="text-gray-500">{notification.timeOfIncident}</div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge
                                className={
                                  notification.incidentType === 'Fatality'
                                    ? 'bg-red-100 text-red-800 border-red-200'
                                    : notification.incidentType === 'Inpatient Hospitalization'
                                      ? 'bg-orange-100 text-orange-800 border-orange-200'
                                      : notification.incidentType === 'Amputation'
                                        ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                                        : 'bg-purple-100 text-purple-800 border-purple-200'
                                }
                              >
                                {notification.incidentType}
                              </Badge>
                            </TableCell>
                            <TableCell className="max-w-xs truncate">{notification.location}</TableCell>
                            <TableCell className="max-w-xs truncate">{notification.description}</TableCell>
                            <TableCell className="text-sm text-gray-600">
                              {new Date(notification.submittedAt).toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const details = `
Incident ID: ${notification.incidentId}
Date: ${notification.dateOfIncident} at ${notification.timeOfIncident}
Type: ${notification.incidentType}
Location: ${notification.location}
Description: ${notification.description}
${notification.incidentType === 'Fatality' ? `Number of Fatalities: ${notification.numFatalities}` : ''}
${notification.incidentType === 'Inpatient Hospitalization' ? `Number Hospitalized: ${notification.numHospitalized}` : ''}
Employees Involved: ${notification.employeesInvolved || 'Not specified'}
Contact Person: ${notification.contactPerson}
Contact Phone: ${notification.contactPhone}
Prepared: ${new Date(notification.submittedAt).toLocaleString()}
                                  `;
                                  alert(details.trim());
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Development Preview Button - Bottom of Page */}
      <div className="mt-6 flex justify-center">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPreviewEmptyState(!previewEmptyState)}
          className="text-xs"
        >
          {previewEmptyState ? 'Show Data' : 'Preview Empty State'}
        </Button>
      </div>

      {/* OSHA Submission Confirmation Modal */}
      {/* <AlertDialog open={showOSHASubmissionModal} onOpenChange={setShowOSHASubmissionModal}>
        <AlertDialogContent className="max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-xl font-semibold text-green-700">
              OSHA 300A Data Prepared for E-Submission
            </AlertDialogTitle>
            <AlertDialogDescription className="text-base">
              <div className="space-y-4">
                <div className="font-medium">
                  Your OSHA Form 300A data has been successfully prepared for electronic submission.
                </div>

                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="font-semibold text-blue-900 mb-2">Key Instruction:</div>
                  <div className="text-blue-800">
                    Please upload the downloaded file (osha_300a_summary_{summaryData?.year || '2025'}.csv) to OSHA's
                    official Injury Tracking Application (ITA) website by March 2nd.
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="font-medium">Access the ITA Portal:</div>
                  <a
                    href="https://www.osha.gov/injuryreporting"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:text-blue-800 underline"
                  >
                    https://www.osha.gov/injuryreporting
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                  </a>
                </div>

                <div className="bg-gray-50 p-3 rounded border-l-4 border-gray-400">
                  <div className="text-sm text-gray-700">
                    <strong>Record Retention:</strong> Remember to keep all your OSHA records (Forms 300, 300A, and 301)
                    for 5 years following the year to which they pertain.
                  </div>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => setShowOSHASubmissionModal(false)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6"
            >
              Got It
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog> */}
    </div>
  );
}
