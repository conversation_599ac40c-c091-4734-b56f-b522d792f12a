import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { zodResolver } from '@hookform/resolvers/zod';
import { AsyncIncidentsSelect } from '@/components/composite/async-incidents-select';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Progress } from '@/components/ui/progress';
import { trpc } from '@/providers/trpc';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { primaryRecordableOutcomeEnum, shiftsEnum, typeOfMedicalCareEnum } from '@shared/schema';
import {
  CreateOshaReportForm,
  CreateOshaReportFormSchema,
  PRIMARY_RECORDABLE_OUTCOME_MAP,
  SHIFTS_MAP,
  TYPE_OF_MEDICAL_CARE_MAP,
} from '@shared/schema.types';
import { ArrowLeft, HelpCircle, Info, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation, useParams } from 'wouter';

const FormSchema = CreateOshaReportFormSchema;

type FormType = CreateOshaReportForm;

export default function OshaForm301() {
  const { eventId } = useParams();

  const [_, navigate] = useLocation();

  const [progress, setProgress] = useState(0);
  const [completionStatus, setCompletionStatus] = useState<'not-started' | 'in-progress' | 'complete'>('not-started');
  const [isSticky, setIsSticky] = useState(false);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const { mutateAsync: createOshaReport } = trpc.oshaReport.create.useMutation({
    onSuccess: () => {
      toast('OSHA Form 301 Submitted', {
        description: 'The OSHA log has been recorded successfully.',
      });
    },
    onError: () => {
      toast('Error submitting OSHA form', {
        description: 'There was a problem submitting the OSHA form. Please try again.',
      });
    },
  });

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      // Section 1 - Employee Info
      employeeName: '',
      employeeDepartment: '',
      employeeJobTitle: '',
      employeeWorkLocation: '',
      employeeDateOfHire: undefined,
      employeeShift: 'day',
      employeeSupervisor: '',
      privacyCase: false,

      // Section 2 - Incident Details

      // Section 3 - Nature of Injury
      bodyPartInjured: '',
      typeOfInjury: '',
      treatmentLocation: '',
      typeOfMedicalCare: 'first_aid',
      prescribedMedication: false,

      // Section 4 - OSHA Questions
      wasHospitalized: false,
      wasDeceased: false,
      daysAwayFromWork: undefined,
      daysRestrictedFromWork: undefined,

      // Section 5 - People & Witnesses
      witnesses: '',
      preparedAt: undefined,
      reportedBy: '',

      // Section 6 - Corrective Actions
      rootCauseAnalysis: '',
      correctiveActions: '',

      // Section 7 - OSHA Reporting
      primaryRecordableOutcome: 'medical_treatment_beyond_first_aid',
      reasonForReport: '',
      reasonForPrivacyCase: '',

      // Linked safety event
    },
    mode: 'onSubmit',
  });

  // Watch form values to update progress
  useEffect(() => {
    const subscription = form.watch(() => {
      updateProgress();
    });

    return () => subscription.unsubscribe();
  }, [form.watch]);

  // Watch for sticky state changes
  useEffect(() => {
    const handleScroll = () => {
      const progressElement = document.getElementById('progress-bar');
      if (progressElement) {
        const rect = progressElement.getBoundingClientRect();
        // Element is sticky when it's at the top of the viewport
        setIsSticky(rect.top === 0);
      }
    };

    window.addEventListener('scroll', handleScroll);
    // Also listen for resize events
    window.addEventListener('resize', handleScroll);
    handleScroll(); // Check initial state

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, []);

  function updateProgress() {
    const formValues = form.getValues();

    // For privacy cases, don't require employeeName
    let requiredFields = ['employeeJobTitle', 'employeeShift', 'employeeWorkLocation', 'preparedByUserId', 'eventId'];

    // Only add employeeName as required if it's not a privacy case
    if (!formValues.privacyCase) {
      requiredFields.push('employeeName');
    }

    const filledFields = requiredFields.filter((field) => formValues[field as keyof FormType]).length;

    const calculatedProgress = Math.round((filledFields / requiredFields.length) * 100);
    setProgress(calculatedProgress);

    if (calculatedProgress === 0) {
      setCompletionStatus('not-started');
    } else if (calculatedProgress < 100) {
      setCompletionStatus('in-progress');
    } else {
      setCompletionStatus('complete');
    }
  }

  function handleBackToIncident() {
    if (eventId) {
      navigate(ROUTES.BUILD_INCIDENT_DETAILS_PATH(eventId));
    } else {
      navigate(ROUTES.OSHA_REPORTS);
    }
  }

  async function onSubmit(data: FormType) {
    console.log('OSHA Form Data:', data);

    setIsSubmitting(true);

    await createOshaReport(data);

    handleBackToIncident();

    form.reset();
    setIsSubmitting(false);

    setIsSubmitting(false);
  }

  return (
    <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
      <div className="mb-8">
        {/* Back navigation */}
        <Button variant="ghost" onClick={handleBackToIncident} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {eventId ? 'Back to Incident' : 'Back to OSHA Logs'}
        </Button>

        {/* Header with title and action buttons */}
        <div className="flex flex-col mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start mb-2">
            <h1 className="text-2xl font-bold">OSHA Form 301: Injury and Illness Incident Report</h1>
          </div>

          {/* Status information area */}
          <div className="flex flex-wrap items-center gap-2 mt-1">
            {eventId && <span className="text-sm text-muted-foreground">Linked to Safety Event #{eventId}</span>}
          </div>
        </div>
      </div>

      {/* Progress bar - sticky positioned */}
      <div
        id="progress-bar"
        className={`sticky top-0 bg-white/95 backdrop-blur-sm z-20 py-3 mb-6 border-gray-200 transition-all duration-200 ${
          isSticky ? 'border-b border-l border-r rounded-b-lg' : 'border rounded-lg'
        }`}
      >
        <div className="container mx-auto max-w-7xl px-4 sm:px-6">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              {completionStatus === 'not-started' && (
                <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                  🔴 Not Started
                </Badge>
              )}
              {completionStatus === 'in-progress' && (
                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                  🟡 In Progress
                </Badge>
              )}
              {completionStatus === 'complete' && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  🟢 Complete
                </Badge>
              )}
            </div>
            <span className="text-sm font-medium">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="mb-6 border border-gray-200 rounded-lg p-4 bg-white">
            <div className="flex items-center mb-4">
              <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                1
              </span>
              <h2 className="text-lg font-semibold">
                Linked Safety Event <span className="text-red-500">*</span>
              </h2>
            </div>
            <p className="text-sm text-muted-foreground mb-4">This is a mandatory field.</p>
            <FormField
              control={form.control}
              name="eventId"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-base font-medium">Select Safety Event</FormLabel>
                  <AsyncIncidentsSelect
                    onChange={field.onChange}
                    value={field.value}
                    mustIncludeObjectIds={eventId ? [eventId] : undefined}
                    placeholder="Search and select a safety event..."
                  />
                  <FormDescription>
                    Associate this OSHA record with an existing safety event for better tracking.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Section 2: Employee Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  2
                </span>
                Employee Information
              </CardTitle>
              <CardDescription>Identify the employee involved in the incident</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Privacy Case Toggle */}
              <FormField
                control={form.control}
                name="privacyCase"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border border-blue-300 bg-blue-50 p-4 shadow-sm">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="mr-2 bg-blue-100 text-blue-800 border-blue-300 px-2 py-1">
                        🛡️ PRIVACY
                      </Badge>
                      <span className="text-md font-semibold text-blue-800">Mark as Privacy Case</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-blue-500" />
                          </TooltipTrigger>
                          <TooltipContent className="w-[300px] p-4 bg-white shadow-lg rounded-md border">
                            <div className="space-y-2">
                              <h4 className="font-semibold text-blue-800">OSHA Privacy Case Criteria</h4>
                              <p className="text-sm text-gray-700">
                                Mark this case as private if it involves any of the following:
                              </p>
                              <ul className="text-sm text-gray-700 list-disc pl-4 space-y-1">
                                <li>Injury to intimate body part or reproductive system</li>
                                <li>Sexual assault</li>
                                <li>Mental illness</li>
                                <li>HIV infection, hepatitis, or tuberculosis</li>
                                <li>Needle stick injury from contaminated object</li>
                                <li>Employee requests privacy</li>
                              </ul>
                              <p className="text-sm text-gray-700 pt-1">
                                When enabled, the employee's name will be replaced with "Privacy Case" in OSHA records.
                              </p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-blue-600"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeName"
                render={({ field }) => {
                  // Check if privacy case is enabled
                  const isPrivacyCase = form.watch('privacyCase');

                  return (
                    <FormItem>
                      <FormLabel>
                        Employee Name
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        {isPrivacyCase ? (
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800 flex items-center">
                            <span className="mr-2">🛡️</span>
                            Privacy Case — name will not be displayed in OSHA logs
                          </div>
                        ) : (
                          <Input {...field} placeholder="Employee name" value={field.value ?? ''} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={form.control}
                name="employeeJobTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Job position or title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeWorkLocation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Work Location
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Warehouse, Office, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeDepartment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      <Input placeholder="Employee department" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="employeeDateOfHire"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date of Hire</FormLabel>
                      <FormControl>
                        <DateTimePicker
                          selected={field.value}
                          onSelect={field.onChange}
                          className="w-full"
                          onlyDate
                          placeholder="Select date of hire"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="employeeShift"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shift</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select shift" />
                          </SelectTrigger>
                          <SelectContent>
                            {shiftsEnum.enumValues.map((shift) => (
                              <SelectItem key={shift} value={shift}>
                                {SHIFTS_MAP[shift]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="employeeSupervisor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supervisor</FormLabel>
                    <FormControl>
                      <Input placeholder="Employee's supervisor name" {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 3: Medical Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  3
                </span>
                Medical Information
              </CardTitle>
              <CardDescription>Details about the injury or illness and incident context</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="bodyPartInjured"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Body Part Affected
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Specify the part of body that was affected</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Hand, back, eye, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="typeOfInjury"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Type of Injury
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Specify the type of injury sustained</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Cut, burn, fracture, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="treatmentLocation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Treatment Location</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Onsite first aid, hospital, clinic, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="typeOfMedicalCare"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type of Medical Care</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        {typeOfMedicalCareEnum.enumValues.map((medicalCare) => (
                          <FormItem className="flex items-center space-x-3 space-y-0" key={medicalCare}>
                            <FormControl>
                              <RadioGroupItem value={medicalCare} />
                            </FormControl>
                            <FormLabel className="font-normal">{TYPE_OF_MEDICAL_CARE_MAP[medicalCare]}</FormLabel>
                          </FormItem>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="prescribedMedication"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Prescription Medication Given</FormLabel>
                      <FormDescription>
                        Check if prescription medication was provided as part of treatment
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {/* Relocated fields from Safety Event Details */}
              {/* <Separator className="my-6" />
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <h4 className="text-sm font-medium text-gray-700">Incident Context Details</h4>
                </div>

                <FormField
                  control={form.control}
                  name="specificTask"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        Specific Task Being Performed
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span>
                                <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-xs">
                              <p>
                                Describe what specific task or activity the employee was performing when the incident
                                occurred
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the specific task, operation, or activity being performed when the incident occurred"
                          className="min-h-[60px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="objectCaused"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        Object/Substance That Directly Injured
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span>
                                <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-xs">
                              <p>
                                Identify the specific object, tool, substance, or material that directly caused the
                                injury
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Knife, chemical, machinery part, hot surface, etc." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div> */}
            </CardContent>
          </Card>

          {/* Section 4: OSHA Questions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  4
                </span>
                OSHA Questions
              </CardTitle>
              <CardDescription>Additional information required for OSHA recordkeeping</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="wasHospitalized"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Employee was hospitalized as an in-patient</FormLabel>
                        <FormDescription>Check if overnight admission to a hospital was required</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="wasDeceased"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Incident resulted in death</FormLabel>
                        <FormDescription>Check if the incident resulted in a fatality</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="daysAwayFromWork"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Days Away From Work</FormLabel>
                      <FormDescription>Number of days the employee was unable to work</FormDescription>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          value={field.value ?? ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="daysRestrictedFromWork"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Days of Restricted Work Activity</FormLabel>
                      <FormDescription>Days employee was on restricted duty or job transfer</FormDescription>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          {...field}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          value={field.value ?? ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Section 5: Witnesses & People */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  5
                </span>
                Witnesses & People
              </CardTitle>
              <CardDescription>People involved in or who witnessed the incident</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="witnesses"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Witnesses</FormLabel>
                    <FormDescription>Names of people who witnessed the incident (comma-separated list)</FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder="Enter witness names separated by commas"
                        className="min-h-[60px]"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="reportedBy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reported By</FormLabel>
                      <FormControl>
                        <AsyncUserSelect
                          placeholder="Person who reported this incident"
                          onChange={(userId) => field.onChange(userId)}
                          value={field.value?.toString()}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="preparedByUserId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Prepared By
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <AsyncUserSelect
                          placeholder="Person who prepared this report"
                          onChange={(userId) => field.onChange(userId)}
                          value={field.value?.toString()}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="preparedAt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date Prepared</FormLabel>
                    <FormControl>
                      <DateTimePicker
                        selected={field.value}
                        onSelect={field.onChange}
                        className="w-full"
                        onlyDate
                        placeholder="Select date prepared"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 6: Corrective Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  6
                </span>
                Corrective Actions
              </CardTitle>
              <CardDescription>Actions taken or planned to prevent recurrence</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="rootCauseAnalysis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Root Cause Analysis</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the root cause(s) of the incident"
                        className="min-h-[80px]"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="correctiveActions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Corrective Actions</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe actions taken or planned to prevent recurrence"
                        className="min-h-[80px]"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 7: OSHA Reporting */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  7
                </span>
                OSHA Reporting
              </CardTitle>
              <CardDescription>OSHA reporting requirements and classification</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Primary Recordable Outcome - Mandatory Dropdown */}
              <FormField
                control={form.control}
                name="primaryRecordableOutcome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Primary Recordable Outcome
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select primary recordable outcome" />
                        </SelectTrigger>
                        <SelectContent>
                          {primaryRecordableOutcomeEnum.enumValues.map((outcome) => (
                            <SelectItem key={outcome} value={outcome}>
                              {PRIMARY_RECORDABLE_OUTCOME_MAP[outcome]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription>Select the primary outcome that makes this case OSHA recordable</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Reason for Reportability - Optional Text Area */}
              <FormField
                control={form.control}
                name="reasonForReport"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason for Reportability</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Optional: Provide additional explanation if the dropdown selection doesn't fully cover the situation..."
                        className="min-h-[80px] resize-none"
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormDescription>Optional field for additional context or explanation</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form action buttons */}
          <div className="flex justify-between flex-wrap gap-3">
            {/* Left side buttons */}
            <div className="flex flex-wrap gap-3">
              <Button type="button" variant="outline" onClick={handleBackToIncident}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>

            {/* Right side buttons - save button only in edit mode */}
            <Button type="submit" disabled={isSubmitting}>
              <Save className="mr-2 h-4 w-4" />
              Save OSHA Form
            </Button>
          </div>

          {/* OSHA Record Keeping Requirement - above submit button */}

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-6">
            <div className="flex gap-3">
              <Info className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-medium text-amber-800">OSHA Record Keeping Requirement</h3>
                <p className="text-sm text-amber-700 mt-1">
                  This form must be retained for 5 years following the year to which it pertains. Completing this form
                  will automatically update your OSHA 300 Log and 300A Summary.
                </p>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
