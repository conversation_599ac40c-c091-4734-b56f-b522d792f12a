import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { AccessPointInvalid } from '@/components/access-points/access-point-invalid';
import { AnalyzingLoading } from '@/components/composite/analyzing-loading';
import { AsyncAssetSelect } from '@/components/composite/async-asset-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { MediaUpload } from '@/components/composite/media-upload';
import { VoiceInput, VoiceInputRef } from '@/components/composite/voice-input';
import { PublicIncidentError } from '@/components/incidents/public/public-incident-error';
import { PublicIncidentLoading } from '@/components/incidents/public/public-incident-loading';
import { Button } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useAnalytics } from '@/hooks/use-analytics';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { accessPointStatusEnum, eventCategoryEnum, reportTypeEnum, severityEnum } from '@shared/schema';
import { CATEGORY_MAP, CreateEventFormPublicSchema, REPORT_TYPE_MAP, SEVERITY_MAP } from '@shared/schema.types';
import axios from 'axios';
import { Sparkles } from 'lucide-react';
import { motion } from 'motion/react';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation, useSearchParams } from 'wouter';
import { z } from 'zod';

const FormSchema = CreateEventFormPublicSchema;

export default function NewIncidentReport() {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [autoFilledFields, setAutoFilledFields] = useState<string[]>([]);
  const voiceInputRef = useRef<VoiceInputRef>(null);
  // Analytics tracking state
  const [formStartTime] = useState(() => Date.now());
  const [voiceStartTime, setVoiceStartTime] = useState<number | undefined>();
  const [isVoiceUsed, setIsVoiceUsed] = useState<boolean>(false);
  // Track when voice analysis completed for abandonment tracking
  const [voiceCompletedAt, setVoiceCompletedAt] = useState<number | undefined>();
  // Track last field user interacted with for abandonment tracking
  const [lastFieldInteracted, setLastFieldInteracted] = useState<string>('');
  const [isAccessPointValid, setIsAccessPointValid] = useState<boolean>(false);

  const { track } = useAnalytics();
  const utils = trpc.useUtils();

  const [_, navigate] = useLocation();

  const [searchParams] = useSearchParams();

  const accessPointId = searchParams.get('accessPointId');
  const upkeepCompanyId = searchParams.get('upkeepCompanyId');

  const {
    data: accessPoint,
    isLoading: isLoadingAccessPoint,
    isError: isErrorAccessPoint,
    isSuccess: isSuccessAccessPoint,
  } = trpc.accessPoint.getByIdPublic.useQuery({
    id: accessPointId!,
    upkeepCompanyId: upkeepCompanyId!,
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: '',
      email: '',
      type: reportTypeEnum.enumValues[0],
      title: '',
      reportedAt: new Date(),
      category: eventCategoryEnum.enumValues[0],
      severity: severityEnum.enumValues[0],
      description: '',
      immediateActions: '',
      assetIds: [],
      locationId: undefined,
      upkeepCompanyId: upkeepCompanyId!,
    },
    mode: 'onSubmit',
  });

  // Form View Tracking
  useEffect(() => {
    if (isSuccessAccessPoint && accessPoint) {
      track(ANALYTICS_EVENTS.EVENT.FORM_VIEWED, {
        form_entry_point: 'QR Code',
        access_point_id: accessPoint.id!,
        location_id: accessPoint.locationId,
        is_prefilled_from_qr: !!accessPoint.locationId,
        platform: 'web',
        role: 'Public',
        organization_id: accessPoint.upkeepCompanyId,
        component: 'Incidents',
        access_scope: 'public',
      });
    }
  }, [accessPoint, isSuccessAccessPoint]);

  // Form Abandonment Tracking
  useEffect(() => {
    const handleBeforeUnload = () => {
      const formDuration = Math.floor((Date.now() - formStartTime) / 1000);

      // Check for voice-to-form abandonment (voice was used successfully but form abandoned)
      if (isVoiceUsed && voiceCompletedAt && !isSubmitting) {
        const voiceDuration = Date.now() - voiceCompletedAt;
        track(ANALYTICS_EVENTS.EVENT.VOICE_TO_FORM_ABANDONED, {
          duration_ms: voiceDuration,
          last_field_interacted: lastFieldInteracted,
          access_point_id: accessPointId ?? undefined,
          platform: 'web',
          role: 'Public',
          organization_id: accessPoint?.upkeepCompanyId,
          component: 'Incidents',
          access_scope: 'public',
        });
      }
      // General form abandonment tracking
      else if (formDuration > 30 && !isSubmitting) {
        track(ANALYTICS_EVENTS.EVENT.FORM_ABANDONED, {
          form_entry_point: 'QR Code',
          duration_on_form_seconds: formDuration,
          access_point_id: accessPointId ?? undefined,
          platform: 'web',
          role: 'Public',
          organization_id: accessPoint?.upkeepCompanyId,
          component: 'Incidents',
          access_scope: 'public',
        });
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [formStartTime, isSubmitting, accessPointId, accessPoint?.upkeepCompanyId]);

  useEffect(() => {
    if (accessPoint?.status === accessPointStatusEnum.enumValues[0] && !accessPoint?.archived) {
      setIsAccessPointValid(true);
    }
  }, [accessPoint]);

  useEffect(() => {
    if (accessPoint?.locationId) {
      form.setValue('locationId', accessPoint.locationId);
    }
  }, [accessPoint?.locationId]);

  const { mutateAsync: analyze, isPending: isAnalyzing } = trpc.ai.analyzePublic.useMutation({
    onSuccess: () => {
      // Analysis complete - data processed
      toast('Analysis complete', {
        description: 'We have analyzed your safety event report and filled out the form for you.',
      });
    },
    onError: (error) => {
      console.error('Error analyzing safety event', error);
      toast('Error analyzing safety event', {
        description: 'There was a problem analyzing your safety event. Please try again.',
      });
    },
  });

  useEffect(() => {
    if (form.watch('locationId')) {
      form.setValue('assetIds', []);
      utils.asset.searchPublic.invalidate();
    }
  }, [form.watch('locationId')]);

  const { mutateAsync: createEvent } = trpc.incident.createPublic.useMutation({
    onSuccess: () => {
      utils.incident.list.invalidate();
    },
    onError: (error) => {
      console.error('Error creating safety event', error);
      toast('Error creating safety event', {
        description: 'There was a problem creating your safety event. Please try again.',
      });
    },
  });

  const { mutateAsync: getPresignedUrl } = trpc.file.getPresignedUrlPublic.useMutation();

  const { mutateAsync: updateFile } = trpc.file.updatePublic.useMutation();

  if (!accessPointId || !upkeepCompanyId || isErrorAccessPoint) {
    return (
      <PublicIncidentError
        accessPointId={accessPointId}
        upkeepCompanyId={upkeepCompanyId}
        isErrorAccessPoint={isErrorAccessPoint}
      />
    );
  }

  if (isLoadingAccessPoint) {
    return <PublicIncidentLoading />;
  }

  // Separate function to handle the actual form submission
  const onSubmit = async (values: z.infer<typeof FormSchema>) => {
    setIsSubmitting(true);

    if (!upkeepCompanyId) {
      // Form Validation Failed Tracking
      const errorMessage = 'Role ID is required but missing';
      track(ANALYTICS_EVENTS.EVENT.FORM_VALIDATION_FAILED, {
        validation_errors: [errorMessage],
        error_message: errorMessage,
        access_point_id: accessPointId,
        platform: 'web',
        role: 'Public',
        organization_id: accessPoint?.upkeepCompanyId,
        component: 'Incidents',
        access_scope: 'public',
      });
      toast('Error reporting safety event', {
        description: 'There was a problem submitting your safety event. Please try again.',
      });
      return;
    }

    try {
      const createdEvent = await createEvent({
        ...values,
        upkeepCompanyId,
        media: undefined,
      });

      for (const item of values.media ?? []) {
        const result = await getPresignedUrl({
          fileName: item.name,
          fileSize: item.size,
          mimeType: item.type,
          entityType: 'event',
          entityId: createdEvent?.id,
          upkeepCompanyId,
        });

        try {
          await axios.put(result.presignedUrl, item.file, {
            headers: {
              'Content-Type': result.file?.mimeType,
            },
          });

          if (!result?.file) {
            throw new Error('Error uploading file');
          } else {
            await updateFile({
              id: result.file.id,
              s3Key: result.file.s3Key,
              status: 'completed',
              upkeepCompanyId: accessPoint?.upkeepCompanyId!,
            });
          }
        } catch (error) {
          console.error('Error uploading file', error);
          toast('Error uploading file', {
            description: 'There was a problem uploading your file. Please try again.',
          });
        }
      }

      // Form Submission Tracking
      track(ANALYTICS_EVENTS.EVENT.FORM_SUBMITTED, {
        event_id: createdEvent?.id,
        report_type: REPORT_TYPE_MAP[values.type as keyof typeof REPORT_TYPE_MAP],
        severity_level: values.severity
          ? ((values.severity.charAt(0).toUpperCase() + values.severity.slice(1)) as
              | 'Low'
              | 'Medium'
              | 'High'
              | 'Critical')
          : undefined,
        location: values.locationId || '',
        asset: values.assetIds?.length ? values.assetIds[0] : '',
        hazard_category: values.category || '',
        media_attached_count: values.media?.length || 0,
        is_ai_assisted: isVoiceUsed,
        first_name: values.name?.split(' ')[0] || '',
        last_name: values.name?.split(' ').slice(1).join(' ') || '',
        reporter_email: values.email,
        form_entry_point: 'QR Code',
        is_prefilled_from_qr: !!accessPoint?.locationId,
        access_point_id: accessPointId,
        organization_id: accessPoint?.upkeepCompanyId,
        platform: 'web',
        role: 'Public',
        component: 'Incidents',
        access_scope: 'public',
      });

      // QR Code Specific Analytics - tracking form submission via QR code
      if (accessPointId && accessPoint) {
        const timeFromScanToSubmit = Math.floor((Date.now() - formStartTime) / 1000);
        track(ANALYTICS_EVENTS.ACCESS_POINT.EVENT_SUBMITTED_VIA_QR, {
          access_point_id: accessPointId,
          location_id: accessPoint.locationId,
          time_from_scan_to_submit: timeFromScanToSubmit,
          platform: 'web',
          role: 'Public',
          organization_id: accessPoint.upkeepCompanyId,
          component: 'Incidents',
          access_scope: 'public',
        });
      }
      toast('Safety Event created', {
        description: 'Your safety event has been created successfully',
      });

      // Reset form
      form.reset();

      // Clear any auto-filled fields
      setAutoFilledFields([]);

      // Reset media files
      form.setValue('media', []);

      // Reset voice input
      voiceInputRef.current?.resetTranscript();

      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: 'smooth' });

      navigate(ROUTES.INCIDENT_PUBLIC_REPORT_SUCCESS, {
        state: {
          createdIncidentSlug: createdEvent?.slug,
          url: window.location.href,
        },
      });
    } catch (error) {
      console.error('Error submitting safety event', error);
      // Form Validation Failed Tracking for submission errors
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      track(ANALYTICS_EVENTS.EVENT.FORM_VALIDATION_FAILED, {
        validation_errors: [errorMessage],
        error_message: errorMessage,
        access_point_id: accessPointId,
        platform: 'web',
        role: 'Public',
        organization_id: accessPoint?.upkeepCompanyId,
        component: 'Incidents',
        access_scope: 'public',
      });
      toast('Error reporting safety event', {
        description: 'There was a problem submitting your safety event. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle the extracted data from voice analysis
  const handleVoiceAnalysis = async (text: string) => {
    // Voice Started Tracking
    setVoiceStartTime(Date.now());
    setIsVoiceUsed(true);

    track(ANALYTICS_EVENTS.EVENT.VOICE_STARTED, {
      access_point_id: accessPointId,
      platform: 'web',
      role: 'Public',
      organization_id: accessPoint?.upkeepCompanyId,
      component: 'Incidents',
      access_scope: 'public',
    });

    // Clear any previous animations
    setAutoFilledFields([]);

    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    try {
      const data = await analyze({ text, timezone });
      const voiceDuration = voiceStartTime ? Date.now() - voiceStartTime : 0;
      let fieldsPopulated = 0;

      // Track each field that gets populated
      const trackFieldPopulation = (fieldName: string, value: string | Date | undefined) => {
        if (value) {
          fieldsPopulated++;

          // Track individual field AI population
          track(ANALYTICS_EVENTS.EVENT.FIELD_AI_POPULATED, {
            field_name: fieldName,
            access_point_id: accessPointId,
            platform: 'web',
            role: 'Public',
            organization_id: accessPoint?.upkeepCompanyId,
            component: 'Incidents',
            access_scope: 'public',
          });
        }
      };

      // Animate each field separately with staggered timing
      const animateField = (
        field: keyof z.infer<typeof FormSchema>,
        value: string | Date | undefined,
        delay: number,
      ) => {
        setTimeout(() => {
          form.setValue(field, value);
          trackFieldPopulation(field, value);
          setAutoFilledFields((prev) => [...prev, field]);
        }, delay);
      };

      // Base delay between animations and running counter
      const baseDelay = 300;
      let currentDelay = 300;

      // Apply animations in sequence for first section of form
      if (data?.type) {
        animateField('type', data.type, currentDelay);
        currentDelay += baseDelay;
      }

      if (data?.title) {
        animateField('title', data.title, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'category' in data && data.category) {
        animateField('category', data.category, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'reportedAt' in data && data.reportedAt) {
        animateField('reportedAt', data.reportedAt, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'severity' in data && data.severity) {
        animateField('severity', data.severity, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'description' in data && data.description) {
        animateField('description', data.description, currentDelay);
        currentDelay += baseDelay;
      }

      if (data && 'immediateActions' in data && data.immediateActions) {
        animateField('immediateActions', data.immediateActions, currentDelay);
        currentDelay += baseDelay;
      }

      // Voice Success Tracking
      track(ANALYTICS_EVENTS.EVENT.VOICE_SUCCESSFUL, {
        duration_ms: voiceDuration,
        fields_populated_count: fieldsPopulated,
        access_point_id: accessPointId,
        platform: 'web',
        role: 'Public',
        organization_id: accessPoint?.upkeepCompanyId,
        component: 'Incidents',
        access_scope: 'public',
      });

      // Mark when voice analysis completed for abandonment tracking
      setVoiceCompletedAt(Date.now());

      toast('🪄 AI Voice Analysis Complete', {
        description: "We've filled out the form with your spoken incident report",
      });
    } catch (error) {
      const voiceDuration = voiceStartTime ? Date.now() - voiceStartTime : 0;

      // Determine failure reason
      const determineFailureReason = (error: unknown): 'no_speech_detected' | 'mic_denied' | 'api_error' => {
        if (error instanceof Error) {
          if (error.message.includes('speech') || error.message.includes('audio')) {
            return 'no_speech_detected';
          }
          if (error.message.includes('permission') || error.message.includes('microphone')) {
            return 'mic_denied';
          }
        }
        return 'api_error';
      };

      // Voice Failure Tracking
      track(ANALYTICS_EVENTS.EVENT.VOICE_FAILED, {
        reason: determineFailureReason(error),
        duration_ms: voiceDuration,
        access_point_id: accessPointId,
        platform: 'web',
        role: 'Public',
        organization_id: accessPoint?.upkeepCompanyId,
        component: 'Incidents',
        access_scope: 'public',
      });
    }
  };

  if (!isAccessPointValid && isSuccessAccessPoint) {
    return <AccessPointInvalid />;
  }

  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <div>
        <div className="mb-6">
          <p className="text-muted-foreground text-base sm:text-lg">
            Use this form to report safety incidents or near misses that occurred in your facility.
          </p>
        </div>

        <div className="mb-5">
          <VoiceInput ref={voiceInputRef} onAnalysisComplete={handleVoiceAnalysis} isPublic={true} />
        </div>

        {isAnalyzing && <AnalyzingLoading />}

        {/* Media Upload Component */}
        <div className="mt-5 mb-10 w-full px-0 sm:px-0">
          <MediaUpload
            className="w-full max-w-full"
            files={form.watch('media') ?? []}
            setFiles={(tFiles) => {
              form.setValue('media', tFiles);
            }}
          />
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Name <span className="text-red-500">*</span>
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input
                      placeholder="Enter your full name"
                      className={autoFilledFields.includes('name') ? 'border-indigo-300' : ''}
                      {...field}
                      onFocus={() => setLastFieldInteracted('name')}
                    />
                  </FormControl>
                  <FormDescription>Your full name as it appears in company records</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Email <span className="text-red-500">*</span>
                    </FormLabel>
                  </div>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email address"
                      className={autoFilledFields.includes('email') ? 'border-indigo-300' : ''}
                      {...field}
                      onFocus={() => setLastFieldInteracted('email')}
                    />
                  </FormControl>
                  <FormDescription>Your company email address </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Report Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Report Type <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('reportType') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('type');
                    }}
                    value={field.value} // Use value instead of defaultValue to keep it updated
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('reportType') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {reportTypeEnum.enumValues.map((type) => {
                        return (
                          <SelectItem key={type} value={type}>
                            {REPORT_TYPE_MAP[type]}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    An incident is an event that caused injury or damage. A near miss is an event that could have caused
                    injury or damage but didn't. An observation is an event that did not cause injury or damage.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Title <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('title') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Input
                      placeholder="Briefly describe what happened"
                      className={autoFilledFields.includes('title') ? 'border-indigo-300' : ''}
                      {...field}
                      onFocus={() => setLastFieldInteracted('title')}
                    />
                  </FormControl>
                  <FormDescription>
                    A short description of the safety event (e.g., "Fall from ladder in warehouse")
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date and Time */}
            <FormField
              control={form.control}
              name="reportedAt"
              render={({ field }) => (
                <FormItem className={`flex flex-col`}>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Date and Time <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('reportedAt') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <DateTimePicker
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={{ after: new Date() }}
                      onFocus={() => setLastFieldInteracted('reportedAt')}
                    />
                  </FormControl>
                  <FormDescription>When did the safety event occur?</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location */}
            <FormField
              control={form.control}
              name="locationId"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Location</FormLabel>
                    {autoFilledFields.includes('location') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <AsyncLocationSelect
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                        setLastFieldInteracted('locationId');
                      }}
                      placeholder="Where did it happen?"
                      upkeepCompanyId={upkeepCompanyId}
                    />
                  </FormControl>
                  <FormDescription>
                    Specific area, building, or equipment where the safety event occurred
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Assets */}
            <FormField
              control={form.control}
              name="assetIds"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Assets</FormLabel>
                    {autoFilledFields.includes('assetIds') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <AsyncAssetSelect
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                        setLastFieldInteracted('assetIds');
                      }}
                      locationId={form.watch('locationId') ?? undefined}
                      placeholder="Select assets"
                      multiple
                      upkeepCompanyId={upkeepCompanyId}
                    />
                  </FormControl>
                  <FormDescription>
                    {/* Equipment or asset involved (AI will suggest based on selected location) */}
                    Select the assets that were involved in the safety event. If the safety event involved multiple
                    assets, select all that apply.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Hazard Category */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Hazard Category <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('category') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('category');
                    }}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('category') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Type of hazard" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {eventCategoryEnum.enumValues.map((category) => (
                        <SelectItem key={category} value={category}>
                          {CATEGORY_MAP[category]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>Examples: Chemical, Electrical, Ergonomic, Fall, Fire, Mechanical</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Severity Level */}
            <FormField
              control={form.control}
              name="severity"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>
                      Severity Level <span className="text-red-500">*</span>
                    </FormLabel>
                    {autoFilledFields.includes('severityLevel') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setLastFieldInteracted('severity');
                    }}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className={autoFilledFields.includes('severityLevel') ? 'border-indigo-300' : ''}>
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {severityEnum.enumValues.map((level) => (
                        <SelectItem key={level} value={level}>
                          {SEVERITY_MAP[level]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    How serious was this safety event or how serious could it have been?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Description</FormLabel>
                    {autoFilledFields.includes('description') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what happened in detail"
                      className={`min-h-[120px] ${autoFilledFields.includes('description') ? 'border-indigo-300' : ''}`}
                      {...field}
                      value={field.value ?? ''}
                      onFocus={() => setLastFieldInteracted('description')}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a detailed account of what happened, what led to the safety event, and any relevant
                    circumstances
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Immediate Actions */}
            <FormField
              control={form.control}
              name="immediateActions"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Immediate Actions Taken</FormLabel>
                    {autoFilledFields.includes('immediateActions') && (
                      <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} className="text-indigo-500">
                        <Sparkles className="h-4 w-4" />
                      </motion.div>
                    )}
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="What actions were taken immediately after the safety event?"
                      className={`min-h-[80px] ${autoFilledFields.includes('immediateActions') ? 'border-indigo-300' : ''}`}
                      {...field}
                      value={field.value ?? ''}
                      onFocus={() => setLastFieldInteracted('immediateActions')}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe any immediate steps taken to address the situation, treat injuries, or prevent further harm
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  // Reset voice input
                  console.log('reset voice input');
                  voiceInputRef.current?.resetTranscript();
                  form.reset();
                  setAutoFilledFields([]);
                  // Reset media files
                  form.setValue('media', []);
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
              >
                Reset
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Report'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
