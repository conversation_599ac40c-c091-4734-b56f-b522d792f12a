import { CreateGlobalLocationModal } from '@/components/global-locations/create-global-location-modal';
import { Filters } from '@/components/global-locations/global-locations-filters';
import { GlobalLocationsEmpty } from '@/components/global-locations/global-locations-empty';
import { GlobalLocationsError } from '@/components/global-locations/global-locations-error';
import { GlobalLocationsLoading } from '@/components/global-locations/global-locations-loading';
import { MobileFilters } from '@/components/global-locations/global-locations-mobile-filters';
import { GlobalLocationsMobileView } from '@/components/global-locations/global-locations-mobile-view';
import { GlobalLocationsTable } from '@/components/global-locations/global-locations-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useInfiniteGlobalLocations } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { GlobalLocationsFilters } from '@shared/location.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { useDebounce } from '@uidotdev/usehooks';
import { Search, X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'wouter';

export default function GlobalLocationsView() {
  const [_, navigate] = useLocation();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const [location] = useLocation();
  const { hasPermission } = usePermissions();

  const [filters, setFilters] = useState<GlobalLocationsFilters>({
    createdBy: [],
    includeArchived: false,
    createdDateRange: {
      from: undefined,
      to: undefined,
    },
  });

  const {
    data: globalLocations,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteGlobalLocations({ search: debouncedSearchTerm, filters });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Check for create query parameter and open modal
  useEffect(() => {
    if (location === ROUTES.GLOBAL_LOCATIONS_NEW) {
      setIsModalOpen(true);
    } else {
      // Close modal if navigating away from the new route
      setIsModalOpen(false);
    }
  }, [location]);

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      createdBy: [],
      includeArchived: false,
      createdDateRange: {
        from: undefined,
        to: undefined,
      },
    });
    setSearchTerm('');
  };

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      (filters.createdBy?.length ?? 0) +
      (filters.createdDateRange?.from || filters.createdDateRange?.to ? 1 : 0) +
      (filters.includeArchived ? 1 : 0) +
      (debouncedSearchTerm.trim() ? 1 : 0)
    );
  }, [filters, debouncedSearchTerm]);

  const trackFilterApplied = (
    type: 'createdBy' | 'createdDateRange' | 'includeArchived',
    value: string,
  ) => {
    // TODO: Add analytics tracking if needed
    console.log('Filter applied:', type, value);
  };

  const handleArchive = (globalLocation: RouterOutputs['globalLocation']['list']['result'][number]) => {
    // TODO: Implement archive functionality
    console.log('Archive global location:', globalLocation);
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl font-bold mb-4 md:mb-0">Global Locations</h1>
        <div className="flex items-center w-full md:w-auto">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search global locations..."
              className="pl-8 pr-4"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button variant="ghost" size="sm" className="absolute right-0 top-0 " onClick={() => setSearchTerm('')}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="ml-4 flex items-center gap-2">
            {hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.CREATE) && (
              <Button
                onClick={() => {
                  setIsModalOpen(true);
                }}
              >
                + Create Global Location
              </Button>
            )}
          </div>

          <MobileFilters
            activeFilterCount={activeFilterCount}
            filters={filters}
            setFilters={setFilters}
            resetFilters={resetFilters}
            trackFilterApplied={trackFilterApplied}
          />
        </div>
      </div>

      {/* Desktop Filters bar */}
      <Filters
        filters={filters}
        setFilters={setFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        trackFilterApplied={trackFilterApplied}
      />

      {/* Error state */}
      {error && <GlobalLocationsError />}

      {/* Loading state */}
      {isLoading && <GlobalLocationsLoading />}

      {/* Create Global Location Modal */}
      {hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.CREATE) && (
        <CreateGlobalLocationModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      )}

      {/* Desktop Table View */}
      {!isLoading && !error && globalLocations && globalLocations.length > 0 && (
        <GlobalLocationsTable globalLocations={globalLocations} />
      )}

      {/* Mobile Card View */}
      {!isLoading && !error && globalLocations && globalLocations.length > 0 && (
        <GlobalLocationsMobileView globalLocations={globalLocations} onArchive={handleArchive} />
      )}

      {/* Empty state - when no global locations are found and not loading */}
      {!isLoading && !error && globalLocations && globalLocations.length === 0 && (
        <GlobalLocationsEmpty
          hasActiveFilters={activeFilterCount > 0}
          onResetFilters={resetFilters}
          onCreateGlobalLocation={() => {
            setIsModalOpen(true);
          }}
        />
      )}

      {/* Load More Button for Infinite Scrolling */}
      {globalLocations && globalLocations.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${globalLocations.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
