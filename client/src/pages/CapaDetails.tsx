import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { CapaError } from '@/components/capas/details/capa-error';
import { CapaLoading } from '@/components/capas/details/capa-loading';
import { CreateWorkOrderModal } from '@/components/capas/details/create-work-order-modal';
import { EffectivenessBadge } from '@/components/capas/details/effectiveness-badge';
import { LinkedTo } from '@/components/capas/details/linked-to';
import { CapaTypeBadge } from '@/components/capas/list/capa-type-badge';
import { PriorityBadge } from '@/components/capas/list/priority-badge';
import { CommentsSection } from '@/components/composite/comments';
import { NotifiedTeamMembers } from '@/components/composite/notified-team-members';
import { StatusBadge } from '@/components/composite/status-badge';
import { Timeline } from '@/components/composite/timeline';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { handleDownload } from '@/lib/download-file';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { capaTagsEnum, statusEnum } from '@shared/schema';
import { CAPA_TAGS_MAP, RCA_METHOD_MAP, STATUS_MAP } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { differenceInDays, format } from 'date-fns';
import {
  Archive,
  ArchiveRestore,
  Calendar,
  ChevronLeft,
  Download,
  Edit,
  FileText,
  MapPin,
  MoreVertical,
  User,
} from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

export default function CapaDetails({ params }: { params: { id: string } }) {
  const [, navigate] = useLocation();
  const { hasPermission } = usePermissions();

  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [showWorkOrderModal, setShowWorkOrderModal] = useState(false);

  const utils = trpc.useUtils();
  const { track } = useAnalytics();

  const capaId = params.id;

  // Fetch CAPA data
  const {
    data: capa,
    isLoading: isCapaLoading,
    isError: capaError,
    error,
    isSuccess: isCapaSuccess,
  } = trpc.capa.getById.useQuery({ id: capaId });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  const { mutateAsync: updateCapa } = trpc.capa.update.useMutation({
    onSuccess: () => {
      utils.capa.getById.invalidate({ id: capaId });
    },
  });

  const { data: workOrders } = trpc.workOrder.getByCapa.useQuery({
    capaId: [capaId],
    cursor: undefined,
    limit: 10,
  });

  // Calculate overdue days - moved before early returns to avoid hook order issues
  const overdue = useMemo(() => {
    if (!capa?.dueDate) return 0;
    return differenceInDays(new Date(), new Date(capa.dueDate));
  }, [capa?.dueDate]);

  // Track CAPA details view according to CSV spec
  useEffect(() => {
    if (isCapaSuccess && capa) {
      track(ANALYTICS_EVENTS.CAPA.DETAIL_VIEW_OPENED, {
        capa_id: capa.id,
        current_status: capa.status,
        owner_id: capa.ownerId,
        priority: capa.priority,
        is_private_to_admins: false, // Would need to check actual permission
      });
    }
  }, [capa, isCapaSuccess]);

  const handleStatusChange = async (newStatus: (typeof statusEnum.enumValues)[number]) => {
    try {
      const oldStatus = capa?.status;

      await updateCapa({
        id: capaId,
        status: newStatus,
      });

      // Get dynamic user role
      const getCurrentUserRole = (): 'Admin' | 'Technician' => {
        try {
          const userDataStr = sessionStorage.getItem('user-data');
          if (userDataStr) {
            const userData = JSON.parse(userDataStr);
            return (userData.userAccountType || 'Admin') as 'Admin' | 'Technician';
          }
        } catch (error) {
          console.warn('Could not get user role:', error);
        }
        return 'Admin';
      };

      // Determine trigger action based on status change
      const getTriggerAction = (
        from: string | undefined,
        to: string,
      ): 'Save Draft' | 'Submit' | 'Mark Complete' | 'Verify' | 'System - Overdue' => {
        if (to === 'Open' && from === 'Draft') return 'Submit';
        if (to === 'Completed' && from === 'Open') return 'Mark Complete';
        if (to === 'Verified' && from === 'Completed') return 'Verify';
        if (to === 'Draft') return 'Save Draft';
        return 'Submit';
      };

      // Track status change according to CSV spec
      track(ANALYTICS_EVENTS.CAPA.STATUS_CHANGED, {
        capa_id: capaId,
        previous_status: oldStatus,
        new_status: newStatus,
        triggered_by_role: getCurrentUserRole(),
        trigger_action: getTriggerAction(oldStatus, newStatus),
      });

      toast('Status Updated', {
        description: `CAPA status changed to ${STATUS_MAP[newStatus]}`,
      });
    } catch (error) {
      console.error('Error updating status:', error);
      toast('Error', {
        description: 'Failed to update status. Please try again.',
      });
    }
  };

  const handleArchiveToggle = async () => {
    try {
      await updateCapa({
        id: capaId,
        archived: !capa?.archived,
      });

      // Track CAPA archived
      track(ANALYTICS_EVENTS.CAPA.ARCHIVED, {
        capa_id: capaId,
        source: 'detail_page',
      });

      toast(capa?.archived ? 'CAPA Unarchived' : 'CAPA Archived', {
        description: capa?.archived
          ? 'The CAPA has been restored to active status.'
          : 'The CAPA has been moved to archive.',
      });

      setShowArchiveConfirm(false);
    } catch (error) {
      console.error('Error toggling archive:', error);
      toast('Error', {
        description: 'Failed to update archive status. Please try again.',
      });
    }
  };

  // Helper function to track edit initiated
  const handleEditClick = () => {
    if (capa) {
      track(ANALYTICS_EVENTS.CAPA.EDIT_INITIATED, {
        capa_id: capaId,
        source: 'detail_page',
      });
      navigate(ROUTES.BUILD_CAPA_EDIT_PATH(capa.id));
    }
  };

  // Helper function to track work order creation
  const handleWorkOrderClick = () => {
    if (capa) {
      track(ANALYTICS_EVENTS.CAPA.WORK_ORDER_CREATED, {
        capa_id: capaId,
        work_order_id: '', // Will be filled when work order is actually created
        trigger_point: 'detail_page',
      });
    }
    setShowWorkOrderModal(true);
  };

  // Loading state
  if (isCapaLoading) {
    return <CapaLoading />;
  }

  // Error state
  if (capaError || !capa) {
    return <CapaError />;
  }

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      {/* Archive/Unarchive Confirmation Dialog */}
      <AlertDialog open={showArchiveConfirm} onOpenChange={setShowArchiveConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{capa.archived ? 'Unarchive this CAPA?' : 'Archive this CAPA?'}</AlertDialogTitle>
            <AlertDialogDescription>
              {capa.archived
                ? 'This will restore the CAPA to active status and make it visible in the main list.'
                : "This will move the CAPA to the archive. It will still be accessible but won't appear in the main list."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleArchiveToggle}>
              {capa.archived ? 'Unarchive' : 'Archive'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <CreateWorkOrderModal
        showWorkOrderModal={showWorkOrderModal}
        setShowWorkOrderModal={setShowWorkOrderModal}
        capa={capa}
      />

      {/* Back Navigation */}
      <div className="mb-4">
        <Button variant="ghost" onClick={() => navigate(ROUTES.CAPA_LIST)}>
          <ChevronLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>

      {/* Title Header */}
      <div className="flex flex-col md:flex-row justify-between items-start mb-4 gap-3">
        {/* Desktop View */}
        <div className="hidden md:block w-full">
          <div className="flex flex-wrap gap-2 mb-2">
            <Badge variant="outline" className="bg-gray-100 text-gray-700">
              {capa.slug}
            </Badge>
            <StatusBadge status={capa.status} />
            <PriorityBadge priority={capa.priority} />
            <CapaTypeBadge type={capa.type} />
            {capa.archived && (
              <Badge className="bg-amber-50 text-amber-600 border-amber-200 font-medium" variant="outline">
                <Archive className="h-3 w-3 mr-1" />
                Archived
              </Badge>
            )}
          </div>

          <h1 className="text-2xl font-bold">{capa.title}</h1>
        </div>

        {/* Mobile View - with menu in title area */}
        <div className="md:hidden w-full">
          <div className="flex flex-wrap gap-2 mb-2">
            <Badge variant="outline" className="bg-gray-100 text-gray-700">
              {capa.slug}
            </Badge>

            <StatusBadge status={capa.status} />
            <PriorityBadge priority={capa.priority} />
            <CapaTypeBadge type={capa.type} />
            {capa.archived && (
              <Badge className="bg-amber-50 text-amber-600 border-amber-200 font-medium" variant="outline">
                <Archive className="h-3 w-3 mr-1" />
                Archived
              </Badge>
            )}
          </div>

          {/* Mobile title row with menu */}
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold mb-2 pr-3 flex-1">{capa.title}</h1>

            {/* Mobile menu in the title row */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 flex items-center justify-center"
                  aria-label="Open actions menu"
                >
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>CAPA Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />

                {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT) && (
                  <DropdownMenuItem className="h-11 cursor-pointer" onClick={handleEditClick}>
                    <Edit className="mr-2 h-4 w-4" />
                    <span>Edit CAPA</span>
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem className="h-11 cursor-pointer" onClick={handleWorkOrderClick}>
                  + Create Work Order
                </DropdownMenuItem>

                <DropdownMenuItem className="h-11 cursor-pointer" onClick={() => setShowArchiveConfirm(true)}>
                  {capa.archived ? (
                    <>
                      <ArchiveRestore className="mr-2 h-4 w-4" />
                      <span>Unarchive CAPA</span>
                    </>
                  ) : (
                    <>
                      <Archive className="mr-2 h-4 w-4" />
                      <span>Archive CAPA</span>
                    </>
                  )}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Desktop buttons - only shown on desktop */}
        <div className="hidden md:flex gap-2 self-start">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button aria-label="Create Work Order from CAPA" size="sm" onClick={handleWorkOrderClick}>
                  + Create Work Order
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Generate a Work Order from this CAPA</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT) && (
            <Button size="sm" aria-label="Edit CAPA" onClick={handleEditClick}>
              <Edit className="h-4 w-4" />
              <span>Edit</span>
            </Button>
          )}

          {/* More actions menu - desktop only */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>CAPA Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />

              {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.EDIT) && (
                <DropdownMenuItem className="h-11 cursor-pointer" onClick={() => setShowArchiveConfirm(true)}>
                  {capa.archived ? (
                    <>
                      <ArchiveRestore className="mr-2 h-4 w-4" />
                      <span>Unarchive</span>
                    </>
                  ) : (
                    <>
                      <Archive className="mr-2 h-4 w-4" />
                      <span>Archive</span>
                    </>
                  )}
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Location and Date Bar */}
      <div className="flex flex-wrap items-center text-sm text-gray-500 mb-6 gap-y-2 gap-x-2">
        <div className="flex items-center gap-1 ">
          <MapPin className="h-4 w-4" />
          <span>{capa?.location?.name || 'No location specified'}</span>
        </div>

        <div className="hidden sm:block text-muted-foreground">•</div>

        <div className="flex items-center gap-1">
          <Calendar className="h-4 w-4 " />
          <span>{capa.createdAt ? format(new Date(capa.createdAt), "MMM d, yyyy 'at' h:mm a") : ''}</span>
        </div>

        <div className="hidden sm:block text-muted-foreground">•</div>

        <div className="flex items-center gap-1">
          <User className="h-4 w-4 " />
          <span>{capa?.owner?.fullName || 'Unassigned'}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main content column */}
        <div className="lg:col-span-2 space-y-6">
          {/* AI Safety Insight - Enhanced */}
          <div className="bg-blue-50 rounded-lg border border-blue-200 shadow-sm mb-6">
            <div className="p-5">
              <p className="text-gray-800 leading-relaxed text-[15px] mb-4">
                {capa.eventId
                  ? `A ${capa.type} action of ${capa.priority} severity was linked to Safety Event #${capa.eventSlug} at ${capa?.location?.name || 'Unspecified location'} on ${capa.createdAt ? formatDate(capa.createdAt) : 'Unknown date'}.`
                  : `A ${capa.type} action of ${capa.priority} severity was created to address ${capa.rootCause ? capa.rootCause.replace(/_/g, ' ') : 'Not specified'} issues on ${capa.createdAt ? format(new Date(capa.createdAt), 'MMMM d, yyyy') : 'Unknown date'}.`}
              </p>

              <p className="text-gray-800 leading-relaxed text-[15px] mb-4">
                <span className="font-medium">Due Date:</span>{' '}
                {capa.dueDate ? format(new Date(capa.dueDate), 'MMMM d, yyyy') : 'No due date specified'}
                {capa.dueDate && new Date(capa.dueDate) < new Date() && overdue > 0 && (
                  <span className="text-red-600 font-medium ml-2">(Overdue by {overdue} days)</span>
                )}
              </p>

              {/* Root Cause Analysis Summary */}
              {capa.summary && (
                <div className="mt-4 pt-3 border-t border-blue-100">
                  <p className="text-indigo-900 text-sm font-medium mb-2">Root Cause Analysis Summary</p>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {(() => {
                      return capa.summary;
                    })()}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Proposed Actions Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Proposed Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-gray-700">
                {capa.actionsToAddress ? (
                  <ol className="list-decimal pl-5 space-y-2">
                    {capa.actionsToAddress
                      .split('\n')
                      .map((action: string, index: number) => {
                        // Remove any leading numbers (like "1. ", "2. ", etc.) to avoid duplicate numbering
                        const cleanedAction = action.replace(/^\d+\.\s*/, '');
                        return cleanedAction.trim() ? (
                          <li key={index} className="pl-1">
                            {cleanedAction}
                          </li>
                        ) : null;
                      })
                      .filter(Boolean)}
                  </ol>
                ) : (
                  'No proposed actions provided.'
                )}
              </div>
            </CardContent>
          </Card>

          {/* Actual Actions Taken Section */}
          {capa.actionsImplemented && capa.actionsImplemented.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Actual Actions Taken</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-900 mb-2 block">Actions Implemented:</label>

                    <div className="text-gray-700 p-3 bg-gray-50 rounded-md min-h-[80px]">
                      {capa.actionsImplemented || 'No actions implemented yet.'}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-900 mb-1 block">Implementation Date:</label>

                    <div className="text-gray-700">
                      {capa.implementationDate
                        ? new Date(capa.implementationDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          })
                        : 'Not specified'}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-900 mb-1 block">Implemented By:</label>
                    <div className="text-gray-700">{capa.implementedBy?.fullName || 'Not specified'}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Verification of Effectiveness (VoE) Section */}
          {(capa.voeDueDate || capa.verificationFindings || capa.voePerformedBy) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Verification of Effectiveness (VoE)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-900 mb-1 block">VoE Due Date:</label>
                    <div className="text-gray-700">
                      {capa.voeDueDate ? format(new Date(capa.voeDueDate), 'MMM d, yyyy') : 'Not specified'}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-900 mb-2 block">Verification Findings:</label>

                    <div className="text-gray-700 p-3 bg-gray-50 rounded-md min-h-[80px]">
                      {capa.verificationFindings || 'No verification findings documented yet.'}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-900 mb-1 block">VoE Performed By:</label>
                    <div className="text-gray-700">{capa.voePerformedBy?.fullName || 'Not specified'}</div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-900 mb-1 block">VoE Date:</label>

                    <div className="text-gray-700">
                      {capa.voeDate
                        ? new Date(capa.voeDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          })
                        : 'Not completed'}
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-900 mb-1 block">Effectiveness Status:</label>

                    <div className="text-gray-700">
                      {capa.effectivenessStatus && <EffectivenessBadge effectiveness={capa.effectivenessStatus} />}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Root Cause Analysis Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Root Cause Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* RCA Method Used */}
                <div>
                  <label className="text-sm font-medium text-gray-900 mb-1 block">RCA Method Used:</label>

                  <div className="text-gray-700">
                    {capa?.rcaMethod ? RCA_METHOD_MAP[capa.rcaMethod] : 'Not specified'}
                  </div>
                </div>

                {/* Identified Root Cause Category */}
                <div>
                  <label className="text-sm font-medium text-gray-900 mb-1 block">
                    Identified Root Cause Category:
                  </label>

                  <div className="text-gray-700">
                    <div className="flex items-center">
                      <span className="capitalize">{capa?.rootCause?.replace(/_/g, ' ')}</span>
                      {capa.rootCause === 'other' && capa.otherRootCause && (
                        <span className="ml-2 text-gray-600">({capa.otherRootCause})</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* RCA Findings & Conclusion */}
                <div>
                  <label className="text-sm font-medium text-gray-900 mb-2 block">RCA Findings & Conclusion:</label>

                  <div className="text-gray-700 bg-gray-50 rounded-md p-3 border">
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {capa.rcaFindings || 'No RCA findings provided.'}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Comments Section */}
          {/* TODO: Work with context to get the entity details */}
          <CommentsSection
            entityId={capaId}
            entityType="capa"
            entitySlug={capa.slug!}
            entityTitle={capa.title}
            status={capa.status}
          />
        </div>

        {/* Sidebar - metadata */}
        <div className="space-y-6">
          {/* Owner - moved to the first position */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Owner</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <Avatar className="h-8 w-8 mr-3">
                  <AvatarFallback>{capa?.owner?.firstName ? capa.owner.firstName.charAt(0) : '?'}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{capa?.owner?.fullName || 'Unassigned'}</p>
                  <p className="text-sm text-gray-500">
                    Due {capa.dueDate ? format(new Date(capa.dueDate), 'MMM d, yyyy') : 'Not set'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {capa.teamMembersToNotify && capa.teamMembersToNotify.length > 0 && (
            <NotifiedTeamMembers users={capa.teamMembersToNotify} />
          )}

          {capa.location && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 mb-4">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span>{capa.location?.name}</span>
                </div>
                {capa.asset && (
                  <div className="flex flex-col gap-2">
                    <p className="text-sm font-medium mb-2">Linked Asset</p>
                    <div className="flex flex-wrap items-center gap-2">
                      <Badge variant="outline" key={capa.asset.id}>
                        {capa.asset.name}
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Status Card - moved to second position */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <StatusBadge status={capa.status} />

                {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE) && (
                  <div className="mt-4">
                    <p className="text-sm text-gray-500 mb-2">Update Status:</p>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusChange('open')}
                        disabled={capa.status === 'open'}
                        className={capa.status === 'open' ? 'bg-gray-100' : ''}
                      >
                        Open
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusChange('in_review')}
                        disabled={capa.status === 'in_review'}
                        className={capa.status === 'in_review' ? 'bg-gray-100' : ''}
                      >
                        In Review
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleStatusChange('closed')}
                        disabled={capa.status === 'closed'}
                        className={capa.status === 'closed' ? 'bg-gray-100' : ''}
                      >
                        Closed
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Linked Items - moved to third position */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Linked To</CardTitle>
            </CardHeader>
            <CardContent>
              <LinkedTo capa={capa} workOrders={workOrders} />
            </CardContent>
          </Card>

          {/* Priority */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Priority</CardTitle>
            </CardHeader>
            <CardContent>
              <PriorityBadge priority={capa.priority} />
            </CardContent>
          </Card>

          {/* Tags */}
          {capa.tags && capa.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {capa.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="bg-gray-100">
                      {CAPA_TAGS_MAP[tag as (typeof capaTagsEnum.enumValues)[number]]}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Attachments */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Attachments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Show existing attachments or empty state */}
                {capa.attachments && capa.attachments.length > 0 ? (
                  <div>
                    {capa.attachments
                      .filter((attachment) => attachment && attachment.name && attachment.name.trim() !== '')
                      .map((attachment: { name: string; url: string; type: string; size: number }, index: number) => (
                        <div key={index} className="flex items-center justify-between py-2">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 mr-2 text-blue-500" />
                            <span
                              className="text-sm text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
                              onClick={() => window.open(attachment.url, '_blank')}
                            >
                              {attachment.name}
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownload(attachment.url, attachment.name)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-gray-500 text-sm py-4 text-center">No attachments added yet</div>
                )}
              </div>
            </CardContent>
          </Card>

          <Timeline entityId={capaId} entityType="capa" />
        </div>
      </div>
    </div>
  );
}
