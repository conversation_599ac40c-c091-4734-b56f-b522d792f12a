import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { IncidentsEmpty } from '@/components/incidents/list/incidents-empty';
import { IncidentsError } from '@/components/incidents/list/incidents-error';
import { Filters } from '@/components/incidents/list/incidents-filters';
import { IncidentsLoading } from '@/components/incidents/list/incidents-loading';
import { MobileFilters } from '@/components/incidents/list/incidents-mobile-filters';
import { IncidentsMobileView } from '@/components/incidents/list/incidents-mobile-view';
import { IncidentsTable } from '@/components/incidents/list/incidents-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAnalytics } from '@/hooks/use-analytics';
import { useIsMobile } from '@/hooks/use-mobile';
import { useInfiniteIncidents } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { EventsFilters, ReportTypeSchema, SeveritySchema, StatusSchema } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { useDebounce } from '@uidotdev/usehooks';
import { Search, X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'wouter';
import z from 'zod';

export default function IncidentLog() {
  const [_, navigate] = useLocation();
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const { hasPermission } = usePermissions();
  const analytics = useAnalytics();

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const [filters, setFilters] = useState<EventsFilters>({
    status: [],
    type: [],
    severity: [],
    oshaReportable: undefined,
    includeArchived: false,
    locationIds: [],
  });

  const {
    data: incidents,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isFetchedAfterMount,
  } = useInfiniteIncidents({
    search: debouncedSearchTerm,
    filters,
    enabled: true,
  });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Track page view on component mount (only once)
  useEffect(() => {
    // since sorting is not applied yet in incident list page, keeping static values for now
    if (isFetchedAfterMount) {
      analytics.track(ANALYTICS_EVENTS.EVENT.LOG_VIEW_OPENED, {
        default_sort_by: 'reportedAt',
        default_sort_order: 'desc',
      });
    }
  }, [isFetchedAfterMount]);

  // Track search when debounced search term changes
  useEffect(() => {
    if (debouncedSearchTerm.trim() && incidents) {
      analytics.track(ANALYTICS_EVENTS.EVENT.SEARCH_PERFORMED, {
        search_term: debouncedSearchTerm,
        result_count: incidents.length,
      });
    }
  }, [debouncedSearchTerm, incidents]);

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      (filters.status?.length ?? 0) +
      (filters.type?.length ?? 0) +
      (filters.severity?.length ?? 0) +
      (filters.oshaReportable !== undefined ? 1 : 0) +
      (filters.includeArchived ? 1 : 0) +
      (debouncedSearchTerm.trim() ? 1 : 0) +
      (filters.locationIds?.length ?? 0)
    );
  }, [filters, debouncedSearchTerm]);

  const toggleFilter = (
    type: 'status' | 'type' | 'severity',
    value: z.infer<typeof StatusSchema> | z.infer<typeof ReportTypeSchema> | z.infer<typeof SeveritySchema>,
  ) => {
    setFilters((prev) => {
      const currentFilters = [...(prev[type] ?? [])];
      const index = currentFilters.indexOf(value);

      if (index > -1) {
        currentFilters.splice(index, 1);
      } else {
        currentFilters.push(value);
      }

      // Track filter applied
      analytics.track(ANALYTICS_EVENTS.EVENT.FILTER_APPLIED, {
        filter_name: type,
        filter_value: value,
        include_archived_toggle_state: prev.includeArchived,
      });

      return { ...prev, [type]: currentFilters };
    });
  };

  const trackFilterApplied = (type: 'status' | 'type' | 'severity' | 'locationIds', value: string) => {
    analytics.track(ANALYTICS_EVENTS.EVENT.FILTER_APPLIED, {
      filter_name: type,
      filter_value: value,
      include_archived_toggle_state: filters.includeArchived,
    });
  };

  // Toggle OSHA reportable filter
  const toggleOshaFilter = () => {
    setFilters((prev) => {
      const newValue = prev.oshaReportable === undefined ? true : prev.oshaReportable === true ? false : undefined;

      // Track filter applied
      analytics.track(ANALYTICS_EVENTS.EVENT.FILTER_APPLIED, {
        filter_name: 'oshaReportable',
        filter_value: newValue?.toString() ?? 'undefined',
        include_archived_toggle_state: prev.includeArchived,
      });

      return {
        ...prev,
        oshaReportable: newValue,
      };
    });
  };

  // Reset all filters
  const resetFilters = () => {
    // Track filter reset
    analytics.track(ANALYTICS_EVENTS.EVENT.FILTER_RESET, {});

    setFilters({
      status: [],
      type: [],
      severity: [],
      oshaReportable: undefined,
      includeArchived: false,
      locationIds: [],
    });
    setSearchTerm('');
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl font-bold mb-4 md:mb-0">Safety Events</h1>
        <div className="flex items-center w-full md:w-auto">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search incidents..."
              className="pl-8 pr-4"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button variant="ghost" size="icon" className="absolute right-0 top-0 " onClick={() => setSearchTerm('')}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="ml-4">
            {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE) && (
              <Button
                onClick={() => {
                  analytics.track(ANALYTICS_EVENTS.EVENT.FORM_VIEWED, {
                    form_entry_point: 'Event Log',
                  });
                  navigate(ROUTES.INCIDENT_NEW);
                }}
              >
                + Create Safety Event
              </Button>
            )}
          </div>
          <MobileFilters
            toggleFilter={toggleFilter}
            filters={filters}
            toggleOshaFilter={toggleOshaFilter}
            setFilters={setFilters}
            activeFilterCount={activeFilterCount}
            resetFilters={resetFilters}
            trackFilterApplied={trackFilterApplied}
          />
        </div>
      </div>

      {/* Filters bar */}
      <Filters
        toggleFilter={toggleFilter}
        filters={filters}
        toggleOshaFilter={toggleOshaFilter}
        setFilters={setFilters}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        trackFilterApplied={trackFilterApplied}
      />
      {/* Error state */}
      {error ? <IncidentsError /> : null}

      {/* Loading state */}
      {isLoading ? <IncidentsLoading /> : null}

      {/* Empty state - when no incidents are found and not loading */}
      {incidents && incidents.length === 0 && !isLoading && !error && (
        <IncidentsEmpty hasActiveFilters={activeFilterCount > 0} onResetFilters={resetFilters} />
      )}

      {/* Desktop Table View */}
      {!isMobile && incidents && incidents.length > 0 ? (
        <IncidentsTable incidents={incidents} activeFilterCount={activeFilterCount} resetFilters={resetFilters} />
      ) : null}

      {/* Mobile Card View */}
      {isMobile && incidents && incidents.length > 0 ? (
        <IncidentsMobileView incidents={incidents} activeFilterCount={activeFilterCount} resetFilters={resetFilters} />
      ) : null}

      {/* Load More Button for Infinite Scrolling */}
      {incidents && incidents.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${incidents.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
