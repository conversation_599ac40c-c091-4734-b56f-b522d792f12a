import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { AccessPointBulkImport } from '@/components/access-points/access-point-bulk-import';
import { AccessPointBulkActionsDropdown } from '@/components/access-points/access-point-bulk-actions-dropdown';
import { TransientAccessPoint } from '@/components/access-points/access-point.types';
import { AccessPointsEmpty } from '@/components/access-points/access-points-empty';
import { AccessPointsError } from '@/components/access-points/access-points-error';
import { Filters } from '@/components/access-points/access-points-filters';
import { AccessPointsLoading } from '@/components/access-points/access-points-loading';
import { MobileFilters } from '@/components/access-points/access-points-mobile-filters';
import { AccessPointsMobileView } from '@/components/access-points/access-points-mobile-view';
import { AccessPointsTable } from '@/components/access-points/access-points-table';
import { BulkImportSummaryModal } from '@/components/access-points/bulk-import-summary-modal';
import { CreateAccessPointModal } from '@/components/access-points/create-access-point-modal';
import { QrViewingModal } from '@/components/access-points/qr-viewing-modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAnalytics } from '@/hooks/use-analytics';
import { useInfiniteAccessPoints } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { generatePublicIncidentUrl } from '@/lib/utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { AccessPointsFilters, BulkImportResult } from '@shared/schema.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { useDebounce } from '@uidotdev/usehooks';
import { Search, X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'wouter';

export default function AccessPointsView() {
  const [_, navigate] = useLocation();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isBulkImportOpen, setIsBulkImportOpen] = useState<boolean>(false);
  const [isQrModalOpen, setIsQrModalOpen] = useState<boolean>(false);
  const [selectedAccessPoint, setSelectedAccessPoint] = useState<TransientAccessPoint | undefined>(undefined);
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState<boolean>(false);
  const [bulkImportResult, setBulkImportResult] = useState<BulkImportResult | null>(null);

  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const [location] = useLocation();
  const { hasPermission } = usePermissions();
  const { track } = useAnalytics();

  const [filters, setFilters] = useState<AccessPointsFilters>({
    status: [],
    locationId: [],
    createdBy: [],
    includeArchived: false,
    createdDateRange: {
      from: undefined,
      to: undefined,
    },
  });

  const {
    data: accessPoints,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isFetchedAfterMount,
  } = useInfiniteAccessPoints({ search: debouncedSearchTerm, filters });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  // Track page view on component mount
  useEffect(() => {
    if (isFetchedAfterMount) {
      track(ANALYTICS_EVENTS.ACCESS_POINT.TABLE_VIEW_OPENED, {});
    }
  }, [isFetchedAfterMount]);

  // Track search when debounced search term changes
  useEffect(() => {
    if (debouncedSearchTerm.trim() && accessPoints) {
      track(ANALYTICS_EVENTS.ACCESS_POINT.SEARCH_PERFORMED, {
        search_term: debouncedSearchTerm,
        result_count: accessPoints.length,
      });
    }
  }, [debouncedSearchTerm, accessPoints]);

  // Check for create query parameter and open modal
  useEffect(() => {
    if (location === ROUTES.ACCESS_POINTS_NEW) {
      setIsModalOpen(true);
      // Track create initiated when modal opens via URL
      track(ANALYTICS_EVENTS.ACCESS_POINT.CREATE_INITIATED, {});
    } else {
      // Close modal if navigating away from the new route
      setIsModalOpen(false);
    }
  }, [location]);

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      status: [],
      locationId: [],
      createdBy: [],
      includeArchived: false,
      createdDateRange: {
        from: undefined,
        to: undefined,
      },
    });
    setSearchTerm('');
  };

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      (filters.status?.length ?? 0) +
      (filters.locationId?.length ?? 0) +
      (filters.createdBy?.length ?? 0) +
      (filters.createdDateRange?.from || filters.createdDateRange?.to ? 1 : 0) +
      (filters.includeArchived ? 1 : 0) +
      (debouncedSearchTerm.trim() ? 1 : 0)
    );
  }, [filters, debouncedSearchTerm]);

  // Handle adding or removing a filter
  const toggleFilter = (type: 'status' | 'locationId' | 'createdBy', value: string) => {
    setFilters((prev) => {
      const currentFilters = [...(prev[type] ?? [])];
      const index = currentFilters.indexOf(value);

      if (index > -1) {
        currentFilters.splice(index, 1);
      } else {
        currentFilters.push(value);
      }

      return { ...prev, [type]: currentFilters };
    });
  };

  const trackFilterApplied = (
    type: 'status' | 'locationId' | 'createdBy' | 'createdDateRange' | 'includeArchived',
    value: string,
  ) => {
    track(ANALYTICS_EVENTS.ACCESS_POINT.FILTER_APPLIED, {
      filter_name: type as string,
      filter_value: value,
    });
  };

  // Handle bulk import success
  const handleBulkImportSuccess = (result: BulkImportResult) => {
    track(ANALYTICS_EVENTS.ACCESS_POINT.BULK_IMPORT_SUCCESS, {
      total_count: result.total,
      success_count: result.created,
      error_count: result.failed,
    });
    setBulkImportResult(result);
    setIsBulkImportOpen(false);
    setIsSummaryModalOpen(true);
  };

  // Handle viewing QR code for an existing access point
  const handleViewQRCode = async (accessPoint: TransientAccessPoint, qrCodeUrl: string) => {
    setSelectedAccessPoint(accessPoint);

    if (qrCodeUrl) {
      // If the access point already has a QR code URL, just show it
      setQrCodeUrl(qrCodeUrl);
      setIsQrModalOpen(true);
      return;
    }

    // Otherwise, generate a QR code URL for this access point
    setIsGenerating(true);
    try {
      const url = generatePublicIncidentUrl(accessPoint);

      // Store the QR code URL
      setQrCodeUrl(url);
      setIsQrModalOpen(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
      // TODO: Show error message to user via toast or alert
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle form submission - generate QR code
  const handleGenerateQR = async (createdAccessPoint: RouterOutputs['accessPoint']['create']) => {
    setIsGenerating(true);

    try {
      const url = generatePublicIncidentUrl(createdAccessPoint);

      // Store the QR code URL
      setQrCodeUrl(url);
      // After successful generation, close the creation modal
      setIsModalOpen(false);

      // Track access point created
      track(ANALYTICS_EVENTS.ACCESS_POINT.CREATED, {
        access_point_id: createdAccessPoint.id,
        access_point_name: createdAccessPoint.name,
        location_id: createdAccessPoint.locationId || undefined,
        has_label_text: Boolean(createdAccessPoint.description),
      });

      // Set the created access point as selected and show QR modal immediately
      setSelectedAccessPoint(createdAccessPoint);
      setIsQrModalOpen(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl font-bold mb-4 md:mb-0">QR Code Access Points</h1>
        <div className="flex items-center w-full md:w-auto">
          <div className="relative flex-1 md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search access points..."
              className="pl-8 pr-4"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button variant="ghost" size="sm" className="absolute right-0 top-0 " onClick={() => setSearchTerm('')}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="ml-4 flex items-center gap-2">
            {hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE) && (
              <>
                <AccessPointBulkActionsDropdown onImportClick={() => setIsBulkImportOpen(true)} />
                <Button
                  onClick={() => {
                    setIsModalOpen(true);
                    track(ANALYTICS_EVENTS.ACCESS_POINT.CREATE_INITIATED, {});
                  }}
                >
                  + Create Access Point
                </Button>
              </>
            )}
          </div>

          <MobileFilters
            activeFilterCount={activeFilterCount}
            filters={filters}
            setFilters={setFilters}
            toggleFilter={toggleFilter}
            resetFilters={resetFilters}
            trackFilterApplied={trackFilterApplied}
          />
        </div>
      </div>

      {/* Desktop Filters bar */}
      <Filters
        filters={filters}
        setFilters={setFilters}
        toggleFilter={toggleFilter}
        activeFilterCount={activeFilterCount}
        resetFilters={resetFilters}
        trackFilterApplied={trackFilterApplied}
      />

      {/* Error state */}
      {error && <AccessPointsError />}

      {/* Loading state */}
      {isLoading && <AccessPointsLoading />}

      <QrViewingModal
        isQrModalOpen={isQrModalOpen}
        setIsQrModalOpen={setIsQrModalOpen}
        accessPoint={selectedAccessPoint}
        qrCodeUrl={qrCodeUrl}
      />

      {/* Create Access Point Modal */}
      {hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE) && (
        <CreateAccessPointModal
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
          isGenerating={isGenerating}
          handleGenerateQR={handleGenerateQR}
        />
      )}

      {/* Bulk Import Modal */}
      <AccessPointBulkImport
        isOpen={isBulkImportOpen}
        onClose={() => setIsBulkImportOpen(false)}
        onSuccess={handleBulkImportSuccess}
      />

      {/* Bulk Import Summary Modal */}
      <BulkImportSummaryModal
        isOpen={isSummaryModalOpen}
        onClose={() => setIsSummaryModalOpen(false)}
        result={bulkImportResult}
      />

      {/* Desktop Table View */}
      {!isLoading && !error && accessPoints && accessPoints.length > 0 && (
        <AccessPointsTable accessPoints={accessPoints} onViewQRCode={handleViewQRCode} qrCodeUrl={qrCodeUrl} />
      )}

      {/* Mobile Card View */}
      {!isLoading && !error && accessPoints && accessPoints.length > 0 && (
        <AccessPointsMobileView accessPoints={accessPoints} onViewQRCode={handleViewQRCode} qrCodeUrl={qrCodeUrl} />
      )}

      {/* Empty state - when no access points are found and not loading */}
      {!isLoading && !error && accessPoints && accessPoints.length === 0 && (
        <AccessPointsEmpty
          hasActiveFilters={activeFilterCount > 0}
          onResetFilters={resetFilters}
          onCreateAccessPoint={() => {
            setIsModalOpen(true);
            track(ANALYTICS_EVENTS.ACCESS_POINT.CREATE_INITIATED, {});
          }}
        />
      )}

      {/* Load More Button for Infinite Scrolling */}
      {accessPoints && accessPoints.length > 0 && hasNextPage && (
        <div className="flex justify-center mt-6">
          <Button onClick={() => fetchNextPage()} disabled={isFetchingNextPage} variant="outline" className="px-6">
            {isFetchingNextPage ? 'Loading more...' : `Load More (${accessPoints.length} loaded)`}
          </Button>
        </div>
      )}
    </div>
  );
}
