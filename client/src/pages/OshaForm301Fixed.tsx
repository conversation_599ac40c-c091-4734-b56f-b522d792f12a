// eslint-disable
// @ts-nocheck

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

import {
  Activity,
  AlertCircle,
  AlertTriangle,
  ArrowLeft,
  Calendar,
  Check,
  CheckCircle,
  Clock,
  Download,
  Edit,
  ExternalLink,
  Eye,
  FileDown,
  FileText,
  HelpCircle,
  Info,
  Link as LinkIcon,
  LinkIcon as LinkIconSolid,
  MapPin,
  PenLine,
  Save,
  Send,
  Settings,
  X,
} from 'lucide-react';

// OSHA Form 301 schema
const oshaForm301Schema = z.object({
  // Section 1 - Employee Info
  employeeName: z.string().optional(),
  jobTitle: z.string().min(1, 'Job title is required'),
  department: z.string().optional(),
  dateOfHire: z.string().optional(),
  shift: z.string().optional(),
  supervisor: z.string().optional(),
  privacyCase: z.boolean().default(false),

  // Section 2 - Incident Details
  dateOfInjury: z.string().min(1, 'Date is required'),
  timeOfEvent: z.string().min(1, 'Time is required'),
  location: z.string().optional(),
  description: z.string().min(1, 'Description is required'),
  specificTask: z.string().optional(),
  objectCaused: z.string().optional(),
  equipmentInvolved: z.string().optional(),

  // Section 3 - Nature of Injury
  bodyPartAffected: z.string().min(1, 'Body part is required'),
  injuryType: z.string().min(1, 'Injury type is required'),
  treatmentLocation: z.string().optional(),
  typeOfCare: z.string().optional(),
  prescriptionGiven: z.boolean().default(false),

  // Section 4 - OSHA Questions
  wasHospitalized: z.boolean().default(false),
  resultedInDeath: z.boolean().default(false),
  daysAwayFromWork: z.string().optional(),
  daysOfRestrictedDuty: z.string().optional(),

  // Section 5 - People & Witnesses
  witnesses: z.array(z.string()).optional(),
  reportedBy: z.string().optional(),
  preparedBy: z.string().optional(),
  preparedDate: z.string().optional(),

  // Section 6 - Corrective Actions
  rootCause: z.string().optional(),
  correctiveActions: z.string().optional(),

  // Section 7 - OSHA Reporting
  primaryRecordableOutcome: z.string().min(1, 'Primary Recordable Outcome is required'),
  reportableReason: z.string().optional(),

  // Linked safety event
  linkedIncidentId: z.number().min(1, 'Linked Safety Event is required'),

  // Form metadata
  oshaSubmittedBy: z.string().optional(),
  oshaSubmittedAt: z.string().optional(),
});

type OshaForm301Values = z.infer<typeof oshaForm301Schema>;

export default function OshaForm301() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  // Extract parameters from URL
  const params = new URLSearchParams(window.location.search);
  const incidentId = params.get('incidentId');
  const formId = params.get('id'); // Get form ID for editing existing OSHA forms

  // Get the mode from URL parameters
  const isViewMode = params.get('mode') === 'view';
  const isEditMode = params.get('mode') === 'edit';

  const [progress, setProgress] = useState(0);
  const [completionStatus, setCompletionStatus] = useState<'not-started' | 'in-progress' | 'complete'>('not-started');
  const [isSubmitted, setIsSubmitted] = useState(false); // Determine from API data
  const [existingFormData, setExistingFormData] = useState<any>(null);

  // Generate OSHA Log ID (for display purposes)
  const getOshaLogId = (): string => {
    const year = new Date().getFullYear();
    if (incidentId) {
      return `OSHA-${year}-${incidentId.padStart(4, '0') || '0000'}`;
    } else {
      // For standalone entries, use timestamp
      const timestamp = new Date().getTime().toString().slice(-6);
      return `OSHA-${year}-S${timestamp}`;
    }
  };

  // Debug to console - helps us see current state
  console.log('OSHA Form Mode:', { isViewMode, isEditMode, isSubmitted });

  // Standalone mode when no incidentId is provided
  const isStandaloneMode = !incidentId;

  // State for safety event search dropdown
  const [safetyEventSearchOpen, setSafetyEventSearchOpen] = useState(false);
  const [safetyEventSearchValue, setSafetyEventSearchValue] = useState('');

  // Define Safety Event interface for TypeScript
  interface SafetyEvent {
    id: number;
    title: string;
    dateTime: string;
    location: string;
    reportType: string;
    status: string;
    severity?: string;
    description?: string;
    equipmentInvolved?: string;
  }

  // Query all safety events for the dropdown
  const { data: allSafetyEvents, isLoading: isLoadingSafetyEvents } = useQuery<SafetyEvent[]>({
    queryKey: ['/api/incidents'],
    enabled: !isViewMode, // Only load when not in view mode
  });

  // Query safety event data if there's an incidentId
  const {
    data: safetyEvent,
    isLoading: safetyEventLoading,
    error: safetyEventError,
  } = useQuery({
    queryKey: incidentId ? [`/api/incidents/${incidentId}`] : ['no-safety-event'],
    queryFn: ({ queryKey }) =>
      queryKey[0] !== 'no-safety-event' ? fetch(queryKey[0].toString()).then((res) => res.json()) : null,
    enabled: !!incidentId && !formId, // Only fetch safety event if no formId (not editing existing form)
  });

  // Query existing OSHA form data if in edit mode with a formId
  const {
    data: existingOshaForm,
    isLoading: formLoading,
    error: formError,
  } = useQuery({
    queryKey: formId ? [`/api/osha-forms/${formId}`] : ['no-form'],
    queryFn: async ({ queryKey }) => {
      if (queryKey[0] === 'no-form') return null;

      try {
        // First try to fetch directly from the specific form API
        const directResponse = await fetch(queryKey[0].toString());

        if (directResponse.ok) {
          const formData = await directResponse.json();
          console.log('Direct API found form:', formData);
          return formData;
        }

        // If direct API fails, fall back to logs API
        console.log('Direct API fetch failed, falling back to logs API');
        const logsResponse = await fetch('/api/osha-logs');
        const logs = await logsResponse.json();

        // Find the specific form by ID
        if (Array.isArray(logs) && formId) {
          const foundForm = logs.find((log: any) => log.id && log.id.toString() === formId);
          console.log('Found existing OSHA form from logs API:', foundForm);
          return foundForm;
        }

        return null;
      } catch (error) {
        console.error('Error fetching OSHA form data:', error);
        return null;
      }
    },
    enabled: !!formId && isEditMode,
  });

  // Combined loading and error states
  const isLoading = safetyEventLoading || formLoading;
  const error = safetyEventError || formError;

  const form = useForm<OshaForm301Values>({
    resolver: zodResolver(oshaForm301Schema),
    defaultValues: {
      // Section 1 - Employee Info
      employeeName: 'John Smith',
      jobTitle: 'Technician',
      department: 'Maintenance',
      dateOfHire: '2022-03-15',
      shift: 'Day',
      supervisor: 'David Martinez',
      privacyCase: false,

      // Section 2 - Incident Details
      dateOfInjury: '2025-05-01',
      timeOfEvent: '11:44',
      location: 'Line 4 – Near Pump 42',
      description:
        'Technician slipped on fluid and nearly fell, caught themselves on railing. Incident due to hydraulic seal failure.',
      specificTask: 'Performing post-maintenance inspection',
      objectCaused: 'Hydraulic fluid',
      equipmentInvolved: 'Pump 42',

      // Section 3 - Nature of Injury
      bodyPartAffected: 'Hand',
      injuryType: 'Fracture',
      treatmentLocation: "St. John's Hospital",
      typeOfCare: 'Emergency Room',
      prescriptionGiven: false,

      // Section 4 - OSHA Questions
      wasHospitalized: true,
      resultedInDeath: false,
      daysAwayFromWork: '5',
      daysOfRestrictedDuty: '10',

      // Section 5 - People & Witnesses
      witnesses: ['Jane Smith (Operator)', 'Derek Lin (Line Supervisor)'],
      reportedBy: 'Jane Smith',
      preparedBy: 'Alex Johnson',
      preparedDate: '2025-05-02',

      // Section 6 - Corrective Actions
      rootCause: 'Faulty hydraulic seal resulted in leak and slip hazard',
      correctiveActions: 'Area taped off, technician sent for evaluation, maintenance ticket created',

      // Section 7 - OSHA Reporting
      primaryRecordableOutcome: 'Medical Treatment Beyond First Aid',
      reportableReason: 'Hospitalization',

      // Linked safety event
      linkedIncidentId: incidentId ? parseInt(incidentId) : undefined,

      // Form metadata
      oshaSubmittedBy: 'Alex Johnson', // For testing purposes
      oshaSubmittedAt: new Date().toISOString(), // For testing purposes
    },
  });

  // Auto-fill form with safety event data when loaded
  useEffect(() => {
    if (safetyEvent) {
      // Extract date and time from safetyEvent.dateTime
      const date = new Date(safetyEvent.dateTime);
      const dateStr = format(date, 'yyyy-MM-dd');
      const timeStr = format(date, 'HH:mm');

      form.reset({
        ...form.getValues(),
        dateOfInjury: dateStr,
        timeOfEvent: timeStr,
        location: safetyEvent.location || '',
        description: safetyEvent.description || '',

        // Simple extraction for body part and injury type
        bodyPartAffected: extractBodyPart(safetyEvent.description || ''),
        injuryType: extractInjuryType(safetyEvent.description || ''),

        // OSHA flags
        wasHospitalized: false,
        primaryRecordableOutcome: 'Medical Treatment Beyond First Aid',
      });

      // Calculate form completion progress
      updateProgress();
    }
  }, [safetyEvent]);

  // Set submission status based on existing form data
  useEffect(() => {
    if (existingOshaForm) {
      // Check if the form status indicates it's submitted vs draft
      const formStatus = existingOshaForm.status || existingOshaForm.formData?.status;
      setIsSubmitted(formStatus === 'Submitted');
    }
  }, [existingOshaForm]);

  // Auto-fill form with existing OSHA form data when loaded (for edit mode)
  useEffect(() => {
    if (existingOshaForm && isEditMode) {
      console.log('Pre-filling form with existing OSHA data:', existingOshaForm);

      try {
        // Extract form data from the existingOshaForm
        const formData = existingOshaForm.formData || {};

        // Handle date formatting
        let dateOfInjury = formData.dateOfInjury;
        if (!dateOfInjury && existingOshaForm.dateTime) {
          // If dateOfInjury isn't in formData, try to use the dateTime from the main object
          dateOfInjury = existingOshaForm.dateTime;
          if (typeof dateOfInjury === 'string' && dateOfInjury.includes('T')) {
            dateOfInjury = dateOfInjury.split('T')[0];
          }
        }

        // Set linked safety event ID
        let linkedIncidentId = formData.linkedIncidentId || existingOshaForm.linkedIncidentId || undefined;
        if (typeof linkedIncidentId === 'string') {
          linkedIncidentId = parseInt(linkedIncidentId);
        }

        // Create values object with all possible fields
        const values: OshaForm301Values = {
          employeeName: formData.employeeName || existingOshaForm.employee || '',
          jobTitle: formData.jobTitle || '',
          privacyCase: formData.privacyCase || existingOshaForm.privacyCase || false,
          dateOfInjury: dateOfInjury || format(new Date(), 'yyyy-MM-dd'),
          timeOfEvent: formData.timeOfEvent || '12:00',
          location: formData.location || existingOshaForm.location || '',
          description: formData.description || '',
          bodyPartAffected: formData.bodyPartAffected || '',
          injuryType: formData.injuryType || existingOshaForm.title?.split(': ')[1] || '',
          treatmentLocation: formData.treatmentLocation || '',
          typeOfCare: formData.typeOfCare || '',
          prescriptionGiven: formData.prescriptionGiven || false,
          wasHospitalized: formData.wasHospitalized || false,
          resultedInDeath: formData.resultedInDeath || false,
          daysAwayFromWork: formData.daysAwayFromWork || '',
          daysOfRestrictedDuty: formData.daysOfRestrictedDuty || '',
          department: formData.department || '',
          dateOfHire: formData.dateOfHire || '',
          shift: formData.shift || '',
          supervisor: formData.supervisor || '',
          specificTask: formData.specificTask || '',
          objectCaused: formData.objectCaused || '',
          equipmentInvolved: formData.equipmentInvolved || '',
          witnesses: formData.witnesses || [],
          reportedBy: formData.reportedBy || '',
          preparedBy: formData.preparedBy || '',
          preparedDate: formData.preparedDate || '',
          rootCause: formData.rootCause || '',
          correctiveActions: formData.correctiveActions || '',
          primaryRecordableOutcome: formData.primaryRecordableOutcome || 'Medical Treatment Beyond First Aid',
          reportableReason: formData.reportableReason || '',
          linkedIncidentId: linkedIncidentId,
          oshaSubmittedBy: formData.oshaSubmittedBy || existingOshaForm.submittedBy || '',
          oshaSubmittedAt: formData.oshaSubmittedAt || existingOshaForm.submittedAt || new Date().toISOString(),
        };

        console.log('Setting form values:', values);

        // Reset form with the values
        form.reset(values);

        // Calculate form completion progress
        updateProgress();
      } catch (error) {
        console.error('Error pre-filling form with existing data:', error);
        toast({
          title: 'Error Loading Form Data',
          description: 'There was a problem loading the existing form data. Some fields may not be pre-filled.',
          variant: 'destructive',
        });
      }
    }
  }, [existingOshaForm, isEditMode]);

  // Watch form values to update progress
  useEffect(() => {
    const subscription = form.watch(() => {
      updateProgress();
    });
    return () => subscription.unsubscribe();
  }, [form.watch]);

  // Simple extraction functions (these would ideally be replaced with AI suggestions)
  function extractBodyPart(description: string): string {
    const bodyParts = ['head', 'neck', 'arm', 'hand', 'finger', 'leg', 'foot', 'back', 'chest', 'eye'];
    for (const part of bodyParts) {
      if (description.toLowerCase().includes(part)) {
        return part.charAt(0).toUpperCase() + part.slice(1);
      }
    }
    return '';
  }

  function extractInjuryType(description: string): string {
    const injuryTypes = ['cut', 'burn', 'fracture', 'sprain', 'bruise', 'contusion', 'laceration'];
    for (const type of injuryTypes) {
      if (description.toLowerCase().includes(type)) {
        return type.charAt(0).toUpperCase() + type.slice(1);
      }
    }
    return '';
  }

  function updateProgress() {
    const formValues = form.getValues();

    // For privacy cases, don't require employeeName
    let requiredFields = ['jobTitle', 'dateOfInjury', 'timeOfEvent', 'description', 'bodyPartAffected', 'injuryType'];

    // Only add employeeName as required if it's not a privacy case
    if (!formValues.privacyCase) {
      requiredFields.push('employeeName');
    }

    const filledFields = requiredFields.filter((field) => formValues[field as keyof OshaForm301Values]).length;

    const calculatedProgress = Math.round((filledFields / requiredFields.length) * 100);
    setProgress(calculatedProgress);

    if (calculatedProgress === 0) {
      setCompletionStatus('not-started');
    } else if (calculatedProgress < 100) {
      setCompletionStatus('in-progress');
    } else {
      setCompletionStatus('complete');
    }
  }

  async function onSubmit(data: OshaForm301Values) {
    console.log('OSHA Form Data:', data);

    try {
      if (isEditMode && formId) {
        // Update existing OSHA form with complete data set
        const completeFormData = {
          ...data,
          // Ensure key fields are present with defaults if needed
          linkedIncidentId: incidentId ? parseInt(incidentId) : data.linkedIncidentId || null,
          privacyCase: data.privacyCase || false,
          bodyPartAffected: data.bodyPartAffected || '',
          injuryType: data.injuryType || '',
          dateOfInjury: data.dateOfInjury || new Date().toISOString().split('T')[0],
          timeOfEvent: data.timeOfEvent || '12:00',
          location: data.location || '',
          description: data.description || '',
          oshaUpdatedAt: new Date().toISOString(),
        };

        console.log('Updating OSHA form with complete data:', completeFormData);

        await apiRequest('PATCH', `/api/osha-forms/${formId}`, {
          formData: completeFormData,
          updatedBy: 'Alex Johnson', // In a real app, this would be the logged-in user
          updatedAt: new Date(),
        });

        toast({
          title: 'OSHA Form Updated',
          description: 'The OSHA form has been updated successfully.',
          duration: 5000,
        });

        // Invalidate OSHA logs data to refresh the list and show the updated submission
        queryClient.invalidateQueries({ queryKey: ['/api/osha-logs'] });
        queryClient.invalidateQueries({ queryKey: ['/api/osha-summary'] });

        // Navigate back to the proper view page
        setTimeout(() => {
          // Get the updated form data to find the logId
          apiRequest('GET', `/api/osha-forms/${formId}`)
            .then((updatedForm: any) => {
              if (updatedForm?.logId) {
                setLocation(`/osha/reports/${updatedForm.logId}`);
              } else {
                setLocation('/osha-logs');
              }
            })
            .catch(() => {
              setLocation('/osha-logs');
            });
        }, 1000);
      } else if (incidentId) {
        // Create new OSHA form linked to a safety event - ensure complete data is stored
        const completeFormData = {
          ...data,
          // Ensure key fields are present with defaults if needed
          linkedIncidentId: parseInt(incidentId),
          privacyCase: data.privacyCase || false,
          bodyPartAffected: data.bodyPartAffected || '',
          injuryType: data.injuryType || '',
          dateOfInjury: data.dateOfInjury || new Date().toISOString().split('T')[0],
          timeOfEvent: data.timeOfEvent || '12:00',
          location: data.location || '',
          description: data.description || '',
          oshaSubmittedAt: new Date().toISOString(),
        };

        console.log('Submitting complete OSHA data for safety event:', completeFormData);

        // First, create a standalone OSHA form record for this safety event
        const oshaFormResponse = (await apiRequest('POST', '/api/osha-forms', {
          formData: completeFormData,
          standaloneEntry: false,
          status: 'Submitted',
          submittedBy: 'Alex Johnson',
          submittedAt: new Date(),
        })) as { id?: number | string }; // Type assertion to fix TypeScript error

        console.log('Created OSHA form record:', oshaFormResponse);

        // Then update the safety event with the OSHA submission status and reference
        await apiRequest('PATCH', `/api/incidents/${incidentId}/osha-submission`, {
          oshaFormData: JSON.stringify(completeFormData), // Store as JSON string
          oshaSubmitted: true,
          username: 'Alex Johnson', // In a real app, this would be the logged-in user
          submittedBy: 'Alex Johnson',
          submittedAt: new Date(),
          oshaFormId: oshaFormResponse?.id || null,
        });

        setIsSubmitted(true);

        toast({
          title: 'OSHA Form 301 Submitted',
          description: 'The OSHA log has been recorded successfully.',
          duration: 5000,
        });

        // Invalidate OSHA logs data
        queryClient.invalidateQueries({ queryKey: ['/api/osha-logs'] });
        queryClient.invalidateQueries({ queryKey: ['/api/osha-summary'] });

        // Return to incident details
        setLocation(`/incidents/${incidentId}`);
      } else {
        // Create standalone OSHA entry - ensure we include all form fields
        const completeFormData = {
          ...data,
          // Ensure key fields are present with defaults if needed
          linkedIncidentId: data.linkedIncidentId || null,
          privacyCase: data.privacyCase || false,
          bodyPartAffected: data.bodyPartAffected || '',
          injuryType: data.injuryType || '',
          oshaSubmittedAt: new Date().toISOString(),
        };

        console.log('Submitting complete form data:', completeFormData);

        const response = (await apiRequest('POST', '/api/osha-forms', {
          formData: completeFormData,
          standaloneEntry: true,
          submittedBy: 'Alex Johnson', // In a real app, this would be the logged-in user
          submittedAt: new Date(),
        })) as { id?: number | string }; // Type assertion to fix TypeScript error

        setIsSubmitted(true);

        toast({
          title: 'OSHA Form 301 Submitted',
          description: 'The OSHA log has been recorded successfully.',
          duration: 5000,
        });

        // Invalidate OSHA logs data
        queryClient.invalidateQueries({ queryKey: ['/api/osha-logs'] });
        queryClient.invalidateQueries({ queryKey: ['/api/osha-summary'] });

        // For standalone forms, redirect to the proper view page
        setTimeout(() => {
          try {
            // Get the created form data to find the logId
            if (response && typeof response === 'object' && 'id' in response) {
              apiRequest('GET', `/api/osha-forms/${response.id}`)
                .then((createdForm: any) => {
                  if (createdForm?.logId) {
                    setLocation(`/osha/reports/${createdForm.logId}`);
                  } else {
                    setLocation('/osha-logs');
                  }
                })
                .catch(() => {
                  setLocation('/osha-logs');
                });
            } else {
              setLocation('/osha-logs');
            }
          } catch (err) {
            console.error('Error processing response for navigation:', err);
            setLocation('/osha-logs');
          }
        }, 1000);
      }
    } catch (error) {
      console.error('Error submitting OSHA form:', error);
      toast({
        title: 'Submission Error',
        description: 'There was a problem submitting the OSHA form. Please try again.',
        variant: 'destructive',
        duration: 5000,
      });
    }
  }

  function handleBackToIncident() {
    if (incidentId) {
      setLocation(`/incidents/${incidentId}`);
    } else {
      setLocation('/osha-logs');
    }
  }

  function handleExportPdf() {
    // Create printable div
    const printDiv = document.createElement('div');
    printDiv.style.width = '8.5in';
    printDiv.style.margin = '0 auto';
    printDiv.style.padding = '0.5in';
    printDiv.style.fontFamily = 'system-ui, sans-serif';

    // Header
    const header = document.createElement('div');
    header.style.marginBottom = '20px';
    header.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
        <div>
          <h1 style="font-size: 24px; font-weight: bold; margin: 0 0 5px;">OSHA Form 301: Injury and Illness Incident Report</h1>
          ${
            incidentId
              ? `<p style="margin: 0; color: #666;">Linked to Incident #${incidentId}</p>`
              : '<p style="margin: 0; color: #666;">Standalone OSHA Report</p>'
          }
        </div>
        <div style="text-align: right;">
          <p style="margin: 0; font-weight: bold;">UpKeep EHS</p>
          <p style="margin: 0;">OSHA Recordkeeping System</p>
          <p style="margin: 0;">${format(new Date(), 'MM/dd/yyyy')}</p>
        </div>
      </div>
    `;
    printDiv.appendChild(header);

    // Employee information section
    const employeeSection = document.createElement('div');
    employeeSection.style.marginBottom = '20px';
    employeeSection.innerHTML = `
      <h2 style="font-size: 18px; border-bottom: 1px solid #e2e8f0; padding-bottom: 5px; margin-bottom: 10px;">2. Employee Information</h2>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Employee Name:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${
              form.getValues().privacyCase
                ? 'Privacy Case — name will not be displayed in OSHA logs'
                : form.getValues().employeeName || 'Not provided'
            }
          </p>
        </div>
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Job Title:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().jobTitle || 'Not provided'}
          </p>
        </div>
      </div>
      <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Department:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().department || 'N/A'}
          </p>
        </div>
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Date of Hire:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().dateOfHire ? format(new Date(form.getValues().dateOfHire!), 'MMMM d, yyyy') : 'N/A'}
          </p>
        </div>
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Shift:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().shift || 'N/A'}
          </p>
        </div>
      </div>
      <div style="margin-bottom: 15px;">
        <p style="font-weight: bold; margin: 0 0 5px;">Supervisor:</p>
        <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
          ${form.getValues().supervisor || 'N/A'}
        </p>
      </div>
    `;
    printDiv.appendChild(employeeSection);

    // Incident details section
    const incidentSection = document.createElement('div');
    incidentSection.style.marginBottom = '20px';
    incidentSection.innerHTML = `
      <h2 style="font-size: 18px; border-bottom: 1px solid #e2e8f0; padding-bottom: 5px; margin-bottom: 10px;">3. Safety Event Details</h2>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Date of Event:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().dateOfInjury ? format(new Date(form.getValues().dateOfInjury), 'MMMM d, yyyy') : 'Not provided'}
          </p>
        </div>
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Time of Event:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().timeOfEvent ? format(new Date(`2000-01-01T${form.getValues().timeOfEvent}`), 'h:mm a') : 'Not provided'}
          </p>
        </div>
      </div>
      <div style="margin-bottom: 15px;">
        <p style="font-weight: bold; margin: 0 0 5px;">Location:</p>
        <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
          ${form.getValues().location || 'Not provided'}
        </p>
      </div>
      <div style="margin-bottom: 15px;">
        <p style="font-weight: bold; margin: 0 0 5px;">Description of Incident:</p>
        <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0; min-height: 50px;">
          ${form.getValues().description || 'Not provided'}
        </p>
      </div>
    `;
    printDiv.appendChild(incidentSection);

    // Injury details section
    const injurySection = document.createElement('div');
    injurySection.style.marginBottom = '20px';
    injurySection.innerHTML = `
      <h2 style="font-size: 18px; border-bottom: 1px solid #e2e8f0; padding-bottom: 5px; margin-bottom: 10px;">3. Nature of Injury</h2>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Body Part Affected:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().bodyPartAffected || 'Not provided'}
          </p>
        </div>
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Type of Injury:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().injuryType || 'Not provided'}
          </p>
        </div>
      </div>
      <div style="margin-bottom: 15px;">
        <p style="font-weight: bold; margin: 0 0 5px;">Treatment Location:</p>
        <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
          ${form.getValues().treatmentLocation || 'Not provided'}
        </p>
      </div>
    `;
    printDiv.appendChild(injurySection);

    // OSHA questions section
    const oshaSection = document.createElement('div');
    oshaSection.style.marginBottom = '20px';
    oshaSection.innerHTML = `
      <h2 style="font-size: 18px; border-bottom: 1px solid #e2e8f0; padding-bottom: 5px; margin-bottom: 10px;">4. OSHA Questions</h2>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Required Hospitalization:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().wasHospitalized ? 'Yes' : 'No'}
          </p>
        </div>
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Resulted in Death:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${form.getValues().resultedInDeath ? 'Yes' : 'No'}
          </p>
        </div>
      </div>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Days Away From Work:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${
              form.getValues().daysAwayFromWork && form.getValues().daysAwayFromWork !== ''
                ? `${form.getValues().daysAwayFromWork} ${parseInt(form.getValues().daysAwayFromWork || '0') !== 1 ? 'days' : 'day'}`
                : '0 days'
            }
          </p>
        </div>
        <div>
          <p style="font-weight: bold; margin: 0 0 5px;">Restricted Work Days:</p>
          <p style="margin: 0; padding: 5px; background-color: #f7fafc; border: 1px solid #e2e8f0;">
            ${
              form.getValues().daysOfRestrictedDuty && form.getValues().daysOfRestrictedDuty !== ''
                ? `${form.getValues().daysOfRestrictedDuty} ${parseInt(form.getValues().daysOfRestrictedDuty || '0') !== 1 ? 'days' : 'day'}`
                : '0 days'
            }
          </p>
        </div>
      </div>
    `;
    printDiv.appendChild(oshaSection);

    // Footer with certification
    const footer = document.createElement('div');
    footer.style.marginTop = '40px';
    footer.style.borderTop = '1px solid #e2e8f0';
    footer.style.paddingTop = '15px';
    footer.innerHTML = `
      <div style="font-size: 12px; color: #4a5568;">
        <p style="margin: 0 0 10px;">This report was completed in accordance with OSHA recordkeeping requirements.</p>
        <p style="margin: 0;">Generated on ${format(new Date(), 'MMMM d, yyyy, h:mm a')} by UpKeep EHS System</p>
      </div>
    `;
    printDiv.appendChild(footer);

    // Add to document, print, then remove
    document.body.appendChild(printDiv);

    const originalContents = document.body.innerHTML;
    document.body.innerHTML = printDiv.innerHTML;

    setTimeout(() => {
      window.print();
      document.body.innerHTML = originalContents;
      toast({
        title: 'PDF Export',
        description: 'Your OSHA Form 301 has been sent to the printer or saved as PDF.',
        duration: 3000,
      });
    }, 500);
  }

  // Special view-only mode for submitted forms
  if (isViewMode && isSubmitted) {
    return (
      <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
        <div className="mb-8">
          <Button variant="ghost" onClick={handleBackToIncident} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {incidentId ? 'Back to Incident' : '← Back to OSHA Logs'}
          </Button>

          {/* Header section with title, status, and action buttons */}
          <div className="bg-white border rounded-xl p-6 mb-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
              <div>
                <h1 className="text-2xl font-bold mb-1">OSHA Form 301: Injury and Illness Incident Report</h1>
                <div className="flex flex-wrap items-center gap-2">
                  <Badge className="bg-blue-50 text-blue-700 border-blue-200">Log ID: {getOshaLogId()}</Badge>
                  {isSubmitted ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <CheckCircle className="h-3.5 w-3.5 mr-1" />
                      Submitted
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                      <Clock className="h-3.5 w-3.5 mr-1" />
                      Draft
                    </Badge>
                  )}
                  {incidentId && (
                    <span className="text-sm text-muted-foreground">Linked to Incident #{incidentId}</span>
                  )}
                  {!incidentId && <span className="text-sm text-muted-foreground">Standalone OSHA Report</span>}
                </div>
              </div>

              {isSubmitted && (
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (incidentId) {
                        setLocation(`/osha-form-301?incidentId=${incidentId}&mode=edit`);
                      } else {
                        setLocation('/osha-form-301?mode=edit');
                      }
                    }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>

                  <Button variant="outline" size="sm" onClick={handleExportPdf}>
                    <FileDown className="h-4 w-4 mr-2" />
                    Export PDF
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Main content cards */}
          <div className="space-y-6">
            {/* Employee Information card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gray-50 border-b py-4">
                <CardTitle className="text-lg font-semibold flex items-center">
                  <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                    1
                  </span>
                  Employee Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Employee Name</h3>
                    <p className="text-base text-gray-800">
                      {form.getValues().privacyCase
                        ? 'Privacy Case — name will not be displayed in OSHA logs'
                        : form.getValues().employeeName || 'Not provided'}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Job Title</h3>
                    <p className="text-base text-gray-800">{form.getValues().jobTitle || 'Not provided'}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Department</h3>
                    <p className="text-base text-gray-800">{form.getValues().department || 'N/A'}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Date of Hire</h3>
                    <p className="text-base text-gray-800">
                      {form.getValues().dateOfHire
                        ? format(new Date(form.getValues().dateOfHire || '2020-01-01'), 'MMM d, yyyy')
                        : 'N/A'}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Shift</h3>
                    <p className="text-base text-gray-800">{form.getValues().shift || 'N/A'}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Supervisor</h3>
                    <p className="text-base text-gray-800">{form.getValues().supervisor || 'N/A'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Incident Details card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gray-50 border-b py-4">
                <CardTitle className="text-lg font-semibold flex items-center">
                  <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                    2
                  </span>
                  Incident Details
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Date of Injury
                    </h3>
                    <p className="text-base text-gray-800">
                      {form.getValues().dateOfInjury
                        ? format(new Date(form.getValues().dateOfInjury), 'MMMM d, yyyy')
                        : 'Not provided'}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      Time of Event
                    </h3>
                    <p className="text-base text-gray-800">
                      {form.getValues().timeOfEvent
                        ? format(new Date(`2000-01-01T${form.getValues().timeOfEvent}`), 'h:mm a')
                        : 'Not provided'}
                    </p>
                  </div>
                </div>

                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-500 mb-1 flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    Location
                  </h3>
                  <p className="text-base text-gray-800">{form.getValues().location || 'Not provided'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Description</h3>
                  <div className="text-base text-gray-800 whitespace-pre-wrap">
                    {form.getValues().description || 'Not provided'}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Nature of Injury card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gray-50 border-b py-4">
                <CardTitle className="text-lg font-semibold flex items-center">
                  <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                    3
                  </span>
                  Nature of Injury
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Body Part Affected</h3>
                    <p className="text-base text-gray-800">{form.getValues().bodyPartAffected || 'Not provided'}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Type of Injury</h3>
                    <p className="text-base text-gray-800">{form.getValues().injuryType || 'Not provided'}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Treatment Location</h3>
                  <p className="text-base text-gray-800">{form.getValues().treatmentLocation || 'Not provided'}</p>
                </div>
              </CardContent>
            </Card>

            {/* OSHA Questions card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gray-50 border-b py-4">
                <CardTitle className="text-lg font-semibold flex items-center">
                  <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                    4
                  </span>
                  OSHA Questions
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  <div className="flex items-start gap-3">
                    <div
                      className={`mt-0.5 rounded-full w-5 h-5 flex items-center justify-center ${form.getValues().wasHospitalized ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}
                    >
                      {form.getValues().wasHospitalized ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Employee was hospitalized?</h3>
                      <p className="text-base text-gray-800">{form.getValues().wasHospitalized ? 'Yes' : 'No'}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div
                      className={`mt-0.5 rounded-full w-5 h-5 flex items-center justify-center ${form.getValues().resultedInDeath ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'}`}
                    >
                      {form.getValues().resultedInDeath ? <Check className="h-3 w-3" /> : <X className="h-3 w-3" />}
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Incident resulted in death?</h3>
                      <p className="text-base text-gray-800">{form.getValues().resultedInDeath ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Days Away From Work</h3>
                    <p className="text-base text-gray-800">
                      {form.getValues().daysAwayFromWork || '0'}{' '}
                      {parseInt(form.getValues().daysAwayFromWork || '0') === 1 ? 'day' : 'days'}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Days of Restricted Work Activity</h3>
                    <p className="text-base text-gray-800">
                      {form.getValues().daysOfRestrictedDuty || '0'}{' '}
                      {parseInt(form.getValues().daysOfRestrictedDuty || '0') === 1 ? 'day' : 'days'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Footer with metadata */}
            <div className="mt-8 border-t pt-6">
              <div className="flex flex-wrap justify-between text-sm text-muted-foreground">
                <div>
                  <p>Submitted by: {form.getValues().oshaSubmittedBy || 'Alex Johnson'}</p>
                  <p>Date submitted: {format(new Date(), 'MMMM d, yyyy')}</p>
                </div>
                <Button variant="outline" size="sm" onClick={handleExportPdf}>
                  <FileDown className="h-4 w-4 mr-2" />
                  Export as PDF
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading && incidentId) {
    return (
      <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
        <div className="mb-8">
          <Button variant="ghost" onClick={handleBackToIncident} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Loading OSHA Form 301...</h1>
        </div>
      </div>
    );
  }

  if (error && incidentId) {
    return (
      <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
        <div className="mb-8">
          <Button variant="ghost" onClick={handleBackToIncident} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-bold text-red-600">Error Loading OSHA Form</h1>
          <p className="mt-2">Unable to load incident data. Please try again.</p>
        </div>
      </div>
    );
  }

  // Note: We're using the getOshaLogId function that's already defined above

  return (
    <div className="container mx-auto max-w-7xl py-10 px-4 sm:px-6">
      <div className="mb-8">
        {/* Back navigation */}
        <Button variant="ghost" onClick={handleBackToIncident} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {incidentId ? 'Back to Incident' : 'Back to OSHA Logs'}
        </Button>

        {/* Header with title and action buttons */}
        <div className="flex flex-col mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start mb-2">
            <h1 className="text-2xl font-bold">OSHA Form 301: Injury and Illness Incident Report</h1>

            {/* Action buttons in right-aligned group */}
            {isSubmitted && (
              <div className="flex mt-2 sm:mt-0 space-x-2">
                {/* Print button */}
                <Button variant="outline" size="sm" onClick={() => window.print()}>
                  <FileDown className="h-4 w-4 mr-2" />
                  Print
                </Button>

                {/* Export PDF button */}
                <Button variant="outline" size="sm" onClick={handleExportPdf}>
                  <FileDown className="h-4 w-4 mr-2" />
                  Export PDF
                </Button>

                {/* Toggle View/Edit mode button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newMode = isViewMode ? 'edit' : 'view';
                    if (incidentId) {
                      setLocation(`/osha-form-301?incidentId=${incidentId}&mode=${newMode}`);
                    } else {
                      setLocation(`/osha-form-301?mode=${newMode}`);
                    }
                  }}
                >
                  {isViewMode ? (
                    <>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>

          {/* Status information area */}
          <div className="flex flex-wrap items-center gap-2 mt-1">
            {incidentId && <span className="text-sm text-muted-foreground">Linked to Safety Event #{incidentId}</span>}

            {isSubmitted && (
              <Badge className="bg-blue-50 text-blue-700 border-blue-200">Log ID: {getOshaLogId()}</Badge>
            )}

            {isViewMode && isSubmitted && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="h-3.5 w-3.5 mr-1" />
                Submitted
              </Badge>
            )}

            {!isViewMode && (
              <>
                {completionStatus === 'not-started' && (
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                    🔴 Not Started
                  </Badge>
                )}
                {completionStatus === 'in-progress' && (
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                    🟡 In Progress
                  </Badge>
                )}
                {completionStatus === 'complete' && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    🟢 Complete
                  </Badge>
                )}
              </>
            )}
          </div>

          {/* Progress bar for edit mode */}
          {!isViewMode && (
            <div className="mt-4">
              <div className="flex items-center gap-3">
                <div className="flex-1">
                  <Progress value={progress} className="h-2" />
                </div>
                <span className="text-sm font-medium">{progress}%</span>
              </div>
            </div>
          )}
        </div>
      </div>

      <Form {...form}>
        {/* Section 1: Linked Safety Event */}
        {!isViewMode && (
          <div className="mb-6 border border-gray-200 rounded-lg p-4 bg-white">
            <div className="flex items-center mb-4">
              <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                1
              </span>
              <h2 className="text-lg font-semibold">
                Linked Safety Event <span className="text-red-500">*</span>
              </h2>
            </div>
            <p className="text-sm text-muted-foreground mb-4">This is a mandatory field.</p>
            <FormField
              control={form.control}
              name="linkedIncidentId"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-base font-medium">Select Safety Event</FormLabel>
                  <div className="flex items-center gap-2">
                    {field.value && field.value > 0 ? (
                      // Display the linked safety event details with comprehensive information
                      <div className="w-full">
                        {(() => {
                          const linkedIncident = allSafetyEvents?.find((inc: SafetyEvent) => inc.id === field.value);
                          return linkedIncident ? (
                            <div className="border rounded-lg bg-blue-50 border-blue-200">
                              {/* Header with ID, Title and Unlink Button */}
                              <div className="flex items-center justify-between p-4 border-b border-blue-200 bg-blue-100">
                                <div className="flex items-center gap-2">
                                  <LinkIconSolid className="h-5 w-5 text-blue-600" />
                                  <div>
                                    <div className="font-semibold text-blue-900">
                                      SE-{String(field.value).padStart(4, '0')} - {linkedIncident.title}
                                    </div>
                                    <div className="text-sm text-blue-700">Linked Safety Event (read-only data)</div>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-blue-200"
                                  onClick={() => form.setValue('linkedIncidentId', 0)}
                                >
                                  <X className="h-4 w-4" />
                                  <span className="sr-only">Unlink safety event</span>
                                </Button>
                              </div>

                              {/* Comprehensive Safety Event Details */}
                              <div className="p-4 space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                  {/* Date & Time */}
                                  <div className="space-y-1">
                                    <label className="text-sm font-medium text-blue-800 flex items-center gap-1">
                                      <Calendar className="h-4 w-4" />
                                      Date & Time
                                    </label>
                                    <div className="p-2 bg-white border border-blue-200 rounded text-sm">
                                      {linkedIncident.dateTime
                                        ? format(new Date(linkedIncident.dateTime), "MMM d, yyyy 'at' h:mm a")
                                        : 'Not provided'}
                                    </div>
                                  </div>

                                  {/* Status */}
                                  <div className="space-y-1">
                                    <label className="text-sm font-medium text-blue-800 flex items-center gap-1">
                                      <Activity className="h-4 w-4" />
                                      Status
                                    </label>
                                    <div className="p-2 bg-white border border-blue-200 rounded text-sm">
                                      <Badge
                                        variant={
                                          linkedIncident.status === 'Closed'
                                            ? 'default'
                                            : linkedIncident.status === 'In Progress'
                                              ? 'secondary'
                                              : 'outline'
                                        }
                                        className="text-xs"
                                      >
                                        {linkedIncident.status || 'Open'}
                                      </Badge>
                                    </div>
                                  </div>

                                  {/* Report Type */}
                                  <div className="space-y-1">
                                    <label className="text-sm font-medium text-blue-800 flex items-center gap-1">
                                      <AlertTriangle className="h-4 w-4" />
                                      Report Type
                                    </label>
                                    <div className="p-2 bg-white border border-blue-200 rounded text-sm">
                                      <Badge
                                        variant={
                                          linkedIncident.reportType === 'injury'
                                            ? 'destructive'
                                            : linkedIncident.reportType === 'near_miss'
                                              ? 'secondary'
                                              : 'outline'
                                        }
                                        className="text-xs"
                                      >
                                        {linkedIncident.reportType || 'Not specified'}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>

                                {/* Location */}
                                <div className="space-y-1">
                                  <label className="text-sm font-medium text-blue-800 flex items-center gap-1">
                                    <MapPin className="h-4 w-4" />
                                    Location
                                  </label>
                                  <div className="p-2 bg-white border border-blue-200 rounded text-sm">
                                    {linkedIncident.location || 'Not provided'}
                                  </div>
                                </div>

                                {/* Equipment or Asset Involved */}
                                {linkedIncident.equipmentInvolved && (
                                  <div className="space-y-1">
                                    <label className="text-sm font-medium text-blue-800 flex items-center gap-1">
                                      <Settings className="h-4 w-4" />
                                      Equipment or Asset Involved
                                    </label>
                                    <div className="p-2 bg-white border border-blue-200 rounded text-sm">
                                      {linkedIncident.equipmentInvolved || 'Not specified'}
                                    </div>
                                  </div>
                                )}

                                {/* Description */}
                                <div className="space-y-1">
                                  <label className="text-sm font-medium text-blue-800 flex items-center gap-1">
                                    <FileText className="h-4 w-4" />
                                    Description
                                  </label>
                                  <div className="p-3 bg-white border border-blue-200 rounded text-sm min-h-[80px] whitespace-pre-wrap">
                                    {linkedIncident.description || 'No description available from linked safety event'}
                                  </div>
                                </div>

                                {/* Action Button to View Full Safety Event */}
                                <div className="pt-2 border-t border-blue-200">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-blue-700 border-blue-300 hover:bg-blue-100"
                                    onClick={() => setLocation(`/incidents/${field.value}`)}
                                  >
                                    <ExternalLink className="h-4 w-4 mr-2" />
                                    View Full Safety Event
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            // Fallback for when incident data is not loaded
                            <div className="flex items-center gap-2 p-3 border rounded-md bg-blue-50 text-blue-800 border-blue-200 w-full">
                              <LinkIconSolid className="h-4 w-4 text-blue-500 mr-1" />
                              <span className="font-medium">SE-{String(field.value).padStart(4, '0')}</span>
                              <span className="text-sm text-blue-600">(Loading details...)</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="ml-auto h-7 w-7 p-0"
                                onClick={() => form.setValue('linkedIncidentId', 0)}
                              >
                                <X className="h-4 w-4" />
                                <span className="sr-only">Unlink safety event</span>
                              </Button>
                            </div>
                          );
                        })()}
                      </div>
                    ) : (
                      // Safety event selector dropdown
                      <Popover open={safetyEventSearchOpen} onOpenChange={setSafetyEventSearchOpen}>
                        <PopoverTrigger asChild>
                          <Button variant="outline" role="combobox" className="w-full justify-between bg-white">
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <LinkIcon className="h-4 w-4" />
                              <span>Search for a safety event to link...</span>
                            </div>
                            <X
                              className={`h-4 w-4 opacity-70 ${safetyEventSearchValue ? 'block' : 'hidden'}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                setSafetyEventSearchValue('');
                              }}
                            />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-96 p-0">
                          <Command>
                            <CommandInput
                              placeholder="Search safety events..."
                              value={safetyEventSearchValue}
                              onValueChange={setSafetyEventSearchValue}
                              className="h-9"
                            />
                            <CommandList>
                              <CommandEmpty>No safety events found.</CommandEmpty>
                              {isLoadingSafetyEvents ? (
                                <div className="p-4 text-center text-sm text-muted-foreground">
                                  Loading safety events...
                                </div>
                              ) : (
                                <CommandGroup>
                                  {allSafetyEvents &&
                                    allSafetyEvents
                                      .filter(
                                        (inc: SafetyEvent) =>
                                          inc.title.toLowerCase().includes(safetyEventSearchValue.toLowerCase()) ||
                                          inc.id.toString().includes(safetyEventSearchValue),
                                      )
                                      .sort(
                                        (a: SafetyEvent, b: SafetyEvent) =>
                                          new Date(b.dateTime).getTime() - new Date(a.dateTime).getTime(),
                                      )
                                      .slice(0, 10)
                                      .map((inc: SafetyEvent) => (
                                        <CommandItem
                                          key={inc.id}
                                          value={inc.id.toString()}
                                          onSelect={() => {
                                            form.setValue('linkedIncidentId', inc.id);
                                            setSafetyEventSearchValue('');
                                            setSafetyEventSearchOpen(false);
                                          }}
                                          className="flex items-center gap-2"
                                        >
                                          <span className="font-medium">SE-{String(inc.id).padStart(4, '0')}</span>
                                          <span className="text-gray-500 mx-1">·</span>
                                          <span className="truncate flex-1">{inc.title}</span>
                                        </CommandItem>
                                      ))}
                                </CommandGroup>
                              )}
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    )}
                  </div>
                  <FormDescription>
                    Associate this OSHA record with an existing safety event for better tracking.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* View Mode Linked Safety Event Display */}
        {isViewMode && form.getValues().linkedIncidentId && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  1
                </span>
                Linked Safety Event
              </CardTitle>
              <CardDescription>Read-only details from the linked safety event</CardDescription>
            </CardHeader>
            <CardContent>
              {(() => {
                const linkedId = form.getValues().linkedIncidentId;
                const linkedIncident = allSafetyEvents?.find((inc: SafetyEvent) => inc.id === linkedId);
                return linkedIncident ? (
                  <div className="space-y-4">
                    {/* Header with ID and Title */}
                    <div className="flex items-center justify-between p-4 rounded-lg bg-blue-50 border border-blue-200">
                      <div className="flex items-center gap-2">
                        <LinkIconSolid className="h-5 w-5 text-blue-600" />
                        <div>
                          <div className="font-semibold text-blue-900">
                            SE-{String(linkedId).padStart(4, '0')} - {linkedIncident.title}
                          </div>
                          <div className="text-sm text-blue-700">Linked Safety Event Details</div>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-blue-700 border-blue-300 hover:bg-blue-100"
                        onClick={() => setLocation(`/incidents/${linkedId}`)}
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Full Event
                      </Button>
                    </div>

                    {/* Safety Event Details Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {/* Date & Time */}
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Date & Time
                        </label>
                        <div className="p-2 bg-gray-50 border rounded text-sm">
                          {linkedIncident.dateTime
                            ? format(new Date(linkedIncident.dateTime), "MMM d, yyyy 'at' h:mm a")
                            : 'Not provided'}
                        </div>
                      </div>

                      {/* Status */}
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                          <Activity className="h-4 w-4" />
                          Status
                        </label>
                        <div className="p-2 bg-gray-50 border rounded text-sm">
                          <Badge
                            variant={
                              linkedIncident.status === 'Closed'
                                ? 'default'
                                : linkedIncident.status === 'In Progress'
                                  ? 'secondary'
                                  : 'outline'
                            }
                            className="text-xs"
                          >
                            {linkedIncident.status || 'Open'}
                          </Badge>
                        </div>
                      </div>

                      {/* Report Type */}
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                          <AlertTriangle className="h-4 w-4" />
                          Report Type
                        </label>
                        <div className="p-2 bg-gray-50 border rounded text-sm">
                          <Badge
                            variant={
                              linkedIncident.reportType === 'injury'
                                ? 'destructive'
                                : linkedIncident.reportType === 'near_miss'
                                  ? 'secondary'
                                  : 'outline'
                            }
                            className="text-xs"
                          >
                            {linkedIncident.reportType || 'Not specified'}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* Location */}
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        Location
                      </label>
                      <div className="p-2 bg-gray-50 border rounded text-sm">
                        {linkedIncident.location || 'Not provided'}
                      </div>
                    </div>

                    {/* Equipment or Asset Involved */}
                    {linkedIncident.equipmentInvolved && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                          <Settings className="h-4 w-4" />
                          Equipment or Asset Involved
                        </label>
                        <div className="p-2 bg-gray-50 border rounded text-sm">{linkedIncident.equipmentInvolved}</div>
                      </div>
                    )}

                    {/* Description */}
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                        <FileText className="h-4 w-4" />
                        Description
                      </label>
                      <div className="p-3 bg-gray-50 border rounded text-sm min-h-[80px] whitespace-pre-wrap">
                        {linkedIncident.description || 'No description available from linked safety event'}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 p-3 rounded-md bg-blue-50 text-blue-800 border border-blue-200">
                    <LinkIconSolid className="h-4 w-4 text-blue-500" />
                    <span className="font-medium">SE-{String(linkedId).padStart(4, '0')}</span>
                    <span className="text-sm text-blue-600">(Details loading...)</span>
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        )}
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Section 2: Employee Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  2
                </span>
                Employee Information
              </CardTitle>
              <CardDescription>Identify the employee involved in the incident</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Privacy Case Toggle */}
              <FormField
                control={form.control}
                name="privacyCase"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border border-blue-300 bg-blue-50 p-4 shadow-sm">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="mr-2 bg-blue-100 text-blue-800 border-blue-300 px-2 py-1">
                        🛡️ PRIVACY
                      </Badge>
                      <span className="text-md font-semibold text-blue-800">Mark as Privacy Case</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-blue-500" />
                          </TooltipTrigger>
                          <TooltipContent className="w-[300px] p-4 bg-white shadow-lg rounded-md border">
                            <div className="space-y-2">
                              <h4 className="font-semibold text-blue-800">OSHA Privacy Case Criteria</h4>
                              <p className="text-sm text-gray-700">
                                Mark this case as private if it involves any of the following:
                              </p>
                              <ul className="text-sm text-gray-700 list-disc pl-4 space-y-1">
                                <li>Injury to intimate body part or reproductive system</li>
                                <li>Sexual assault</li>
                                <li>Mental illness</li>
                                <li>HIV infection, hepatitis, or tuberculosis</li>
                                <li>Needlestick injury from contaminated object</li>
                                <li>Employee requests privacy</li>
                              </ul>
                              <p className="text-sm text-gray-700 pt-1">
                                When enabled, the employee's name will be replaced with "Privacy Case" in OSHA records.
                              </p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isViewMode}
                        className="data-[state=checked]:bg-blue-600"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeName"
                render={({ field }) => {
                  // Check if privacy case is enabled
                  const isPrivacyCase = form.watch('privacyCase');

                  return (
                    <FormItem>
                      <FormLabel>Employee Name</FormLabel>
                      <FormControl>
                        {isViewMode ? (
                          isPrivacyCase ? (
                            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800 flex items-center">
                              <span className="mr-2">🛡️</span>
                              Privacy Case — name will not be displayed in OSHA logs
                            </div>
                          ) : (
                            <div className="text-base text-gray-800">{field.value || 'Not provided'}</div>
                          )
                        ) : isPrivacyCase ? (
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800 flex items-center">
                            <span className="mr-2">🛡️</span>
                            Privacy Case — name will not be displayed in OSHA logs
                          </div>
                        ) : (
                          <Input placeholder="Full name" {...field} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={form.control}
                name="jobTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Title</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                      ) : (
                        <Input placeholder="Job position or title" {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                      ) : (
                        <Input placeholder="Employee department" {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="dateOfHire"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date of Hire</FormLabel>
                      <FormControl>
                        {isViewMode ? (
                          <div className="p-2 bg-gray-50 border rounded-md">
                            {field.value ? format(new Date(field.value), 'MMM d, yyyy') : 'Not specified'}
                          </div>
                        ) : (
                          <Input type="date" {...field} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="shift"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shift</FormLabel>
                      <FormControl>
                        {isViewMode ? (
                          <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not specified'}</div>
                        ) : (
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select shift" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Day">Day</SelectItem>
                              <SelectItem value="Evening">Evening</SelectItem>
                              <SelectItem value="Night">Night</SelectItem>
                              <SelectItem value="Rotating">Rotating</SelectItem>
                              <SelectItem value="Other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="supervisor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supervisor</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                      ) : (
                        <Input placeholder="Employee's supervisor name" {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 3: Medical Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  3
                </span>
                Medical Information
              </CardTitle>
              <CardDescription>Details about the injury or illness and incident context</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="bodyPartAffected"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Body Part Affected
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Specify the part of body that was affected</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                      ) : (
                        <Input placeholder="e.g., Hand, back, eye, etc." {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="injuryType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Type of Injury
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Specify the type of injury sustained</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                      ) : (
                        <Input placeholder="e.g., Cut, burn, fracture, etc." {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="treatmentLocation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Treatment Location</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                      ) : (
                        <Input placeholder="e.g., Onsite first aid, hospital, clinic, etc." {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="typeOfCare"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type of Medical Care</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                      ) : (
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="First Aid" />
                            </FormControl>
                            <FormLabel className="font-normal">First Aid Only</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="Medical Treatment" />
                            </FormControl>
                            <FormLabel className="font-normal">Medical Treatment</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="Emergency Room" />
                            </FormControl>
                            <FormLabel className="font-normal">Emergency Room</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="Hospitalized" />
                            </FormControl>
                            <FormLabel className="font-normal">Overnight Hospital Stay</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="prescriptionGiven"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isViewMode} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Prescription Medication Given</FormLabel>
                      <FormDescription>
                        Check if prescription medication was provided as part of treatment
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {/* Relocated fields from Safety Event Details */}
              <Separator className="my-6" />
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <h4 className="text-sm font-medium text-gray-700">Incident Context Details</h4>
                </div>

                <FormField
                  control={form.control}
                  name="specificTask"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        Specific Task Being Performed
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span>
                                <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-xs">
                              <p>
                                Describe what specific task or activity the employee was performing when the incident
                                occurred
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </FormLabel>
                      <FormControl>
                        {isViewMode ? (
                          <div className="p-3 bg-gray-50 border rounded-md min-h-[60px] whitespace-pre-wrap">
                            {field.value || 'Not provided'}
                          </div>
                        ) : (
                          <Textarea
                            placeholder="Describe the specific task, operation, or activity being performed when the incident occurred"
                            className="min-h-[60px]"
                            {...field}
                          />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="objectCaused"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        Object/Substance That Directly Injured
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span>
                                <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-xs">
                              <p>
                                Identify the specific object, tool, substance, or material that directly caused the
                                injury
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </FormLabel>
                      <FormControl>
                        {isViewMode ? (
                          <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                        ) : (
                          <Input placeholder="e.g., Knife, chemical, machinery part, hot surface, etc." {...field} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Section 4: OSHA Questions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  4
                </span>
                OSHA Questions
              </CardTitle>
              <CardDescription>Additional information required for OSHA recordkeeping</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="wasHospitalized"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        {isViewMode ? (
                          <div
                            className={`flex-shrink-0 mt-0.5 rounded-full w-4 h-4 ${field.value ? 'bg-green-500' : 'bg-red-500'}`}
                          />
                        ) : (
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        )}
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Employee was hospitalized as an in-patient</FormLabel>
                        <FormDescription>Check if overnight admission to a hospital was required</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="resultedInDeath"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        {isViewMode ? (
                          <div
                            className={`flex-shrink-0 mt-0.5 rounded-full w-4 h-4 ${field.value ? 'bg-green-500' : 'bg-red-500'}`}
                          />
                        ) : (
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        )}
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Incident resulted in death</FormLabel>
                        <FormDescription>Check if the incident resulted in a fatality</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="daysAwayFromWork"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Days Away From Work</FormLabel>
                      <FormDescription>Number of days the employee was unable to work</FormDescription>
                      <FormControl>
                        {isViewMode ? (
                          <div className="p-2 bg-gray-50 border rounded-md">
                            {field.value ? `${field.value} ${parseInt(field.value) !== 1 ? 'days' : 'day'}` : '0 days'}
                          </div>
                        ) : (
                          <Input type="number" min="0" placeholder="0" {...field} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="daysOfRestrictedDuty"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Days of Restricted Work Activity</FormLabel>
                      <FormDescription>Days employee was on restricted duty or job transfer</FormDescription>
                      <FormControl>
                        {isViewMode ? (
                          <div className="p-2 bg-gray-50 border rounded-md">
                            {field.value ? `${field.value} ${parseInt(field.value) !== 1 ? 'days' : 'day'}` : '0 days'}
                          </div>
                        ) : (
                          <Input type="number" min="0" placeholder="0" {...field} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Section 5: Witnesses & People */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  5
                </span>
                Witnesses & People
              </CardTitle>
              <CardDescription>People involved in or who witnessed the incident</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="witnesses"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Witnesses</FormLabel>
                    <FormDescription>Names of people who witnessed the incident (comma-separated list)</FormDescription>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md min-h-[60px]">
                          {field.value && field.value.length > 0 ? field.value.join(', ') : 'No witnesses listed'}
                        </div>
                      ) : (
                        <Textarea
                          placeholder="Enter witness names separated by commas"
                          className="min-h-[60px]"
                          onChange={(e) => {
                            const namesArray = e.target.value
                              .split(',')
                              .map((name) => name.trim())
                              .filter(Boolean);
                            field.onChange(namesArray);
                          }}
                          value={field.value ? field.value.join(', ') : ''}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="reportedBy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reported By</FormLabel>
                      <FormControl>
                        {isViewMode ? (
                          <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                        ) : (
                          <Input placeholder="Person who reported the incident" {...field} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="preparedBy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Prepared By</FormLabel>
                      <FormControl>
                        {isViewMode ? (
                          <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not provided'}</div>
                        ) : (
                          <Input placeholder="Person who prepared this report" {...field} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="preparedDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date Prepared</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">
                          {field.value
                            ? format(new Date(field.value), 'MMMM d, yyyy')
                            : format(new Date(), 'MMMM d, yyyy')}
                        </div>
                      ) : (
                        <Input type="date" {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 6: Corrective Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  6
                </span>
                Corrective Actions
              </CardTitle>
              <CardDescription>Actions taken or planned to prevent recurrence</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="rootCause"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Root Cause Analysis</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md min-h-[80px] whitespace-pre-wrap">
                          {field.value || 'Not provided'}
                        </div>
                      ) : (
                        <Textarea
                          placeholder="Describe the root cause(s) of the incident"
                          className="min-h-[80px]"
                          {...field}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="correctiveActions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Corrective Actions</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md min-h-[80px] whitespace-pre-wrap">
                          {field.value || 'Not provided'}
                        </div>
                      ) : (
                        <Textarea
                          placeholder="Describe actions taken or planned to prevent recurrence"
                          className="min-h-[80px]"
                          {...field}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 7: OSHA Reporting */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary-500 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  7
                </span>
                OSHA Reporting
              </CardTitle>
              <CardDescription>OSHA reporting requirements and classification</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Primary Recordable Outcome - Mandatory Dropdown */}
              <FormField
                control={form.control}
                name="primaryRecordableOutcome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Primary Recordable Outcome
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md">{field.value || 'Not specified'}</div>
                      ) : (
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select primary recordable outcome" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Fatality">Fatality</SelectItem>
                            <SelectItem value="Days Away From Work">Days Away From Work</SelectItem>
                            <SelectItem value="Job Transfer or Restriction">Job Transfer or Restriction</SelectItem>
                            <SelectItem value="Medical Treatment Beyond First Aid">
                              Medical Treatment Beyond First Aid
                            </SelectItem>
                            <SelectItem value="Loss of Consciousness">Loss of Consciousness</SelectItem>
                            <SelectItem value="Significant Injury/Illness Diagnosed by Healthcare Professional">
                              Significant Injury/Illness Diagnosed by Healthcare Professional
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </FormControl>
                    <FormDescription>Select the primary outcome that makes this case OSHA recordable</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Reason for Reportability - Optional Text Area */}
              <FormField
                control={form.control}
                name="reportableReason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason for Reportability</FormLabel>
                    <FormControl>
                      {isViewMode ? (
                        <div className="p-2 bg-gray-50 border rounded-md min-h-[80px]">
                          {field.value || 'No additional details provided'}
                        </div>
                      ) : (
                        <Textarea
                          {...field}
                          placeholder="Optional: Provide additional explanation if the dropdown selection doesn't fully cover the situation..."
                          className="min-h-[80px] resize-none"
                        />
                      )}
                    </FormControl>
                    <FormDescription>Optional field for additional context or explanation</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form action buttons */}
          <div className="flex justify-between flex-wrap gap-3">
            {/* Left side buttons */}
            <div className="flex flex-wrap gap-3">
              <Button type="button" variant="outline" onClick={handleBackToIncident}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>

            {/* Right side buttons - save button only in edit mode */}
            {!isViewMode && (
              <Button
                type="submit"
                disabled={progress < 100}
                className="bg-primary-500 hover:bg-primary-700 text-white"
              >
                <Save className="mr-2 h-4 w-4" />
                Save OSHA Form
              </Button>
            )}
          </div>

          {/* OSHA Recordkeeping Requirement - above submit button */}
          {!isViewMode && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-6">
              <div className="flex gap-3">
                <Info className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-medium text-amber-800">OSHA Recordkeeping Requirement</h3>
                  <p className="text-sm text-amber-700 mt-1">
                    This form must be retained for 5 years following the year to which it pertains. Completing this form
                    will automatically update your OSHA 300 Log and 300A Summary.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Footer with metadata (only in view mode) */}
          {isViewMode && isSubmitted && (
            <div className="mt-8 border-t pt-6">
              <div className="flex flex-wrap justify-between text-sm text-muted-foreground">
                <div>
                  <p>Submitted by: {form.getValues().oshaSubmittedBy || 'Alex Johnson'}</p>
                  <p>Date submitted: {format(new Date(), 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <p>OSHA Record ID: {getOshaLogId()}</p>
                </div>
              </div>
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}
