import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import confetti from 'canvas-confetti';
import { Plus, ShieldCheck } from 'lucide-react';
import { motion } from 'motion/react';
import { useEffect } from 'react';
import { useLocation } from 'wouter';

export default function NewIncidentReportSuccess() {
  const [_, navigate] = useLocation();

  const isMobile = useIsMobile();

  const { createdIncidentSlug } = history.state;

  // Trigger confetti animation and haptic feedback
  useEffect(() => {
    // Trigger confetti after a short delay to ensure page is loaded
    const timer = setTimeout(() => {
      triggerConfetti();

      // Trigger haptic feedback on mobile if supported
      if (isMobile && navigator.vibrate) {
        navigator.vibrate([100, 50, 100]);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [isMobile]);

  // Confetti animation function
  const triggerConfetti = () => {
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = {
      startVelocity: 30,
      spread: 360,
      ticks: 60,
      zIndex: 9999,
    };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    const interval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);

      // Colorful confetti like in the original modal
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
        colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316'],
      });

      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
        colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316'],
      });
    }, 250);
  };

  const handleReportAnother = () => {
    navigate(history.state.url);
  };

  // Mobile layout
  if (isMobile) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <motion.div
          className="flex-1 flex flex-col justify-center p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col items-center text-center max-w-sm mx-auto">
            <motion.div
              className="mb-6 flex items-center justify-center h-20 w-20 rounded-full bg-green-100"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 300 }}
            >
              <ShieldCheck className="h-10 w-10 text-green-600" />
            </motion.div>

            <motion.h1
              className="text-2xl font-bold text-gray-900 mb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              Safety Report Submitted Successfully!
            </motion.h1>

            {createdIncidentSlug && (
              <motion.div
                className="mb-6 p-4 bg-white rounded-lg border border-gray-200 w-full"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 }}
              >
                <p className="text-sm text-gray-600 mb-1">Your Report ID:</p>
                <p className="text-lg font-mono font-bold text-gray-900 break-all">{createdIncidentSlug}</p>
              </motion.div>
            )}

            <motion.p
              className="text-sm text-gray-600 leading-relaxed mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              A confirmation email has been sent to the address you provided. Our safety team will review your report
              and take appropriate action.
            </motion.p>
          </div>
        </motion.div>

        <motion.div
          className="p-6 space-y-3 bg-white border-t border-gray-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Button className="w-full py-6 text-base bg-blue-600 hover:bg-blue-700" onClick={handleReportAnother}>
            <Plus className="mr-2 h-5 w-5" />
            Submit Another Report
          </Button>
        </motion.div>
      </div>
    );
  }

  // Desktop layout
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-8">
      <motion.div
        className="bg-white rounded-xl shadow-xl w-full max-w-2xl p-12"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, type: 'spring', stiffness: 300 }}
      >
        <div className="flex flex-col items-center text-center mb-12">
          <motion.div
            className="mb-8 flex items-center justify-center h-24 w-24 rounded-full bg-green-100"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 300 }}
          >
            <ShieldCheck className="h-12 w-12 text-green-600" />
          </motion.div>

          <motion.h1
            className="text-4xl font-bold text-gray-900 mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Safety Report Submitted Successfully!
          </motion.h1>

          {createdIncidentSlug && (
            <motion.div
              className="mb-8 p-6 bg-gray-50 rounded-lg border border-gray-200"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 }}
            >
              <p className="text-sm text-gray-600 mb-2">Your Report ID:</p>
              <p className="text-2xl font-mono font-bold text-gray-900">{createdIncidentSlug}</p>
            </motion.div>
          )}

          <motion.p
            className="text-gray-600 text-lg max-w-2xl leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            A confirmation email has been sent to the address you provided. Our safety team will review your report and
            take appropriate action.
          </motion.p>
        </div>

        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Button className="min-w-[200px] py-3 px-6" onClick={handleReportAnother}>
            <Plus className="mr-2 h-4 w-4" />
            Submit Another Report
          </Button>
        </motion.div>
      </motion.div>
    </div>
  );
}
