import { useIsMobile } from '@/hooks/use-mobile';
import { AlertCircle } from 'lucide-react';

export default function NotFound() {
  const isMobile = useIsMobile();

  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
          <AlertCircle className="h-8 w-8 text-red-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>Page Not Found</h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          The page you're looking for doesn't exist or you don't have permission to access it. This could be due to an
          invalid URL or insufficient access rights for the requested resource.
        </p>
        {isMobile && (
          <p className="text-xs text-gray-400 mt-3">
            If you believe you should have access to this page, please contact your administrator.
          </p>
        )}
      </div>
    </div>
  );
}
