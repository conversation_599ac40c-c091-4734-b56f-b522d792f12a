import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RouterOutputs } from '@shared/router.types';
import { AlertCircle, Calendar, CheckCircle, FileText, XCircle } from 'lucide-react';

export const CasesSummary = ({ summary }: { summary?: RouterOutputs['oshaSummary']['getOshaCasesSummary'] }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center w-full">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl mr-4">
            <AlertCircle className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <div>Cases Summary</div>
            <div className="text-sm font-normal text-gray-600 ">Injury & illness statistics</div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-600">Deaths</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                  <XCircle className="h-4 w-4 text-gray-700" />
                </div>
              </div>
              <p className="text-2xl font-bold text-gray-900 mt-2">{summary?.deaths || 0}</p>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-600">Days Away</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                  <Calendar className="h-4 w-4 text-blue-600" />
                </div>
              </div>
              <p className="text-2xl font-bold text-blue-600 mt-2">{summary?.totalDaysAway || 0}</p>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-600">Restricted Work</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                </div>
              </div>
              <p className="text-2xl font-bold text-blue-600 mt-2">{summary?.totalDaysRestricted || 0}</p>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-600">Other Cases</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                  <FileText className="h-4 w-4 text-gray-700" />
                </div>
              </div>
              <p className="text-2xl font-bold text-gray-900 mt-2">{summary?.otherCases || 0}</p>
            </div>
          </div>

          <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-xl border border-gray-200">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full mr-3">
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                </div>
                <Label className="text-lg font-semibold text-gray-800">Total Cases</Label>
              </div>
              <p className="text-3xl font-bold text-blue-600">{summary?.totalCases || 0}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-5 rounded-xl border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-semibold text-gray-700">TRC Rate</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                </div>
              </div>
              <p className="text-2xl font-bold text-blue-600">{summary?.trcRate || 0}</p>
              <p className="text-xs text-gray-500 mt-1">per 200,000 hours</p>
            </div>
            <div className="bg-white p-5 rounded-xl border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-semibold text-gray-700">DART Rate</Label>
                <div className="flex items-center justify-center w-8 h-8 bg-blue-50 rounded-full">
                  <Calendar className="h-4 w-4 text-blue-600" />
                </div>
              </div>
              <p className="text-2xl font-bold text-blue-600">{summary?.dartRate || 0}</p>
              <p className="text-xs text-gray-500 mt-1">per 200,000 hours</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
