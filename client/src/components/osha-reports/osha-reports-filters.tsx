import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { ChevronDown, Filter, X } from 'lucide-react';
import { OshaReportsFilters } from './osha-reports.types';
import { OshaCaseTypeSchema, OshaStatusSchema } from '@shared/schema.types';
import { Location } from '@shared/schema.types';

export const Filters = ({
  filters,
  setFilters,
  activeFilterCount,
  resetFilters,
  locations,
}: {
  filters: OshaReportsFilters;
  setFilters: React.Dispatch<React.SetStateAction<OshaReportsFilters>>;
  activeFilterCount: number;
  resetFilters: () => void;
  locations: Location[];
}) => {
  return (
    <div className="mb-6 overflow-x-auto pb-2 hidden md:block">
      <div className="flex flex-wrap gap-3 items-center">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>
        {/* Case Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Case Type
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.caseType || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            <DropdownMenuLabel>Filter by Case Type</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {OshaCaseTypeSchema.options.map((type) => (
              <DropdownMenuItem
                key={type}
                className="flex items-center gap-2"
                onSelect={() => setFilters({ ...filters, caseType: type })}
              >
                <div
                  className={`h-4 w-4 border rounded-sm flex items-center justify-center ${
                    (type === filters.caseType && !filters.caseType) || filters.caseType === type
                      ? 'bg-primary border-primary text-primary-foreground'
                      : 'border-primary/20'
                  }`}
                >
                  {((type === filters.caseType && !filters.caseType) || filters.caseType === type) && (
                    <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                      <path
                        d="M8.33334 2.5L3.75 7.08333L1.66667 5"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
                <span>{type}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Status Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Status
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.status.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {OshaStatusSchema.options.map((status) => (
              <DropdownMenuItem
                key={status}
                className="flex items-center gap-2"
                onSelect={() => setFilters({ ...filters, status: status })}
              >
                <div
                  className={`h-4 w-4 border rounded-sm flex items-center justify-center ${
                    (status === 'all' && !filters.status) || filters.status.includes(status)
                      ? 'bg-primary border-primary text-primary-foreground'
                      : 'border-primary/20'
                  }`}
                >
                  {((status === 'all' && !filters.status) || filters.status.includes(status)) && (
                    <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                      <path
                        d="M8.33334 2.5L3.75 7.08333L1.66667 5"
                        stroke="currentColor"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
                <span>{status}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Location Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Location
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.locationId.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            {locations?.map((location) => (
              <DropdownMenuCheckboxItem
                key={location.id}
                className="flex items-center space-x-2"
                onSelect={(e) => {
                  e.preventDefault();
                  setFilters({
                    ...filters,
                    locationId: filters.locationId === location.id ? '' : location.id,
                  });
                }}
                checked={filters.locationId.includes(location.id)}
              >
                <label className="capitalize">{location.name}</label>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Privacy Cases Toggle */}
        <div className="flex items-center gap-2 px-3 py-2">
          <Switch
            id="show-privacy-cases"
            checked={filters.showPrivacyCases}
            onCheckedChange={() => setFilters({ ...filters, showPrivacyCases: !filters.showPrivacyCases })}
            className="data-[state=checked]:bg-primary"
          />
          <Label htmlFor="show-privacy-cases" className="text-sm font-medium cursor-pointer">
            Show Privacy Cases
          </Label>
        </div>

        {/* Include Archived Toggle */}
        <div className="flex items-center gap-2 px-3 py-2">
          <Switch
            id="include-archived"
            checked={filters.includeArchived}
            onCheckedChange={() => setFilters({ ...filters, includeArchived: !filters.includeArchived })}
            className="data-[state=checked]:bg-primary"
          />
          <Label htmlFor="include-archived" className="text-sm font-medium cursor-pointer">
            Include Archived
          </Label>
        </div>

        {/* Clear Filters Button */}
        {(filters.caseType ||
          filters.status.length ||
          filters.locationId.length ||
          !filters.showPrivacyCases ||
          filters.includeArchived) && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              resetFilters();
            }}
            className="h-9 text-gray-600 hover:text-gray-900"
          >
            <X className="h-4 w-4 mr-1" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
