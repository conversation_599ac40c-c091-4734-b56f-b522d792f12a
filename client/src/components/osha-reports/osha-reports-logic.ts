import { RouterOutputs } from '@shared/router.types';

// Handle privacy case display
export const getPrivacyAdjustedValue = (
  log: RouterOutputs['oshaReport']['list']['result'][number],
  field: 'employee' | 'jobTitle' | 'employeeWorkLocation',
  showPrivacyCases: boolean,
): string => {
  if (log.privacyCase && !showPrivacyCases) {
    // Mask data based on field type
    if (field === 'employee') return 'Unknown Employee';
    return 'Restricted';
  }
  return log[field as keyof typeof log] as string;
};
