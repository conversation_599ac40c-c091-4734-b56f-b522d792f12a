import { CaseTypeBadge } from '@/components/composite/case-type-badge';
import { OshaStatusBadge } from '@/components/composite/osha-status-badge';
import { getPrivacyAdjustedValue } from '@/components/osha-reports/osha-reports-logic';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { Calendar, Eye, MapPin, User } from 'lucide-react';
import { useLocation } from 'wouter';

export const OshaReportsMobileView = ({
  oshaReports,
  showPrivacyCases,
}: {
  oshaReports: RouterOutputs['oshaReport']['list']['result'];
  showPrivacyCases: boolean;
}) => {
  const [_, navigate] = useLocation();

  return (
    <div className="space-y-4">
      {oshaReports.map((log) => (
        <Card
          key={log.id}
          className="mb-4 cursor-pointer transition-colors hover:bg-muted/50"
          onClick={() => navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(log.id))}
        >
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <div className="flex items-center">
                <p className="font-medium text-sm mr-2">{log.slug}</p>
              </div>
            </div>

            <h3 className="font-medium mb-2 line-clamp-2">{log.employeeName}</h3>

            <div className="text-sm text-muted-foreground mb-3">
              <div className="flex items-center mb-1">
                <CaseTypeBadge oshaReport={log} />
                <span className="flex items-center ml-2">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatDate(log.preparedAt)}
                </span>
              </div>

              <div className="flex items-center mb-1">
                <User className="h-3 w-3 mr-1" />
                {getPrivacyAdjustedValue(log, 'employee', showPrivacyCases)}
              </div>

              <div className="flex items-center mb-1">
                <MapPin className="h-3 w-3 mr-1" />
                {getPrivacyAdjustedValue(log, 'employeeWorkLocation', showPrivacyCases)}
              </div>
            </div>

            <div className="flex justify-between items-center">
              <OshaStatusBadge oshaReport={log} />
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(log.id));
                }}
              >
                <Eye className="h-4 w-4 mr-1" />
                View
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
