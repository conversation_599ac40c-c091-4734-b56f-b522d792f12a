import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { oshaTypeEnum } from '@shared/schema';
import { OshaReportsFilters, OSHA_TYPE_MAP } from '@shared/schema.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';

export const Filters = ({
  filters,
  setFilters,
  activeFilterCount,
  resetFilters,
}: {
  filters: OshaReportsFilters;
  setFilters: React.Dispatch<React.SetStateAction<OshaReportsFilters>>;
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex flex-wrap gap-3 items-center">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>
        {/* Case Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1">
              Case Type
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.caseType ? 1 : 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[180px]">
            {oshaTypeEnum.enumValues.map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                className="flex items-center gap-2"
                onSelect={() =>
                  setFilters((prev) => {
                    const newValue = prev.caseType === type ? undefined : type;
                    return { ...prev, caseType: newValue };
                  })
                }
                checked={filters.caseType === type}
              >
                <span>{OSHA_TYPE_MAP[type]}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Location Filter */}
        <AsyncLocationsFilter
          selected={filters.locationIds}
          onSelect={(locationIds) => {
            setFilters({ ...filters, locationIds });
          }}
          label="Location"
          placeholder="Search location"
        />

        {/* Archive Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilters((prev) => ({ ...prev, includeArchived: !prev.includeArchived }))}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5 mr-1.5" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
