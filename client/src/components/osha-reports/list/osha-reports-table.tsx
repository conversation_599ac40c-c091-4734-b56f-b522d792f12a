import { CaseTypeBadge } from '@/components/composite/case-type-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { Eye } from 'lucide-react';
import { useLocation } from 'wouter';

export const OshaReportsTable = ({ oshaReports }: { oshaReports: RouterOutputs['oshaReport']['list']['result'] }) => {
  const [_, navigate] = useLocation();

  return (
    <div className="overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">OSHA Log ID</TableHead>
            <TableHead className="w-[100px]">Employee</TableHead>
            <TableHead className="w-[100px]">Job Title</TableHead>
            <TableHead className="w-[110px]">Case Type</TableHead>
            <TableHead className="w-[110px]">Fatality</TableHead>
            <TableHead className="w-[130px]">Date</TableHead>
            <TableHead className="w-[110px]">Location</TableHead>
            <TableHead className="w-[110px]">Days Away</TableHead>
            <TableHead className="w-[110px]">Days Restricted</TableHead>
            <TableHead className="w-[120px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {oshaReports.map((report) => (
            <TableRow
              key={report.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(report.id))}
            >
              <TableCell>
                <div className="font-medium">{report.slug}</div>
              </TableCell>
              <TableCell>
                <div className="text-sm text-muted-foreground truncate">
                  {report.privacyCase ? <span className="text-blue-600">Privacy Case</span> : report.employeeName}
                </div>
              </TableCell>
              <TableCell>
                <div className="text-sm text-muted-foreground truncate">{report.employeeJobTitle}</div>
              </TableCell>
              <TableCell>
                <CaseTypeBadge caseType={report.type} />
              </TableCell>
              <TableCell>
                {report.wasDeceased ? (
                  <Badge className="bg-red-100 text-red-800 border-red-200" variant="outline">
                    Yes
                  </Badge>
                ) : (
                  <Badge className="bg-green-100 text-green-800 border-green-200" variant="outline">
                    No
                  </Badge>
                )}
              </TableCell>
              <TableCell className="text-sm text-gray-600">{formatDate(report.createdAt, true)}</TableCell>
              <TableCell className="text-sm text-gray-600">{report.employeeWorkLocation}</TableCell>
              <TableCell className="text-sm text-gray-900 font-medium">{report.daysAwayFromWork || 0}</TableCell>
              <TableCell className="text-sm text-gray-900 font-medium">{report.daysRestrictedFromWork}</TableCell>

              <TableCell>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(report.id));
                  }}
                  className="h-8 w-8 p-0 hover:bg-blue-50"
                >
                  <Eye className="h-4 w-4 text-blue-600" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
