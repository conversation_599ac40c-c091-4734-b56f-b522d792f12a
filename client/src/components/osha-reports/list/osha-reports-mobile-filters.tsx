import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { oshaTypeEnum } from '@shared/schema';
import { OshaReportsFilters, OSHA_TYPE_MAP } from '@shared/schema.types';
import { Archive, ChevronDown, Filter } from 'lucide-react';

export const MobileFilters = ({
  filters,
  setFilters,
  activeFilterCount,
  resetFilters,
}: {
  filters: OshaReportsFilters;
  setFilters: React.Dispatch<React.SetStateAction<OshaReportsFilters>>;
  activeFilterCount: number;
  resetFilters: () => void;
}) => {
  return (
    <div className="md:hidden">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" className="w-full">
            <Filter className="h-4 w-4" />
            {activeFilterCount > 0 && (
              <span className="text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Filter OSHA Logs</SheetTitle>
            <SheetDescription>Choose filters to narrow down your view</SheetDescription>
          </SheetHeader>
          <div className="space-y-4 p-4">
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Case Type</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {oshaTypeEnum.enumValues.map((caseType) => (
                  <div key={caseType} className="flex items-center space-x-2">
                    <Checkbox
                      id={`case-type-${caseType}-mobile`}
                      checked={filters.caseType === caseType}
                      onCheckedChange={() => {
                        setFilters((prev) => {
                          const newValue = prev.caseType === caseType ? undefined : caseType;
                          return { ...prev, caseType: newValue };
                        });
                      }}
                    />
                    <label htmlFor={`case-type-${caseType}-mobile`} className="text-sm cursor-pointer">
                      {OSHA_TYPE_MAP[caseType]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Location Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Location</Label>
              <AsyncLocationsFilter
                selected={filters.locationIds}
                onSelect={(locationIds) => setFilters({ ...filters, locationIds: locationIds })}
                label=""
                placeholder="Select location"
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Archive Status</Label>
              <Button
                variant={filters.includeArchived ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilters((prev) => ({ ...prev, includeArchived: !prev.includeArchived }))}
                className={`justify-start w-full ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
              >
                <Archive className="h-4 w-4 mr-2" />
                Include Archived
              </Button>
            </div>
          </div>
          <SheetFooter className="pt-4">
            <SheetClose asChild>
              <Button variant="outline" className="w-full" onClick={resetFilters}>
                Clear Filters
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button className="w-full">Apply Filters ({activeFilterCount})</Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};
