import { CaseTypeBadge } from '@/components/composite/case-type-badge';
import { OshaStatusBadge } from '@/components/composite/osha-status-badge';
import { getPrivacyAdjustedValue } from '@/components/osha-reports/osha-reports-logic';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { Eye } from 'lucide-react';
import { useLocation } from 'wouter';

export const OshaReportsTable = ({
  oshaReports,
  showPrivacyCases,
}: {
  oshaReports: RouterOutputs['oshaReport']['list']['result'];
  showPrivacyCases: boolean;
}) => {
  const [_, navigate] = useLocation();

  return (
    <div className="overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-50 border-b-2 border-gray-100">
            <TableHead className="cursor-pointer font-semibold text-gray-700 py-4 px-6 hover:bg-gray-100 transition-colors">
              OSHA Log ID
            </TableHead>
            <TableHead className="font-semibold text-gray-700 py-4 px-4">Employee</TableHead>
            <TableHead className="font-semibold text-gray-700 py-4 px-4">Job Title</TableHead>
            <TableHead className="cursor-pointer font-semibold text-gray-700 py-4 px-4 hover:bg-gray-100 transition-colors">
              Case Type
            </TableHead>
            <TableHead className="text-center font-semibold text-gray-700 py-4 px-4">Fatality</TableHead>
            <TableHead className="cursor-pointer font-semibold text-gray-700 py-4 px-4 hover:bg-gray-100 transition-colors">
              Date
            </TableHead>
            <TableHead className="font-semibold text-gray-700 py-4 px-4">Location</TableHead>
            <TableHead className="cursor-pointer font-semibold text-gray-700 py-4 px-4 text-center hover:bg-gray-100 transition-colors">
              Days Away
            </TableHead>
            <TableHead className="cursor-pointer font-semibold text-gray-700 py-4 px-4 text-center hover:bg-gray-100 transition-colors">
              Days Restricted
            </TableHead>
            <TableHead className="font-semibold text-gray-700 py-4 px-4">Status</TableHead>
            <TableHead className="text-center font-semibold text-gray-700 py-4 px-4">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {oshaReports.map((report) => (
            <TableRow
              key={report.id}
              className="cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-100"
              onClick={() => navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(report.id))}
            >
              <TableCell className="max-w-[350px] py-4 px-6">
                <div className="font-medium">{report.slug}</div>
              </TableCell>
              <TableCell>
                <div className="text-sm text-muted-foreground truncate">{report.employeeName}</div>
              </TableCell>
              <TableCell>
                <div className="text-sm text-muted-foreground truncate">{report.employeeJobTitle}</div>
              </TableCell>
              <TableCell className="py-4 px-4 text-sm text-gray-900">
                {getPrivacyAdjustedValue(report, 'employee', showPrivacyCases)}
              </TableCell>
              <TableCell className="py-4 px-4 text-sm text-gray-600">
                {getPrivacyAdjustedValue(report, 'jobTitle', showPrivacyCases)}
              </TableCell>
              <TableCell className="py-4 px-4">
                <CaseTypeBadge oshaReport={report} />
              </TableCell>
              <TableCell className="text-center py-4 px-4">
                {report.primaryRecordableOutcome === 'fatality' ? (
                  <Badge className="bg-red-100 text-red-800 border-red-200" variant="outline">
                    Yes
                  </Badge>
                ) : (
                  <span className="text-gray-400">No</span>
                )}
              </TableCell>
              <TableCell className="py-4 px-4 text-sm text-gray-600">{formatDate(report.preparedAt)}</TableCell>
              <TableCell className="py-4 px-4 text-sm text-gray-600">
                {getPrivacyAdjustedValue(report, 'employeeWorkLocation', showPrivacyCases)}
              </TableCell>
              <TableCell className="text-center py-4 px-4 text-sm text-gray-900 font-medium">
                {report.daysAwayFromWork || 0}
              </TableCell>
              <TableCell className="text-center py-4 px-4 text-sm text-gray-900 font-medium">
                {report.daysRestrictedFromWork}
              </TableCell>

              <TableCell className="py-4 px-4">
                <OshaStatusBadge oshaReport={report} />
              </TableCell>
              <TableCell className="text-center py-4 px-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(report.id));
                  }}
                  className="h-8 w-8 p-0 hover:bg-blue-50"
                >
                  <Eye className="h-4 w-4 text-blue-600" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
