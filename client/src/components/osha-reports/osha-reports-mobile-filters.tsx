import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Filter } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { OshaReportsFilters } from '@/components/osha-reports/osha-reports.types';
import { OshaCaseTypeSchema, OshaStatusSchema } from '@shared/schema.types';
import { Switch } from '@/components/ui/switch';
import z from 'zod';
import { Checkbox } from '@/components/ui/checkbox';
import { Location } from '@shared/schema.types';

export const MobileFilters = ({
  filters,
  setFilters,
  activeFilterCount,
  resetFilters,
  locations,
}: {
  filters: OshaReportsFilters;
  setFilters: React.Dispatch<React.SetStateAction<OshaReportsFilters>>;
  activeFilterCount: number;
  resetFilters: () => void;
  locations: Location[];
}) => {
  return (
    <div className="md:hidden mb-4">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex justify-between items-center w-full">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter
                {(filters.caseType ||
                  filters.status.length ||
                  !filters.showPrivacyCases ||
                  filters.includeArchived) && (
                  <Badge variant="secondary" className="ml-1 px-1.5 py-0.5 text-xs">
                    {
                      [
                        filters.caseType,
                        filters.status.length,
                        !filters.showPrivacyCases && 'Privacy',
                        filters.includeArchived && 'Archived',
                      ].filter(Boolean).length
                    }
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="max-h-[80vh]">
              <SheetHeader>
                <SheetTitle>Filter OSHA Logs</SheetTitle>
                <SheetDescription>Choose filters to narrow down your view</SheetDescription>
              </SheetHeader>
              <div className="space-y-6 py-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Case Type</Label>
                  <Select
                    value={filters.caseType}
                    onValueChange={(value) =>
                      setFilters({ ...filters, caseType: value as z.infer<typeof OshaCaseTypeSchema> })
                    }
                  >
                    {OshaCaseTypeSchema.options.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Status</Label>
                  <Select
                    value={filters.status}
                    onValueChange={(value) =>
                      setFilters({
                        ...filters,
                        status: value as z.infer<typeof OshaStatusSchema>,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      {OshaStatusSchema.options.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Location Filter */}
                <div>
                  <h3 className="font-medium mb-2">Location</h3>
                  <div className="space-y-2">
                    {locations?.map((location) => (
                      <div key={location.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={location.id}
                          checked={filters.locationId === location.id}
                          onCheckedChange={() =>
                            setFilters({
                              ...filters,
                              locationId: filters.locationId === location.id ? '' : location.id,
                            })
                          }
                        />
                        <label className="capitalize" htmlFor={location.id}>
                          {location.name}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium">Display Options</Label>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="show-privacy-mobile" className="text-sm">
                        Show Privacy Cases
                      </Label>
                      <Switch
                        checked={filters.showPrivacyCases}
                        onCheckedChange={() => setFilters({ ...filters, showPrivacyCases: !filters.showPrivacyCases })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="include-archived-mobile" className="text-sm">
                        Include Archived
                      </Label>
                      <Switch
                        checked={filters.includeArchived}
                        onCheckedChange={() => setFilters({ ...filters, includeArchived: !filters.includeArchived })}
                      />
                    </div>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    resetFilters();
                  }}
                >
                  Clear All Filters
                </Button>
              </div>
            </SheetContent>
          </Sheet>

          <div className="text-sm text-muted-foreground">
            {activeFilterCount} result{activeFilterCount !== 1 ? 's' : ''}
          </div>
        </div>
      </div>
    </div>
  );
};
