import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Calendar,
  ChevronLeft,
  Clock,
  LinkIcon,
  ShieldAlert,
  Stethoscope,
  User,
  Users,
  WrenchIcon,
} from 'lucide-react';

export const OshaReportLoading = () => {
  return (
    <div className="container mx-auto py-4 px-4">
      {/* Back button */}
      <div className="mb-3">
        <Button variant="ghost" onClick={() => {}}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>Back</span>
        </Button>
      </div>

      {/* Header skeleton */}
      <div className="flex flex-col md:flex-row justify-between items-start gap-3 mb-3">
        {/* Desktop View */}
        <div className="hidden md:block w-full">
          <div className="flex flex-wrap items-center gap-2 mb-1">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-28" />
            <Skeleton className="h-6 w-20" />
          </div>
          <Skeleton className="h-8 w-80 mb-2" />
        </div>

        {/* Mobile View */}
        <div className="md:hidden w-full">
          <div className="flex flex-wrap items-center gap-2 mb-1">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-28" />
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-9 w-9" />
          </div>
        </div>

        {/* Desktop buttons */}
        <div className="hidden md:flex gap-2 self-start">
          <Skeleton className="h-9 w-16" />
          <Skeleton className="h-9 w-28" />
          <Skeleton className="h-9 w-10" />
        </div>
      </div>

      {/* Context bar skeleton */}
      <div className="flex flex-wrap items-center text-sm text-muted-foreground mb-4 gap-y-2">
        <div className="flex items-center">
          <Calendar className="h-4 w-4 mr-1.5" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="hidden sm:block text-muted-foreground mx-2">•</div>
        <div className="flex items-center">
          <User className="h-4 w-4 mr-1.5" />
          <Skeleton className="h-4 w-32" />
        </div>
        <div className="hidden sm:block text-muted-foreground mx-2">•</div>
        <div className="flex items-center">
          <Clock className="h-4 w-4 mr-1.5" />
          <Skeleton className="h-4 w-36" />
        </div>
      </div>

      {/* Main content grid skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Left column - main content */}
        <div className="md:col-span-2 space-y-4">
          {/* Section 1: Linked Safety Event Reference */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  1
                </span>
                <LinkIcon className="h-5 w-5 text-blue-600 mr-2" />
                Linked Safety Event Reference
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md p-4 bg-blue-50">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Safety Event ID</h4>
                    <Skeleton className="h-5 w-24" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Title</h4>
                    <Skeleton className="h-5 w-48" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Date & Time</h4>
                    <Skeleton className="h-5 w-32" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Location</h4>
                    <Skeleton className="h-5 w-36" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Status</h4>
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Type</h4>
                    <Skeleton className="h-6 w-24" />
                  </div>
                  <div className="col-span-2">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Description</h4>
                    <div className="bg-white p-3 rounded-md">
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 2: Employee Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  2
                </span>
                <User className="h-5 w-5 text-blue-600 mr-2" />
                Employee Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 8 }).map((_, index) => (
                  <div key={index}>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">
                      {
                        [
                          'Employee Name',
                          'Job Title',
                          'Department',
                          'Date of Hire',
                          'Shift',
                          'Supervisor',
                          'Work Location',
                          'Privacy Case',
                        ][index]
                      }
                    </h4>
                    <Skeleton className="h-5 w-32" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Section 3: Medical Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  3
                </span>
                <Stethoscope className="h-5 w-5 text-blue-600 mr-2" />
                Medical Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index}>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">
                      {
                        [
                          'Body Part Affected',
                          'Type of Injury',
                          'Treatment Location',
                          'Type of Medical Care',
                          'Prescription Given',
                        ][index]
                      }
                    </h4>
                    <Skeleton className="h-5 w-28" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Section 4: OSHA Questions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  4
                </span>
                <ShieldAlert className="h-5 w-5 text-blue-600 mr-2" />
                OSHA Questions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index}>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">
                      {
                        ['Was hospitalized?', 'Resulted in death?', 'Days Away from Work', 'Days of Restricted Work'][
                          index
                        ]
                      }
                    </h4>
                    <Skeleton className="h-5 w-16" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Section 5: Witnesses & People */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  5
                </span>
                <Users className="h-5 w-5 text-blue-600 mr-2" />
                Witnesses & People
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index}>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">
                      {['Witnesses', 'Reported By', 'Form Prepared By', 'Prepared Date'][index]}
                    </h4>
                    <Skeleton className="h-5 w-40" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Section 6: Corrective Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  6
                </span>
                <WrenchIcon className="h-5 w-5 text-blue-600 mr-2" />
                Corrective Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Root Cause Analysis</h4>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Corrective Actions</h4>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 7: OSHA Reporting */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  7
                </span>
                <ShieldAlert className="h-5 w-5 text-blue-600 mr-2" />
                OSHA Reporting
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Primary Recordable Outcome</h4>
                  <Skeleton className="h-5 w-48" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Reason for Reportability</h4>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column - metadata & audit trail */}
        <div className="space-y-4">
          {/* Report Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Report Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-sm text-gray-500">
                      {['Report ID', 'Created', 'Updated', 'Status'][index]}
                    </span>
                    <Skeleton className="h-4 w-20" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Audit Trail */}
          <Card>
            <CardHeader>
              <CardTitle>Audit Trail</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
