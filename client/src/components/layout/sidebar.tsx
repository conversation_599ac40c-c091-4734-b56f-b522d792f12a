import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAppContext } from '@/contexts/app-context';
import { useConfig } from '@/hooks/use-config';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { cn } from '@/lib/utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import {
  AlertTriangle,
  Calendar,
  ChevronDown,
  ChevronRight,
  ClipboardCheck,
  FileText,
  type LucideIcon,
  MapPin,
  QrCode,
  Settings,
  Shield,
  ShieldCheck,
  X,
} from 'lucide-react';
import { useState } from 'react';
import { Link, useLocation } from 'wouter';

interface SidebarProps {
  onCloseMobile?: () => void;
  onOpenConsentManager?: () => void;
}

export interface NavItem {
  title: string;
  path: string;
  icon: LucideIcon;
  active?: boolean;
  badge?: number;
}

export interface NavSection {
  key: string;
  title?: string;
  items: NavItem[];
}

const bottomNavItems: NavItem[] = [
  // { title: 'Help', path: '/help', icon: HelpCircle },
  // { title: 'Contact Us', path: '/contact', icon: Globe },
  // { title: 'Manage Subscription', path: '/subscription', icon: Heart },
  // { title: 'Settings', path: '/settings', icon: Settings },
];

export const Sidebar = ({ onCloseMobile, onOpenConsentManager }: SidebarProps) => {
  const location = useLocation();
  const isMobile = useIsMobile();
  const { user } = useAppContext();
  const { VITE_MIXPANEL_TOKEN } = useConfig();
  const { hasPermission } = usePermissions();
  const [isSettingsExpanded, setIsSettingsExpanded] = useState(false);

  const navSections: NavSection[] = [
    {
      key: 'Safety Events',
      title: 'Safety',
      items: [
        ...(hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.VIEW)
          ? [{ title: 'Access Points', path: ROUTES.ACCESS_POINTS_LIST, icon: QrCode }]
          : []),
        ...(hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.VIEW)
          ? [{ title: 'Safety Events', path: ROUTES.INCIDENT_LIST, icon: AlertTriangle }]
          : []),
        ...(hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.VIEW)
          ? [{ title: 'CAPAs', path: ROUTES.CAPA_LIST, icon: ClipboardCheck }]
          : []),
      ],
    },
    ...(user?.featureFlags.ehsOsha
      ? [
          {
            key: 'OSHA',
            title: 'OSHA',
            items: [
              { title: 'OSHA Log (Form 300)', path: ROUTES.OSHA_REPORTS, icon: FileText },
              { title: 'Summary (Form 300A)', path: ROUTES.OSHA_SUMMARY, icon: Calendar },
              // { title: 'Agency Reports', path: ROUTES.OSHA_AGENCY_REPORTS, icon: AlertTriangle },
            ],
          },
        ]
      : []),
  ];

  return (
    <aside
      className={`${isMobile ? 'w-[280px]' : 'w-[240px]'} shrink-0 border-r border-gray-200 bg-white h-full flex flex-col shadow-lg`}
    >
      <div className="p-4 border-b border-gray-200 flex items-center">
        <ShieldCheck color="#ff273b" className="w-5 h-5" />
        <span className="ml-2 font-semibold text-lg">UpKeep EHS</span>
        <div className="ml-auto flex space-x-2">
          {isMobile ? (
            <Button variant="ghost" size="icon" className="w-8 h-8 rounded-full" onClick={onCloseMobile}>
              <X className="w-5 h-5 text-gray-600" />
            </Button>
          ) : (
            <button className="w-6 h-6 flex items-center justify-center rounded-full bg-indigo-100 text-indigo-800 font-medium text-xs hover:bg-indigo-200">
              {user?.firstName.charAt(0)}
            </button>
          )}
        </div>
      </div>

      <div className="overflow-y-auto flex-1">
        {navSections.map((section) => (
          <div key={section.key} className="mt-2 px-3">
            {section.title && (
              <div className="text-xs font-medium uppercase tracking-wider px-3 py-1">{section.title}</div>
            )}

            {section.items.map((item) => {
              // Check if current location starts with the item's path to handle nested routes
              // Special case for dashboard: only mark active if it's exactly /dashboard
              const isActive = location.toString().startsWith(item.path) && item.path !== ROUTES.BASE;

              return (
                <Link
                  key={item.path}
                  href={item.path}
                  className={cn(
                    'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5',
                    isActive && 'bg-blue-50 text-blue-600 font-medium border-l-2 border-blue-600',
                  )}
                  onClick={() => isMobile && onCloseMobile && onCloseMobile()}
                >
                  <item.icon className={cn('w-4 h-4', isActive && 'text-blue-600')} />
                  <span className="ml-3 text-sm">{item.title}</span>
                  {item.badge && (
                    <Badge className="ml-auto bg-blue-100 text-blue-800 hover:bg-blue-100 text-xs">{item.badge}</Badge>
                  )}
                </Link>
              );
            })}
          </div>
        ))}
      </div>

      <div className="p-3 border-t border-gray-200 mt-auto">
        <div className="flex flex-col space-y-1">
          {/* Collapsible Settings Section */}
          <div>
            <button
              onClick={() => setIsSettingsExpanded(!isSettingsExpanded)}
              className={cn(
                'sidebar-item px-3 py-2 rounded flex items-center justify-between text-gray-600 hover:bg-gray-100 my-0.5 cursor-pointer w-full',
              )}
            >
              <div className="flex items-center">
                <Settings className="w-5 h-5" />
                <span className="ml-3 text-sm font-bold">Settings</span>
              </div>
              {isSettingsExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>

            {/* Settings submenu */}
            {isSettingsExpanded && (
              <div className="ml-4 mt-1 space-y-1">
                {hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.VIEW) && (
                  <Link
                    href={ROUTES.GLOBAL_LOCATIONS_LIST}
                    className={cn(
                      'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5',
                      location.toString().startsWith(ROUTES.GLOBAL_LOCATIONS_LIST) && 'bg-blue-50 text-blue-600 font-medium border-l-2 border-blue-600',
                    )}
                    onClick={() => isMobile && onCloseMobile && onCloseMobile()}
                  >
                    <MapPin className={cn('w-4 h-4', location.toString().startsWith(ROUTES.GLOBAL_LOCATIONS_LIST) && 'text-blue-600')} />
                    <span className="ml-3 text-sm">Global Locations</span>
                  </Link>
                )}
              </div>
            )}
          </div>

          {VITE_MIXPANEL_TOKEN && (
            <div
              onClick={() => onOpenConsentManager?.()}
              className={cn(
                'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5 cursor-pointer',
              )}
            >
              <Shield className="w-5 h-5" />
              <span className="ml-3 text-sm font-bold">Privacy Settings</span>
            </div>
          )}

          <a
            href="/web/work-orders"
            className={cn('sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5')}
          >
            <Settings className="text-red-600 w-5 h-5" />
            <span className="ml-3 text-sm font-bold">UpKeep CMMS</span>
          </a>

          {bottomNavItems.map((item) => {
            const isActive = location.toString().startsWith(item.path) && item.path !== ROUTES.BASE;

            return (
              <Link
                key={item.path}
                href={item.path}
                className={cn(
                  'sidebar-item px-3 py-2 rounded flex items-center text-gray-600 hover:bg-gray-100 my-0.5',
                  isActive && 'bg-blue-50 text-blue-600 font-medium border-l-2 border-blue-600',
                )}
                onClick={() => isMobile && onCloseMobile && onCloseMobile()}
              >
                <item.icon className={cn('w-4 h-4', isActive && 'text-blue-600')} />
                <span className="ml-3 text-sm">{item.title}</span>
              </Link>
            );
          })}
        </div>
      </div>
    </aside>
  );
};
