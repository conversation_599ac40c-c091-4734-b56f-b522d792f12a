import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { usePermissions } from '@/hooks/use-permissions';
import { useAnalytics } from '@/hooks/use-analytics';
import { cn } from '@/lib/utils';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { AlertTriangle, ClipboardCheck, Menu, PenLine, Plus, QrCode } from 'lucide-react';
import { useMemo } from 'react';
import { useLocation } from 'wouter';

interface TopNavbarProps {
  onMenuClick?: () => void;
  isMobile?: boolean;
}

const PAGE_TITLE_MAP = {
  [ROUTES.INCIDENT_NEW]: 'Report Safety Event',
  [ROUTES.ACCESS_POINTS_LIST]: 'Access Points',
  [ROUTES.GLOBAL_LOCATIONS_LIST]: 'Global Locations',
  [ROUTES.CAPA_NEW]: 'Create CAPA',
  [ROUTES.INCIDENT_LIST]: 'Safety Events',
  [ROUTES.CAPA_LIST]: 'CAPAs',
  [ROUTES.ACCESS_POINTS_NEW]: 'Create Access Point',
  [ROUTES.GLOBAL_LOCATIONS_NEW]: 'Create Global Location',
};

export const Navbar = ({ onMenuClick, isMobile = false }: TopNavbarProps) => {
  const [location, navigate] = useLocation();
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();

  const getPageTitle = () => {
    return PAGE_TITLE_MAP[location as keyof typeof PAGE_TITLE_MAP] || 'UpKeep EHS';
  };

  const hasAccessToCreate = useMemo(() => {
    return (
      hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE) ||
      hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE) ||
      hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE)
    );
  }, [hasPermission]);

  return (
    <header className="bg-white border-b border-gray-200 py-3 px-4 sm:px-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {isMobile && (
            <Button variant="ghost" size="icon" className="menu-trigger -ml-1 mr-1" onClick={onMenuClick}>
              <Menu className="w-5 h-5 text-gray-700" />
            </Button>
          )}
          <h1 className="text-xl font-semibold text-gray-800 truncate max-w-[200px] sm:max-w-none">{getPageTitle()}</h1>
        </div>

        {hasAccessToCreate && (
          <div className="flex items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" className={cn(isMobile ? 'w-9 h-9 p-0' : 'px-4 py-2 h-auto')}>
                  <Plus className={isMobile ? 'w-5 h-5' : 'mr-1 w-4 h-4'} />
                  {!isMobile && 'Create'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-52 rounded-lg p-1 shadow-lg border-gray-200">
                {hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.CREATE) && (
                  <DropdownMenuItem
                    className="rounded-md flex items-center px-3 py-2 text-sm text-neutral-900 hover:bg-primary-300 hover:text-primary-700 cursor-pointer"
                    onClick={() => {
                      analytics.track(ANALYTICS_EVENTS.EVENT.FORM_VIEWED, {
                        form_entry_point: 'Global Create',
                      });
                      navigate(ROUTES.INCIDENT_NEW);
                    }}
                  >
                    <AlertTriangle className="mr-2 h-4 w-4 text-primary-500" />
                    Report Safety Event
                  </DropdownMenuItem>
                )}
                {hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE) && (
                  <DropdownMenuItem
                    className="rounded-md flex items-center px-3 py-2 text-sm text-neutral-900 hover:bg-primary-300 hover:text-primary-700 cursor-pointer"
                    onClick={() => {
                      // Force navigation even if already on access points page
                      navigate(ROUTES.ACCESS_POINTS_LIST, { replace: true });
                      // Use setTimeout to ensure navigation completes before navigating to new
                      setTimeout(() => {
                        navigate(ROUTES.ACCESS_POINTS_NEW);
                      }, 0);
                    }}
                  >
                    <QrCode className="mr-2 h-4 w-4 text-primary-500" />
                    Create Access Point
                  </DropdownMenuItem>
                )}

                {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE) && (
                  <DropdownMenuItem
                    className="rounded-md flex items-center px-3 py-2 text-sm text-neutral-900 hover:bg-primary-300 hover:text-primary-700 cursor-pointer"
                    onClick={() => navigate(ROUTES.CAPA_NEW)}
                  >
                    <ClipboardCheck className="mr-2 h-4 w-4 text-primary-500" />
                    Create CAPA
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem
                  className="rounded-md flex items-center px-3 py-2 text-sm text-neutral-900 hover:bg-primary-300 hover:text-primary-700 cursor-pointer"
                  onClick={() => navigate(ROUTES.OSHA_REPORT_NEW)}
                >
                  <PenLine className="mr-2 h-4 w-4 text-primary-500" />
                  OSHA Form 301
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
    </header>
  );
};
