import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAppContext } from '@/contexts/app-context';
import { usePermissions } from '@/hooks/use-permissions';
import { useAnalytics } from '@/hooks/use-analytics';
import { trpc } from '@/providers/trpc';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { formatDate } from '@shared/date-utils';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/router.types';
import { ALLOWED_ACTIONS, MODULES, USER_ACCOUNT_TYPES } from '@shared/user-permissions';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Check<PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Wrench } from 'lucide-react';
import { toast } from 'sonner';
import { useLocation } from 'wouter';

// Enhanced AI summary with formatted text for better visual presentation
const generateAISummary = (event: RouterOutputs['incident']['getById']) => {
  // This would ideally come from the API but for now we'll generate it client-side
  const severity = event.severity || 'unknown severity';
  const type =
    event.type === 'incident' ? 'An incident' : event.type === 'near_miss' ? 'A near miss' : 'An Observation';
  const dateTime = formatDate(event.reportedAt);

  return (
    <>
      <span className="font-semibold">{type}</span> of{' '}
      <span className="font-semibold text-indigo-700">{severity} severity</span> was reported at on {dateTime}.
      {event.oshaReportable ? (
        <span className="text-red-600 font-medium block mt-2">
          ⚠️ This safety event requires OSHA reporting within 24 hours.
        </span>
      ) : (
        <span className="text-green-600 font-medium block mt-2">
          ✓ No OSHA reporting is required for this safety event.
        </span>
      )}
    </>
  );
};

export const Insight = ({ incident: event }: { incident: RouterOutputs['incident']['getById'] }) => {
  const [_, navigate] = useLocation();
  const utils = trpc.useUtils();
  const { hasPermission } = usePermissions();
  const { user } = useAppContext();
  const analytics = useAnalytics();

  const { mutateAsync: updateIncident } = trpc.incident.update.useMutation({
    onSuccess: () => {
      utils.incident.getById.invalidate({ id: event.id });
      utils.auditTrail.get.invalidate({ entityId: event.id, entityType: 'event' });
    },
  });

  const createCapaHandler = (event: RouterOutputs['incident']['getById']) => {
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Create CAPA',
    });
    navigate(`${ROUTES.CAPA_NEW}?eventId=${event.id}`);
  };

  const editIncidentHandler = (event: RouterOutputs['incident']['getById']) => {
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Edit Event',
    });
    navigate(ROUTES.BUILD_INCIDENT_EDIT_PATH(event.id));
  };

  const markAsReviewedHandler = async (event: RouterOutputs['incident']['getById']) => {
    // Track generic action taken
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Mark as in Review',
    });

    // Track specific status change
    analytics.track(ANALYTICS_EVENTS.EVENT.REVIEWED, {
      event_id: event.id,
      previous_status: event.status,
    });

    await updateIncident({
      id: event.id,
      status: 'in_review',
    });

    toast('Mark as in Review', {
      description: 'Safety event marked as in review.',
    });
  };

  const closeWithoutActionHandler = async (event: RouterOutputs['incident']['getById']) => {
    // Track generic action taken
    analytics.track(ANALYTICS_EVENTS.EVENT.ACTION_TAKEN, {
      event_id: event.id,
      action_type: 'Close Without Action',
    });

    // Track specific status change
    analytics.track(ANALYTICS_EVENTS.EVENT.CLOSED_WITHOUT_ACTION, {
      event_id: event.id,
      previous_status: event.status,
    });

    await updateIncident({
      id: event.id,
      status: 'closed',
    });

    toast('Close Without Action', {
      description: 'Safety event closed without further action.',
    });
  };

  const actions: Record<
    string,
    { label: string; icon: React.ElementType; onClick: () => void; separator?: boolean; disabled?: boolean }
  > = {
    'create-capa': {
      label: 'Create CAPA',
      icon: ClipboardCheck,
      onClick: () => {
        createCapaHandler(event);
      },
    },
    'edit-safety-event': {
      label: 'Edit Safety Event',
      icon: Edit,
      onClick: () => {
        editIncidentHandler(event);
      },
    },
    'mark-as-reviewed': {
      label: 'Mark as In Review',
      icon: CheckCircle2,
      separator: true,
      onClick: async () => {
        await markAsReviewedHandler(event);
      },
      disabled: !hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT, event.reportedByUser?.id),
    },
    'close-without-action': {
      label: 'Close Without Action',
      icon: AlertCircle,
      onClick: async () => {
        await closeWithoutActionHandler(event);
      },
      disabled: !hasPermission(MODULES.EHS_INCIDENT, ALLOWED_ACTIONS.EDIT, event.reportedByUser?.id),
    },
  };

  return (
    <div className="relative overflow-hidden bg-blue-50 rounded-lg border border-blue-100 shadow-xs mb-6 before:absolute before:top-0 before:left-0 before:w-1 before:h-full before:bg-indigo-500">
      <div className="p-5">
        <p className="text-gray-800 leading-relaxed text-[15px]">{generateAISummary(event)}</p>
        {user?.role === USER_ACCOUNT_TYPES.ADMIN && (
          <div className="mt-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button>
                        <Wrench className="h-4 w-4" />
                        Take Action
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56" align="start" data-testid="action-menu">
                      <DropdownMenuLabel>Safety Event Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {Object.entries(actions).map(([key, action]) => {
                        if (action.disabled) {
                          return null;
                        }
                        return (
                          <div key={key}>
                            {action.separator && <DropdownMenuSeparator />}
                            <DropdownMenuItem key={key} onClick={action.onClick}>
                              <action.icon className="mr-2 h-4 w-4" />
                              {action.label}
                            </DropdownMenuItem>
                          </div>
                        );
                      })}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Log a CAPA, create a work order, or update the safety event.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </div>
    </div>
  );
};
