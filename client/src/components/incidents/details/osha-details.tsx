import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

export const OshaDetails = () => {
  return (
    <>
      {/* {incident.oshaSubmitted ? (
        <OshaSubmittedBanner
          ner
          incidentId={incident.id}
          submittedBy={incident.oshaSubmittedBy || 'System User'}
          submittedAt={incident.oshaSubmittedAt || new Date()}
          hasEditPermission={true}
        />
      ) : ( */}
      <div
        className="relative overflow-hidden rounded-lg border-2 border-orange-300 bg-amber-50 shadow-xs mb-6 animate-pulse-subtle"
        role="region"
        aria-label="Important OSHA Alert"
      >
        <div className="absolute top-0 left-0 w-1 h-full bg-red-500"></div>
        <div className="p-5">
          <div className="flex flex-col items-start gap-3">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-6 w-6 text-red-500" />
              <h3 className="text-lg font-bold text-orange-900">🚨 OSHA Reporting Required for This Safety Event</h3>
            </div>
            <div>
              <p className="text-gray-800 text-sm mb-3">
                This safety event meets OSHA criteria for reportability based on injury type or severity.
              </p>
              <p className="text-gray-800 text-sm mb-3">OSHA Reporting feature coming soon.</p>
            </div>
            <div className="flex flex-wrap gap-3">
              {/* <Button
                  onClick={() =>
                    toast('OSHA Form 301', {
                      description: 'OSHA Form 301 functionality will be available soon.',
                    })
                  }
                >
                  <FileText className="h-4 w-4 mr-2" />
                  📄 Create OSHA Log
                </Button> */}
              <Button
                variant="outline"
                onClick={() => window.open('https://www.osha.gov/recordkeeping/forms', '_blank')}
              >
                Learn more about OSHA Forms
              </Button>
            </div>
          </div>
        </div>
      </div>
      {/* )} */}
    </>
  );
};
