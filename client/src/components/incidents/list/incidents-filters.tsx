import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { reportTypeEnum, severityEnum, statusEnum } from '@shared/schema';
import { EventsFilters, ReportTypeSchema, SeveritySchema, STATUS_MAP, StatusSchema } from '@shared/schema.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';
import z from 'zod';

export const Filters = ({
  toggleFilter,
  filters,
  toggleOshaFilter,
  setFilters,
  activeFilterCount,
  resetFilters,
  trackFilterApplied,
}: {
  toggleFilter: (
    type: 'status' | 'type' | 'severity',
    value: z.infer<typeof StatusSchema> | z.infer<typeof ReportTypeSchema> | z.infer<typeof SeveritySchema>,
  ) => void;
  filters: EventsFilters;
  toggleOshaFilter: () => void;
  setFilters: React.Dispatch<React.SetStateAction<EventsFilters>>;
  activeFilterCount: number;
  resetFilters: () => void;
  trackFilterApplied: (type: 'status' | 'type' | 'severity' | 'locationIds', value: string) => void;
}) => {
  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Status
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.status?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {statusEnum.enumValues.map((status) => (
              <DropdownMenuCheckboxItem
                key={status}
                className="flex items-center space-x-2"
                onSelect={() => {
                  toggleFilter('status', status);
                  trackFilterApplied('status', status);
                }}
                checked={filters.status?.includes(status)}
              >
                {STATUS_MAP[status]}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Type
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.type?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {reportTypeEnum.enumValues.map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                className="flex items-center space-x-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('type', type);
                  trackFilterApplied('type', type);
                }}
                checked={filters.type?.includes(type)}
              >
                <span className="capitalize">{type.replace('_', ' ')}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Severity Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Severity
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.severity?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {severityEnum.enumValues.map((severity) => (
              <DropdownMenuCheckboxItem
                key={severity}
                className="flex items-center space-x-2"
                onSelect={(e) => {
                  e.preventDefault();
                  toggleFilter('severity', severity);
                  trackFilterApplied('severity', severity);
                }}
                checked={filters.severity?.includes(severity)}
              >
                <label className="capitalize">{severity}</label>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Location Filter */}
        <AsyncLocationsFilter
          selected={filters.locationIds}
          onSelect={(locationIds) => {
            setFilters({ ...filters, locationIds });
            trackFilterApplied('locationIds', locationIds.join(','));
          }}
          label="Location"
          placeholder="Search location"
        />

        {/* OSHA Reportable Filter */}
        <Button
          variant={filters.oshaReportable !== undefined ? 'default' : 'outline'}
          size="sm"
          onClick={toggleOshaFilter}
        >
          OSHA Reportable
          {filters.oshaReportable !== undefined && (
            <span className="ml-2 text-xs">({filters.oshaReportable ? 'Yes' : 'No'})</span>
          )}
        </Button>

        {/* Archive Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilters((prev) => ({ ...prev, includeArchived: !prev.includeArchived }))}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4 mr-2" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5 mr-1.5" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
