import { Button } from '@/components/ui/button';
import { AlertTriangle, HelpCircle, RefreshCw } from 'lucide-react';

export const PublicIncidentError = ({
  accessPointId,
  upkeepCompanyId,
  isErrorAccessPoint,
}: {
  accessPointId?: string | null;
  upkeepCompanyId?: string | null;
  isErrorAccessPoint?: boolean;
}) => {
  return (
    <div className="max-w-[960px] mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6 sm:py-10">
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center space-y-6">
        {/* Error Icon */}
        <div className="flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/20">
          <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>

        {/* Error Title */}
        <div className="space-y-2">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100">
            Unable to Access Safety Event Report Form
          </h1>
          <p className="text-lg text-muted-foreground max-w-md">
            {!accessPointId || !upkeepCompanyId
              ? 'This form requires a valid access link to continue.'
              : 'There was an error loading your access permissions.'}
          </p>
        </div>

        {/* Error Details */}
        <div className="bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg p-6 max-w-lg">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <HelpCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">What went wrong?</h3>
                <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                  {!accessPointId && <li>• Missing access point identifier in the URL</li>}
                  {!upkeepCompanyId && <li>• Missing role identifier in the URL</li>}
                  {isErrorAccessPoint && <li>• Unable to verify access permissions</li>}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Action Steps */}
        <div className="space-y-4 max-w-lg">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">What can you do?</h3>
          <div className="text-left space-y-3 text-sm text-muted-foreground">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">1</span>
              </div>
              <p>
                <strong className="text-gray-900 dark:text-gray-100">Check your link:</strong> Make sure you're using
                the complete safety event reporting link provided by your safety administrator.
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">2</span>
              </div>
              <p>
                <strong className="text-gray-900 dark:text-gray-100">Contact support:</strong> Reach out to your safety
                administrator or IT support for a new safety event reporting link.
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">3</span>
              </div>
              <p>
                <strong className="text-gray-900 dark:text-gray-100">Try again:</strong> If you believe this is a
                temporary issue, you can try refreshing the page.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          <Button variant="outline" onClick={() => window.location.reload()} className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh Page
          </Button>
          <Button onClick={() => window.history.back()} className="flex items-center gap-2">
            Go Back
          </Button>
        </div>

        {/* Additional Help */}
        <div className="text-xs text-muted-foreground max-w-md pt-4 border-t border-gray-200 dark:border-gray-700">
          <p>
            If you continue to experience issues, please contact your system administrator with this error code:
            <span className="font-mono bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded ml-1">
              ERR_INVALID_ACCESS_POINT
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};
