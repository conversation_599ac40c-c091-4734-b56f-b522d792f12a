import { But<PERSON> } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

export const GlobalLocationsError = () => {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Global Locations</h3>
      <p className="text-gray-600 mb-4">
        We encountered an error while loading the global locations. Please try refreshing the page.
      </p>
      <Button onClick={() => window.location.reload()} variant="outline">
        Refresh Page
      </Button>
    </div>
  );
};
