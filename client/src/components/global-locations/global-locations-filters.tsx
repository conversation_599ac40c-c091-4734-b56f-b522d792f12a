import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { GlobalLocationsFilters } from '@shared/settings.types';
import { Archive, Filter, X } from 'lucide-react';

export const Filters = ({
  filters,
  setFilters,
  activeFilterCount,
  resetFilters,
  trackFilterApplied,
}: {
  filters: GlobalLocationsFilters;
  setFilters: React.Dispatch<React.SetStateAction<GlobalLocationsFilters>>;
  activeFilterCount: number;
  resetFilters: () => void;
  trackFilterApplied: (type: 'createdBy' | 'createdDateRange' | 'includeArchived', value: string) => void;
}) => {
  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-2 px-1.5 py-0.5 text-xs">
              {activeFilterCount}
            </Badge>
          )}
        </Button>

        {/* Include Archived Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => {
            const newValue = !filters.includeArchived;
            setFilters({ ...filters, includeArchived: newValue });
            trackFilterApplied('includeArchived', newValue.toString());
          }}
        >
          <Archive className="h-4 w-4 mr-2" />
          Include Archived
        </Button>

        {/* Created By Filter */}
        <AsyncUsersFilter
          selected={filters.createdBy}
          onSelect={(createdBy) => {
            setFilters({ ...filters, createdBy });
            trackFilterApplied('createdBy', createdBy.join(','));
          }}
          label="Created By"
          placeholder="Search created by"
        />

        {/* Created Date Range Filter */}
        <DateRangePicker
          range={{
            from: filters.createdDateRange?.from,
            to: filters.createdDateRange?.to,
          }}
          setRange={(dateRange) => {
            setFilters({ ...filters, createdDateRange: dateRange });
            trackFilterApplied('createdDateRange', `${dateRange?.from}-${dateRange?.to}`);
          }}
          placeholder="Created date range"
        />

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters}>
            <X className="h-4 w-4 mr-2" />
            Clear All
          </Button>
        )}
      </div>
    </div>
  );
};
