import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RouterOutputs } from '@shared/router.types';
import { format } from 'date-fns';
import { Archive, Trash2 } from 'lucide-react';

export const GlobalLocationsMobileView = ({
  globalLocations,
  onArchive,
}: {
  globalLocations: RouterOutputs['globalLocation']['list']['result'];
  onArchive: (globalLocation: RouterOutputs['globalLocation']['list']['result'][number]) => void;
}) => {
  return (
    <div className="md:hidden space-y-4">
      {globalLocations.map((globalLocation) => (
        <div key={globalLocation.id} className="bg-white p-4 rounded-lg border shadow-sm">
          <div className="flex justify-between items-start mb-3">
            <div className="flex-1">
              <h3 className="font-medium text-gray-900 mb-1">{globalLocation.name}</h3>
              <Badge
                className={`${
                  !globalLocation.archivedAt
                    ? 'bg-green-50 text-green-700 border-green-200'
                    : 'bg-gray-50 text-gray-700 border-gray-200'
                } font-medium flex items-center border px-2 py-1 w-fit`}
                variant="outline"
              >
                {!globalLocation.archivedAt ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
              onClick={() => onArchive(globalLocation)}
            >
              {globalLocation.archivedAt ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
            </Button>
          </div>
          
          <div className="space-y-2 text-sm text-gray-600">
            <div>
              <span className="font-medium">Created By: </span>
              <span>
                {typeof globalLocation.createdBy === 'object' && globalLocation.createdBy?.fullName
                  ? globalLocation.createdBy.fullName
                  : '--'}
              </span>
            </div>
            <div>
              <span className="font-medium">Created: </span>
              <span>{format(new Date(globalLocation.createdAt), 'MMM d, yyyy')}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
