import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { trpc } from '@/providers/trpc';
import { CreateGlobalLocationSchema } from '@shared/settings.types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { usePermissions } from '@/hooks/use-permissions';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Redirect } from 'wouter';

type CreateGlobalLocationFormData = z.infer<typeof CreateGlobalLocationSchema>;

export const CreateGlobalLocationModal = ({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}) => {
  const utils = trpc.useUtils();

  const { hasPermission } = usePermissions();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CreateGlobalLocationFormData>({
    resolver: zodResolver(CreateGlobalLocationSchema),
    defaultValues: {
      name: '',
    },
  });

  const { mutateAsync: createGlobalLocation } = trpc.globalLocation.create.useMutation({
    onSuccess: () => {
      utils.globalLocation.list.invalidate();
      toast('Global location created', {
        description: 'The global location has been successfully created',
      });
    },
    onError: (error) => {
      toast('Error creating global location', {
        description: error.message || 'Please try again',
      });
    },
  });

  const onSubmit = async (data: CreateGlobalLocationFormData) => {
    try {
      await createGlobalLocation(data);
      reset();
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error('Error creating global location:', error);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!hasPermission(MODULES.GLOBAL_LOCATIONS, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.GLOBAL_LOCATIONS_LIST} />;
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create Global Location</DialogTitle>
          <DialogDescription>Create a new global location that can be used across your organization.</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              placeholder="Enter global location name"
              {...register('name')}
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Global Location'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
