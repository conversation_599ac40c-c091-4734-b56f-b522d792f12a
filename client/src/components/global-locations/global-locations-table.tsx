import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/router.types';
import { format } from 'date-fns';
import { Archive, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export const GlobalLocationsTable = ({
  globalLocations,
}: {
  globalLocations: RouterOutputs['globalLocation']['list']['result'];
}) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedGlobalLocation, setSelectedGlobalLocation] = useState<
    RouterOutputs['globalLocation']['list']['result'][number] | null
  >(null);

  const utils = trpc.useUtils();

  const { mutateAsync: deleteGlobalLocation } = trpc.globalLocation.toggleArchive.useMutation({
    onSuccess: () => {
      utils.globalLocation.list.invalidate();
      toast('Global location archived', {
        description: 'The global location has been successfully archived',
      });
    },
    onError: () => {
      toast('Error archiving global location', {
        description: 'Please try again',
      });
    },
  });

  const handleArchive = async (id: string) => {
    try {
      await deleteGlobalLocation({ id });
      setShowArchiveConfirm(false);
      setSelectedGlobalLocation(null);
    } catch (error) {
      console.error('Error archiving global location:', error);
    }
  };

  return (
    <div className="overflow-hidden hidden md:block">
      {selectedGlobalLocation && (
        <AlertDialog open={showArchiveConfirm} onOpenChange={setShowArchiveConfirm}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {selectedGlobalLocation.archivedAt ? 'Unarchive' : 'Archive'} Global Location
              </AlertDialogTitle>
              <AlertDialogDescription>
                {selectedGlobalLocation.archivedAt
                  ? 'This will restore the global location to the active list.'
                  : 'This will archive the global location. It can be restored later if needed.'}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => handleArchive(selectedGlobalLocation.id)}
                className={selectedGlobalLocation.archivedAt ? '' : 'bg-red-600 hover:bg-red-700'}
              >
                {selectedGlobalLocation.archivedAt ? 'Unarchive' : 'Archive'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Created Date</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {globalLocations.map((globalLocation) => (
            <TableRow key={globalLocation.id}>
              <TableCell className="font-medium">{globalLocation.name}</TableCell>
              <TableCell>
                <Badge
                  className={`${
                    !globalLocation.archivedAt
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-gray-50 text-gray-700 border-gray-200'
                  } font-medium flex items-center border px-2 py-1`}
                  variant="outline"
                >
                  {!globalLocation.archivedAt ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex flex-col">
                  <span className="font-medium">
                    {typeof globalLocation.createdBy === 'object' && globalLocation.createdBy?.fullName
                      ? globalLocation.createdBy.fullName
                      : '--'}
                  </span>
                  <span className="text-sm text-gray-500">
                    {typeof globalLocation.createdBy === 'object' && globalLocation.createdBy?.username
                      ? globalLocation.createdBy.username
                      : '--'}
                  </span>
                </div>
              </TableCell>
              <TableCell>{format(new Date(globalLocation.createdAt), 'MMM d, yyyy')}</TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-1">
                  {/* Archive Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                        onClick={() => {
                          setSelectedGlobalLocation(globalLocation);
                          setShowArchiveConfirm(true);
                        }}
                      >
                        {globalLocation.archivedAt ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{globalLocation.archivedAt ? 'Unarchive' : 'Archive'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
