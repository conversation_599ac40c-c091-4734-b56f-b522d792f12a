import { Button } from '@/components/ui/button';
import { MapPin } from 'lucide-react';

export const GlobalLocationsEmpty = ({
  hasActiveFilters,
  onResetFilters,
  onCreateGlobalLocation,
}: {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
  onCreateGlobalLocation: () => void;
}) => {
  if (hasActiveFilters) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <MapPin className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Global Locations Found</h3>
        <p className="text-gray-600 mb-4">
          No global locations match your current filters. Try adjusting your search criteria.
        </p>
        <Button onClick={onResetFilters} variant="outline">
          Clear Filters
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <MapPin className="h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">No Global Locations Yet</h3>
      <p className="text-gray-600 mb-4">
        Get started by creating your first global location. Global locations help organize and standardize location data across your organization.
      </p>
      <Button onClick={onCreateGlobalLocation}>
        + Create Global Location
      </Button>
    </div>
  );
};
