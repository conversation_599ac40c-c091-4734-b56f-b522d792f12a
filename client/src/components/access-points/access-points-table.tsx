import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/router.types';
import { format } from 'date-fns';
import { Archive, CheckCircle, QrCode, Trash2, XCircle } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export const AccessPointsTable = ({
  accessPoints,
  onViewQRCode,
  qrCodeUrl,
}: {
  accessPoints: RouterOutputs['accessPoint']['list']['result'];
  onViewQRCode: (accessPoint: RouterOutputs['accessPoint']['list']['result'][number], qrCodeUrl: string) => void;
  qrCodeUrl: string;
}) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedAccessPoint, setSelectedAccessPoint] = useState<
    RouterOutputs['accessPoint']['list']['result'][number] | null
  >(null);

  const utils = trpc.useUtils();

  const { mutateAsync: updateAccessPoint } = trpc.accessPoint.update.useMutation({
    onSuccess: (data) => {
      utils.accessPoint.list.invalidate();
      toast(`Access point status updated`, {
        description: `Access point has been updated to ${data.status}`,
      });
    },
    onError: () => {
      toast('Error updating access point status', {
        description: 'Please try again',
      });
    },
  });

  return (
    <div className="overflow-hidden hidden md:block">
      {selectedAccessPoint && (
        <ArchiveConfirmationDialog
          archived={selectedAccessPoint?.archived || false}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedAccessPoint.id}
          entityType="accessPoint"
        />
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Access Point Name</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Created Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {accessPoints.map((accessPoint) => (
            <TableRow key={accessPoint.id}>
              <TableCell className="font-medium">{accessPoint.name}</TableCell>
              <TableCell>{accessPoint.location?.name || accessPoint.locationId || '—'}</TableCell>
              <TableCell>
                <div className="flex flex-col">
                  <span className="font-medium">
                    {accessPoint.createdByUser
                      ? `${accessPoint.createdByUser.firstName} ${accessPoint.createdByUser.lastName}`
                      : '--'}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {accessPoint.createdByUser?.username || accessPoint.createdBy}
                  </span>
                </div>
              </TableCell>
              <TableCell>{format(new Date(accessPoint.createdAt), 'MMM d, yyyy')}</TableCell>
              <TableCell>
                <Badge
                  className={`${
                    accessPoint.status === 'active'
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-amber-50 text-amber-700 border-amber-200'
                  } font-medium flex items-center border px-2 py-1`}
                  variant="outline"
                >
                  {accessPoint.status === 'active' ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-1">
                  {/* View QR Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 rounded-sm"
                        onClick={() => onViewQRCode(accessPoint, qrCodeUrl)}
                      >
                        <QrCode className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>View QR Code</p>
                    </TooltipContent>
                  </Tooltip>

                  {/* Activate/Deactivate Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 rounded-sm"
                        onClick={() =>
                          updateAccessPoint({
                            id: accessPoint.id,
                            status: accessPoint.status === 'active' ? 'inactive' : 'active',
                          })
                        }
                      >
                        {accessPoint.status === 'active' ? (
                          <XCircle className="h-4 w-4" />
                        ) : (
                          <CheckCircle className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{accessPoint.status === 'active' ? 'Deactivate' : 'Activate'}</p>
                    </TooltipContent>
                  </Tooltip>

                  {/* Archive/Unarchive Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300 rounded-sm"
                        onClick={() => {
                          setSelectedAccessPoint(accessPoint);
                          setShowArchiveConfirm(true);
                        }}
                      >
                        {accessPoint.archived ? <Archive className="h-4 w-4" /> : <Trash2 className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{accessPoint.archived ? 'Unarchive' : 'Archive'}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
