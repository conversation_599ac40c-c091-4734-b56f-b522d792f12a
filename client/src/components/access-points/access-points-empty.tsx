import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { Plus, QrCode } from 'lucide-react';

interface AccessPointsEmptyProps {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
  onCreateAccessPoint: () => void;
}

export const AccessPointsEmpty = ({
  hasActiveFilters,
  onResetFilters,
  onCreateAccessPoint,
}: AccessPointsEmptyProps) => {
  const isMobile = useIsMobile();
  const { hasPermission } = usePermissions();
  if (hasActiveFilters) {
    // Empty state when filters are applied but no results
    return (
      <div
        className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
      >
        <div className="text-center max-w-md">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
            <QrCode className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
            No access points found
          </h3>
          <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm' : 'text-base'}`}>
            No access points match your current filters. Try adjusting your search criteria or clear filters to see all
            access points.
          </p>
          <Button variant="outline" onClick={onResetFilters} className={isMobile ? 'w-full' : ''}>
            Clear all filters
          </Button>
        </div>
      </div>
    );
  }

  // Empty state when no access points exist at all
  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
          <QrCode className="h-8 w-8 text-blue-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>
          No access points created yet
        </h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          Create QR access points throughout your facility to enable instant safety event reporting. Employees,
          contractors, and visitors can scan to report safety concerns from any location.
        </p>
        <div className="space-y-3">
          {hasPermission(MODULES.EHS_ACCESS_POINT, ALLOWED_ACTIONS.CREATE) && (
            <Button className={`${isMobile ? 'w-full' : ''} `} onClick={onCreateAccessPoint}>
              <Plus className="h-4 w-4 mr-2" />
              Create Access Point
            </Button>
          )}
          {isMobile && (
            <p className="text-xs text-gray-400 mt-3">
              Deploy QR codes at strategic locations for comprehensive safety coverage.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
