import { AsyncLocationsFilter } from '@/components/composite/async-locations-filter';
import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { accessPointStatusEnum } from '@shared/schema';
import type { AccessPointsFilters } from '@shared/schema.types';
import { Archive, ChevronDown, Filter, X } from 'lucide-react';

export const Filters = ({
  filters,
  setFilters,
  toggleFilter,
  activeFilterCount,
  resetFilters,
  trackFilterApplied,
}: {
  filters: AccessPointsFilters;
  setFilters: React.Dispatch<React.SetStateAction<AccessPointsFilters>>;
  toggleFilter: (type: 'status' | 'locationId' | 'createdBy', value: string) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  trackFilterApplied: (
    type: 'status' | 'locationId' | 'createdBy' | 'createdDateRange' | 'includeArchived',
    value: string,
  ) => void;
}) => {
  return (
    <div className="mb-6 overflow-x-auto py-2 hidden md:block">
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" className={activeFilterCount > 0 ? 'bg-muted' : ''}>
          <Filter className="h-4 w-4 mr-2" />
          Filters {activeFilterCount > 0 && `(${activeFilterCount})`}
        </Button>

        {/* Status Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              Status
              <Badge className="ml-1 px-1 py-0 h-5" variant="secondary">
                {filters.status?.length || 'All'}
              </Badge>
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {accessPointStatusEnum.enumValues.map((status) => (
              <DropdownMenuCheckboxItem
                key={status}
                className="flex items-center space-x-2"
                checked={filters.status?.includes(status)}
                onCheckedChange={() => {
                  toggleFilter('status', status);
                  trackFilterApplied('status', status);
                }}
              >
                <span className="capitalize">{status}</span>
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Location Filter */}
        <AsyncLocationsFilter
          selected={filters.locationId}
          onSelect={(locationIds) => setFilters({ ...filters, locationId: locationIds })}
          label="Location"
          placeholder="Search location"
        />

        {/* Created By Filter */}
        <AsyncUsersFilter
          selected={filters.createdBy}
          onSelect={(createdBy) => {
            setFilters({ ...filters, createdBy });
            trackFilterApplied('createdBy', createdBy.join(','));
          }}
          label="Created By"
          placeholder="Search created by"
        />

        {/* Date Range Filter */}
        <DateRangePicker
          placeholder="Created Date Range"
          range={{
            from: filters.createdDateRange?.from,
            to: filters.createdDateRange?.to,
          }}
          setRange={(range) => {
            setFilters((prev) => ({
              ...prev,
              createdDateRange: range,
            }));
            trackFilterApplied('createdDateRange', range?.from?.toISOString() + ' - ' + range?.to?.toISOString() || '');
          }}
        />

        {/* Archive Filter */}
        <Button
          variant={filters.includeArchived ? 'default' : 'outline'}
          size="sm"
          onClick={() => {
            setFilters((prev) => ({ ...prev, includeArchived: !prev.includeArchived }));
            trackFilterApplied('includeArchived', `${filters.includeArchived}`);
          }}
          className={filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}
        >
          <Archive className="h-4 w-4 mr-2" />
          Include Archived
        </Button>

        {/* Reset Filters */}
        {activeFilterCount > 0 && (
          <Button variant="ghost" size="sm" onClick={resetFilters} className="h-9">
            <X className="h-3.5 w-3.5 mr-1.5" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
};
