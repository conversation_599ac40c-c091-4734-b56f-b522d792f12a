import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/router.types';
import { format } from 'date-fns';
import { Archive, CheckCircle, MapPin, QrCode, Trash2, User, XCircle } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export const AccessPointsMobileView = ({
  accessPoints,
  onViewQRCode,
  qrCodeUrl,
}: {
  accessPoints: RouterOutputs['accessPoint']['list']['result'];
  onViewQRCode: (accessPoint: RouterOutputs['accessPoint']['list']['result'][number], qrCodeUrl: string) => void;
  qrCodeUrl: string;
}) => {
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedAccessPoint, setSelectedAccessPoint] = useState<
    RouterOutputs['accessPoint']['list']['result'][number] | null
  >(null);

  const utils = trpc.useUtils();

  const { mutateAsync: updateAccessPoint } = trpc.accessPoint.update.useMutation({
    onSuccess: (data) => {
      utils.accessPoint.list.invalidate();
      toast(`Access point status updated to ${data.status}`, {
        description: `Access point has been updated to ${data.status}`,
      });
    },
    onError: () => {
      toast('Error updating access point status', {
        description: 'Please try again',
      });
    },
  });

  return (
    <div className="space-y-4 block md:hidden">
      {selectedAccessPoint && (
        <ArchiveConfirmationDialog
          archived={selectedAccessPoint?.archived || false}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedAccessPoint.id}
          entityType="accessPoint"
        />
      )}
      {accessPoints.map((accessPoint: RouterOutputs['accessPoint']['list']['result'][number]) => {
        return (
          <Card key={accessPoint.id}>
            <CardHeader>
              <CardTitle>{accessPoint.name}</CardTitle>
              <CardDescription>
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <MapPin className="h-3 w-3 mr-1" />
                  {accessPoint.location?.name || accessPoint.locationId || 'No location'}
                </div>
                <Badge
                  className={`${
                    accessPoint.status === 'active'
                      ? 'bg-green-50 text-green-700 border-green-200'
                      : 'bg-amber-50 text-amber-700 border-amber-200'
                  } font-medium flex items-center border px-2 py-1`}
                  variant="outline"
                >
                  {accessPoint.status === 'active' ? 'Active' : 'Inactive'}
                </Badge>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                <div className="flex items-center">
                  <User className="h-3 w-3 mr-1" />
                  <div className="flex flex-col">
                    <span className="font-medium">
                      {accessPoint.createdByUser
                        ? `${accessPoint.createdByUser.firstName} ${accessPoint.createdByUser.lastName}`
                        : '--'}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {accessPoint.createdByUser?.username || accessPoint.createdBy}
                    </span>
                  </div>
                </div>
                <span>{format(new Date(accessPoint.createdAt), 'MMM d, yyyy')}</span>
              </div>
            </CardContent>
            <CardFooter>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => onViewQRCode(accessPoint, qrCodeUrl)}
                >
                  <QrCode className="h-3 w-3 mr-1" />
                  View QR
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    updateAccessPoint({
                      id: accessPoint.id,
                      status: accessPoint.status === 'active' ? 'inactive' : 'active',
                    })
                  }
                >
                  {accessPoint.status === 'active' ? (
                    <XCircle className="h-3 w-3 mr-1" />
                  ) : (
                    <CheckCircle className="h-3 w-3 mr-1" />
                  )}
                  {accessPoint.status === 'active' ? 'Deactivate' : 'Activate'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="text-red-600 hover:text-red-700 border-gray-300 hover:border-red-300"
                  onClick={() => {
                    setSelectedAccessPoint(accessPoint);
                    setShowArchiveConfirm(true);
                  }}
                >
                  {accessPoint.archived ? <Archive className="h-3 w-3" /> : <Trash2 className="h-3 w-3" />}
                </Button>
              </div>
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );
};
