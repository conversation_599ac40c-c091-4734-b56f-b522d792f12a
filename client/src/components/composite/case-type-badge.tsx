import { Badge } from '@/components/ui/badge';
import { oshaTypeEnum } from '@shared/schema';
import { OSHA_TYPE_MAP } from '@shared/schema.types';

export const CaseTypeBadge = ({ caseType }: { caseType: (typeof oshaTypeEnum.enumValues)[number] }) => {
  const caseTypeMap = {
    [oshaTypeEnum.enumValues[0]]: 'bg-red-100 text-red-800 hover:bg-red-200',
    [oshaTypeEnum.enumValues[1]]: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
    [oshaTypeEnum.enumValues[2]]: 'bg-orange-100 text-orange-800 hover:bg-orange-200',
    [oshaTypeEnum.enumValues[3]]: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
    [oshaTypeEnum.enumValues[4]]: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
    [oshaTypeEnum.enumValues[5]]: 'bg-green-100 text-green-800 hover:bg-green-200',
  };

  return (
    <Badge className={caseTypeMap[caseType as keyof typeof caseTypeMap]} variant="outline">
      {OSHA_TYPE_MAP[caseType]}
    </Badge>
  );
};
