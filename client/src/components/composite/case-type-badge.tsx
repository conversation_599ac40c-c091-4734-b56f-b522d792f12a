import { Badge } from '@/components/ui/badge';
import { RouterOutputs } from '@shared/router.types';
import { OshaCaseTypeSchema } from '@shared/schema.types';
import { z } from 'zod';

export const CaseTypeBadge = ({
  oshaReport,
}: {
  oshaReport: RouterOutputs['oshaReport']['list']['result'][number];
}) => {
  let caseType: z.infer<typeof OshaCaseTypeSchema> = OshaCaseTypeSchema.enum.all;
  if (oshaReport.wasDeceased) {
    caseType = OshaCaseTypeSchema.enum.death;
  } else if (oshaReport.daysAwayFromWork > 0) {
    caseType = OshaCaseTypeSchema.enum.days_away;
  } else if (oshaReport.daysRestrictedFromWork > 0) {
    caseType = OshaCaseTypeSchema.enum.restricted;
  } else {
    caseType = OshaCaseTypeSchema.enum.other_recordable;
  }

  const caseTypeMap = {
    [OshaCaseTypeSchema.enum.death]: 'bg-red-100 text-red-800 hover:bg-red-200',
    [OshaCaseTypeSchema.enum.days_away]: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
    [OshaCaseTypeSchema.enum.restricted]: 'bg-orange-100 text-orange-800 hover:bg-orange-200',
    [OshaCaseTypeSchema.enum.other_recordable]: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
    [OshaCaseTypeSchema.enum.all]: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
  };

  return (
    <Badge className={caseTypeMap[caseType]} variant="outline">
      {caseType}
    </Badge>
  );
};
