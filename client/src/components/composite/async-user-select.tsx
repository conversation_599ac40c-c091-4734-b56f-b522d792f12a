import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteUsersPublic } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { PopoverAnchor, PopoverPortal } from '@radix-ui/react-popover';
import { RouterOutputs } from '@shared/router.types';
import { User } from '@shared/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search, X } from 'lucide-react';
import * as React from 'react';

interface AsyncUserSelectProps {
  value?: User['id'][] | User['id'] | null;
  onChange: (selected: User['id'][] | User['id'] | undefined) => void;
  className?: string;
  placeholder?: string;
  multiple?: boolean;
  disabled?: boolean;
}

export const AsyncUserSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select user...',
  multiple = false,
  disabled = false,
  ...props
}: AsyncUserSelectProps) => {
  const { user } = useAppContext();
  const [open, setOpen] = React.useState(false);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);

  const [search, setSearch] = React.useState('');
  const debouncedSearch = useDebounce(search, 300);

  const {
    data: users,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteUsersPublic({
    search: debouncedSearch,
    upkeepCompanyId: user?.upkeepCompanyId!,
    enabled: !!user?.upkeepCompanyId,
    limit: 100,
  });

  const options = React.useMemo(() => {
    return (
      users?.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, RouterOutputs['user']['getUsersPublic']['result'][number]>,
      ) || {}
    );
  }, [users]);

  const handleUnselect = (userId: User['id']) => {
    if (disabled) return;

    if (multiple && Array.isArray(selected)) {
      onChange(selected.filter((id) => id !== userId));
    } else {
      onChange(undefined);
    }
  };

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (
        document.activeElement === inputRef.current &&
        e.key === 'Backspace' &&
        Array.isArray(selected) &&
        selected.length > 0 &&
        onChange
      ) {
        onChange(selected.filter((_, index) => index !== selected.length - 1));
      }

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onChange, selected, disabled]);

  // Handle infinite scrolling
  const isFetchingRef = React.useRef(false);

  React.useEffect(() => {
    // Only set up scroll listener when popover is open
    if (!open) return;

    // Add a small delay to ensure the popover content is rendered
    const setupTimeoutId = setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea || !hasNextPage || !fetchNextPage) return;

      let scrollTimeoutId: NodeJS.Timeout;

      const handleScroll = () => {
        // Clear any existing timeout
        clearTimeout(scrollTimeoutId);

        // Debounce the scroll event
        scrollTimeoutId = setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
          // Trigger fetchNextPage when user scrolls to within 50px of the bottom
          if (distanceFromBottom < 50 && !isFetchingNextPage && !isFetchingRef.current) {
            isFetchingRef.current = true;
            try {
              const result = fetchNextPage();
              // Check if result is a promise-like object
              if (result && typeof result === 'object' && 'then' in result) {
                (result as Promise<unknown>).finally(() => {
                  isFetchingRef.current = false;
                });
              } else {
                // If fetchNextPage doesn't return a promise, reset immediately
                isFetchingRef.current = false;
              }
            } catch (error) {
              console.error('Error calling fetchNextPage:', error);
              isFetchingRef.current = false;
            }
          }
        }, 100); // 100ms debounce
      };

      scrollArea.addEventListener('scroll', handleScroll);

      // Cleanup function for the scroll listener
      return () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100); // Small delay to ensure DOM is ready

    return () => {
      clearTimeout(setupTimeoutId);
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange: React.ChangeEventHandler<HTMLInputElement> = (event) => {
    if (disabled) return;
    setSearch(event.target.value);
  };

  const onSelect = (userId: User['id']) => {
    if (!onChange || disabled) return;

    if (multiple && Array.isArray(selected)) {
      const alreadySelected = selected.some((id) => id === userId);

      if (alreadySelected) {
        onChange(selected.filter((id) => id !== userId));
      } else {
        onChange([...selected, userId]);
      }

      setOpen(true);
    } else {
      onChange(userId);
      setOpen(false);
    }
  };

  const renderSelectedValue = () => {
    if (isLoading) {
      return <span className="text-muted-foreground text-base md:text-sm">Loading...</span>;
    }

    if (multiple && Array.isArray(selected) && selected.length > 0) {
      return selected.map((userId) => {
        const user = options[userId];
        if (!user) return null;

        return (
          <Badge
            variant="outline"
            key={userId}
            className="flex items-center gap-1 pr-1 group-hover:bg-background"
            onClick={() => handleUnselect(userId)}
          >
            {user.fullName}
            <Button
              variant="ghost"
              size="xs"
              className="border-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleUnselect(userId);
                }
              }}
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleUnselect(userId);
              }}
            >
              <X size={16} className="text-muted-foreground" />
            </Button>
          </Badge>
        );
      });
    }

    if (!multiple && selected && options[selected as string]) {
      const user = options[selected as string];
      return (
        <div className="flex items-center gap-2">
          <span className="text-sm">{user.fullName}</span>
        </div>
      );
    }

    return <span className="text-muted-foreground">{placeholder}</span>;
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          aria-expanded={open}
          aria-disabled={disabled}
          className={cn(
            'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && !disabled && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            Array.isArray(selected) && selected.length > 1 ? 'h-full' : 'h-9',
            disabled && 'cursor-not-allowed opacity-50',
            'cursor-pointer',
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {!multiple && selected && (
              <X size={16} className="text-muted-foreground" onClick={() => handleUnselect(selected as string)} />
            )}
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverAnchor />
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search users..."
                onChange={handleSearchChange}
                disabled={disabled}
                ref={inputRef}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              <ul>
                {Object.keys(options).length === 0 && !isLoading && (
                  <li className="p-2 text-sm text-muted-foreground text-center">No users found</li>
                )}
                {Object.values(options).map((user) => (
                  <li
                    onClick={() => !disabled && onSelect(user.id)}
                    className={cn(
                      'flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3',
                      disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                    )}
                    key={user.id}
                  >
                    <Check
                      className={cn(
                        'size-4 flex-shrink-0',
                        Array.isArray(selected)
                          ? selected.some((id) => id === user.id)
                            ? 'opacity-100'
                            : 'opacity-0'
                          : selected === user.id
                            ? 'opacity-100'
                            : 'opacity-0',
                      )}
                    />
                    <span>{user.fullName}</span>
                  </li>
                ))}
                {isFetchingNextPage && (
                  <li className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
