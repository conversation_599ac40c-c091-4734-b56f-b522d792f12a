import { Badge } from '@/components/ui/badge';
import { CheckCircleIcon, ClipboardCheck, Bell } from 'lucide-react';
import { statusEnum } from '@shared/schema';
import { STATUS_MAP } from '@shared/schema.types';

export const StatusBadge = ({ status }: { status: (typeof statusEnum.enumValues)[number] }) => {
  const statusMap = STATUS_MAP[status];

  const statusIconMap = {
    open: <Bell className="h-3 w-3 mr-1" />,
    in_review: <ClipboardCheck className="h-3 w-3 mr-1" />,
    closed: <CheckCircleIcon className="h-3 w-3 mr-1" />,
  };

  const statusColorMap = {
    open: 'bg-blue-50 text-blue-700 border-blue-200',
    in_review: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    closed: 'bg-green-50 text-green-700 border-green-200',
  };

  const icon = statusIconMap[status];

  return (
    <Badge
      className={`${statusColorMap[status]} font-medium flex items-center border px-2 py-1`}
      variant="outline"
    >
      {icon}
      {statusMap}
    </Badge>
  );
};
