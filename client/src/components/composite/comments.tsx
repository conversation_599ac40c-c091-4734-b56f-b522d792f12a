import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteUsersPublic } from '@/hooks/use-paginated-data';
import { trpc } from '@/providers/trpc';
import { formatDate } from '@shared/date-utils';
import { statusEnum } from '@shared/schema';
import { MessageSquare, MoreVertical, Send, Trash2 } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { RouterOutputs } from 'shared/router.types';
import { toast } from 'sonner';

// Type definitions
type Comment = RouterOutputs['comment']['list'][0];

interface TeamMemberMention {
  id: string;
  fullName: string;
}

interface DropdownPosition {
  top: number;
  left: number;
}

// Constants
const MAX_COMMENT_LENGTH = 500;
const DROPDOWN_POSITION = { top: 34, left: 20 };

export const CommentsSection = ({
  entityId,
  entityType,
  entitySlug,
  entityTitle,
  status,
}: {
  entityId: string;
  entityType: 'event' | 'capa';
  entitySlug: string;
  entityTitle: string;
  status: (typeof statusEnum.enumValues)[number];
}) => {
  // State management
  const [comment, setComment] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [showMentionDropdown, setShowMentionDropdown] = useState(false);
  const [mentionFilter, setMentionFilter] = useState('');
  const [dropdownPosition, setDropdownPosition] = useState<DropdownPosition>(DROPDOWN_POSITION);
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0);
  const [charCount, setCharCount] = useState(MAX_COMMENT_LENGTH);
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeletingComment, setIsDeletingComment] = useState(false);

  // Refs
  const textareaRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Context
  const { user } = useAppContext();

  const utils = trpc.useUtils();

  // Fetch data
  const { data: fetchedComments = [], isLoading: isLoadingComments } = trpc.comment.list.useQuery({
    entityId,
    entityType,
    options: { limit: 50, offset: 0 },
  });

  const { data: users = [] } = useInfiniteUsersPublic({
    upkeepCompanyId: user?.upkeepCompanyId!,
  });

  // Transform users to team members format
  const teamMembers: TeamMemberMention[] = useMemo(() => {
    return users.map((user) => ({
      id: user.id,
      fullName: user.fullName ?? '[user has not accepted invite]',
    }));
  }, [users]);

  // Mutations
  const createCommentMutation = trpc.comment.create.useMutation({
    onSuccess: () => {
      toast('Comment Added', {
        description: 'Your comment has been added successfully.',
      });
      setComment('');
      setCharCount(MAX_COMMENT_LENGTH);
      if (textareaRef.current) {
        textareaRef.current.innerHTML = '';
      }
      utils.auditTrail.get.invalidate({
        entityId,
        entityType,
      });

      utils.comment.list.invalidate({
        entityId,
        entityType,
      });
    },
    onError: (error) => {
      console.error('Error adding comment:', error);
      toast('Error', {
        description: 'Failed to add comment. Please try again.',
      });
    },
  });

  const deleteCommentMutation = trpc.comment.delete.useMutation({
    onSuccess: () => {
      toast('Comment Deleted', {
        description: 'The comment has been removed successfully.',
      });
      utils.comment.list.invalidate({
        entityId,
        entityType,
      });
    },
    onError: (error) => {
      console.error('Error deleting comment:', error);
      toast('Error', {
        description: 'Failed to delete comment. Please try again.',
      });
    },
  });

  // Utility functions
  const getTextContent = useCallback((): string => {
    return textareaRef.current?.textContent || '';
  }, []);

  const getCursorPosition = useCallback((): number => {
    if (!textareaRef.current) return 0;
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return 0;

    const range = selection.getRangeAt(0);
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(textareaRef.current);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString().length;
  }, []);

  const setCursorPosition = useCallback((position: number) => {
    if (!textareaRef.current) return;
    const selection = window.getSelection();
    if (!selection) return;

    let currentPos = 0;
    const walker = document.createTreeWalker(textareaRef.current, NodeFilter.SHOW_TEXT, null);

    let node;
    while ((node = walker.nextNode())) {
      const textLength = node.textContent?.length || 0;
      if (currentPos + textLength >= position) {
        const range = document.createRange();
        range.setStart(node, position - currentPos);
        range.setEnd(node, position - currentPos);
        selection.removeAllRanges();
        selection.addRange(range);
        return;
      }
      currentPos += textLength;
    }
  }, []);

  const formatMentions = useCallback(
    (text: string): string => {
      // Escape HTML characters
      let formattedText = text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');

      // Replace @userId mentions with blue spans showing the actual name
      teamMembers.forEach((member) => {
        const userIdMentionRegex = new RegExp(`@${member.id}\\b`, 'gi');
        formattedText = formattedText.replace(
          userIdMentionRegex,
          `<span style="color: #3e63dd; font-weight: 500;">@${member.fullName}</span>`,
        );
      });

      return formattedText.replace(/\n/g, '<br>');
    },
    [teamMembers],
  );

  const formatDisplayedMentions = useCallback(
    (content: string) => {
      if (!content || typeof content !== 'string') return content;

      let processedContent = content;

      // Replace @userId mentions with actual names first
      teamMembers.forEach((member) => {
        const userIdMentionRegex = new RegExp(`@${member.id}\\b`, 'gi');
        processedContent = processedContent.replace(userIdMentionRegex, `@${member.fullName}`);
      });

      // Create an array to track all mentions and their positions
      const mentions: Array<{ start: number; end: number; member: TeamMemberMention }> = [];

      // Find all mentions in the processed content
      teamMembers.forEach((member) => {
        const escapedName = member.fullName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const mentionRegex = new RegExp(`@${escapedName}\\b`, 'gi');

        let match;
        while ((match = mentionRegex.exec(processedContent)) !== null) {
          mentions.push({
            start: match.index,
            end: match.index + match[0].length,
            member: member,
          });
        }
      });

      // Sort mentions by start position
      mentions.sort((a, b) => a.start - b.start);

      // Remove overlapping mentions (keep the first one)
      const uniqueMentions = mentions.filter((mention, index) => {
        if (index === 0) return true;
        const prevMention = mentions[index - 1];
        return mention.start >= prevMention.end;
      });

      if (uniqueMentions.length === 0) {
        return processedContent;
      }

      // Build the JSX parts
      const parts: React.ReactNode[] = [];
      let lastIndex = 0;

      uniqueMentions.forEach((mention) => {
        // Add text before the mention
        if (mention.start > lastIndex) {
          parts.push(
            <span key={`text-${lastIndex}-${mention.start}`}>
              {processedContent.substring(lastIndex, mention.start)}
            </span>,
          );
        }

        // Add the highlighted mention
        parts.push(
          <span key={`mention-${mention.start}-${mention.member.id}`} className="text-blue-600 font-medium">
            @{mention.member.fullName}
          </span>,
        );

        lastIndex = mention.end;
      });

      // Add remaining text
      if (lastIndex < processedContent.length) {
        parts.push(<span key={`text-${lastIndex}`}>{processedContent.substring(lastIndex)}</span>);
      }

      return parts;
    },
    [teamMembers],
  );

  // New function to convert displayed content back to stored format (with userIds)
  const convertDisplayedContentToStored = useCallback(
    (displayedText: string): string => {
      let storedText = displayedText;

      // Replace displayed @username mentions back to @userId format
      // Sort by name length (longest first) to avoid partial replacements
      const sortedMembers = [...teamMembers].sort((a, b) => b.fullName.length - a.fullName.length);

      sortedMembers.forEach((member) => {
        const escapedName = member.fullName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const displayNameRegex = new RegExp(`@${escapedName}\\b`, 'gi');
        storedText = storedText.replace(displayNameRegex, `@${member.id}`);
      });

      return storedText;
    },
    [teamMembers],
  );

  // Event handlers
  const handleCommentChange = useCallback(() => {
    const displayedText = getTextContent();
    const cursorPosition = getCursorPosition();
    const savedCursorPos = cursorPosition;

    // Convert displayed content back to stored format (with userIds)
    const storedText = convertDisplayedContentToStored(displayedText);

    setComment(storedText);
    setCharCount(MAX_COMMENT_LENGTH - storedText.length);

    // @mention detection - look for @ followed by text that could match a team member name
    const beforeCursor = displayedText.substring(0, cursorPosition);
    const lastAtIndex = beforeCursor.lastIndexOf('@');

    if (lastAtIndex !== -1) {
      const textAfterAt = beforeCursor.substring(lastAtIndex + 1);
      const hasSpaceAfterAt = textAfterAt.includes(' ');

      if (!hasSpaceAfterAt) {
        // Filter team members based on what user is typing
        const filteredMembers = teamMembers.filter((member) =>
          member.fullName.toLowerCase().includes(textAfterAt.toLowerCase()),
        );

        if (filteredMembers.length > 0) {
          setMentionFilter(textAfterAt);
          setShowMentionDropdown(true);
          setSelectedMentionIndex(0);
          setDropdownPosition(DROPDOWN_POSITION);

          setTimeout(() => {
            if (textareaRef.current) {
              textareaRef.current.innerHTML = formatMentions(storedText);
              setCursorPosition(savedCursorPos);
            }
          }, 0);
          return;
        }
      }
    }

    setShowMentionDropdown(false);
    setSelectedMentionIndex(0);

    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.innerHTML = formatMentions(storedText);
        setCursorPosition(savedCursorPos);
      }
    }, 0);
  }, [
    getTextContent,
    getCursorPosition,
    formatMentions,
    setCursorPosition,
    teamMembers,
    convertDisplayedContentToStored,
  ]);

  const handleSelectMention = useCallback(
    (userId: string) => {
      if (!textareaRef.current) return;

      const displayedText = getTextContent();
      const cursorPosition = getCursorPosition();
      const beforeCursor = displayedText.substring(0, cursorPosition);
      const lastAtSymbol = beforeCursor.lastIndexOf('@');

      if (lastAtSymbol !== -1) {
        const beforeMention = displayedText.substring(0, lastAtSymbol);
        const afterCursor = displayedText.substring(cursorPosition);

        // Create stored format (with userId)
        const newStoredComment = `${convertDisplayedContentToStored(beforeMention)}@${userId} ${convertDisplayedContentToStored(afterCursor)}`;

        // Find the user's full name for display and cursor positioning
        const selectedUser = teamMembers.find((member) => member.id === userId);
        const displayName = selectedUser?.fullName || userId;

        setComment(newStoredComment);
        setShowMentionDropdown(false);
        setCharCount(MAX_COMMENT_LENGTH - newStoredComment.length);

        textareaRef.current.innerHTML = formatMentions(newStoredComment);

        setTimeout(() => {
          // Calculate cursor position based on the displayed name length, not userId length
          const newCursorPos = lastAtSymbol + displayName.length + 2; // +1 for @, +1 for space
          setCursorPosition(newCursorPos);
          textareaRef.current?.focus();
        }, 0);
      }
    },
    [
      getTextContent,
      getCursorPosition,
      formatMentions,
      setCursorPosition,
      teamMembers,
      convertDisplayedContentToStored,
    ],
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (showMentionDropdown) {
        // Get filtered members for navigation
        const filteredMembers = teamMembers.filter((member) =>
          member.fullName.toLowerCase().includes(mentionFilter.toLowerCase()),
        );

        switch (e.key) {
          case 'Escape':
            setShowMentionDropdown(false);
            setSelectedMentionIndex(0);
            e.preventDefault();
            break;
          case 'ArrowDown':
            setSelectedMentionIndex((prev) => (prev < filteredMembers.length - 1 ? prev + 1 : 0));
            e.preventDefault();
            break;
          case 'ArrowUp':
            setSelectedMentionIndex((prev) => (prev > 0 ? prev - 1 : filteredMembers.length - 1));
            e.preventDefault();
            break;
          case 'Enter':
            if (
              filteredMembers.length > 0 &&
              selectedMentionIndex >= 0 &&
              selectedMentionIndex < filteredMembers.length
            ) {
              const selectedMember = filteredMembers[selectedMentionIndex];
              handleSelectMention(selectedMember.id);
            }
            e.preventDefault();
            break;
        }
      }
    },
    [showMentionDropdown, selectedMentionIndex, handleSelectMention, teamMembers, mentionFilter],
  );

  const handleSubmitComment = useCallback(async () => {
    if (!comment.trim() || !entityId || !user) return;

    setIsSubmittingComment(true);
    try {
      await createCommentMutation.mutateAsync({
        entityId,
        entityType,
        entitySlug,
        entityTitle,
        status,
        content: comment,
      });
    } finally {
      setIsSubmittingComment(false);
    }
  }, [comment, entityId, entityType, entitySlug, user]);

  const handleDeleteComment = useCallback((commentId: string) => {
    setCommentToDelete(commentId);
    setShowDeleteConfirm(true);
  }, []);

  const confirmDeleteComment = useCallback(async () => {
    if (!commentToDelete) return;

    setIsDeletingComment(true);
    try {
      await deleteCommentMutation.mutateAsync({ id: commentToDelete });
    } finally {
      setIsDeletingComment(false);
      setShowDeleteConfirm(false);
      setCommentToDelete(null);
    }
  }, [commentToDelete, deleteCommentMutation]);

  const cancelDeleteComment = useCallback(() => {
    setShowDeleteConfirm(false);
    setCommentToDelete(null);
  }, []);

  // Effects
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showMentionDropdown &&
        dropdownRef.current &&
        textareaRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !textareaRef.current.contains(event.target as Node)
      ) {
        setShowMentionDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showMentionDropdown]);

  // Loading state
  if (isLoadingComments) {
    return (
      <Card className="shadow-xs">
        <CardHeader className="pb-3 pt-4 px-4">
          <div className="flex items-center">
            <MessageSquare className="h-4 w-4 mr-2" />
            <CardTitle className="text-base">Comments</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="px-4 pb-4">
          <div className="text-center py-8 text-gray-500">Loading comments...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-xs">
      <CardHeader>
        <div className="flex items-center">
          <MessageSquare className="h-4 w-4 mr-2" />
          <CardTitle className="text-base">Comments</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-4">
        {fetchedComments.length > 0 ? (
          <div className="space-y-4">
            {fetchedComments.map((comment: Comment) => (
              <div key={comment.id} className="bg-gray-50 border rounded-md p-3 relative">
                <div className="flex items-start mb-2">
                  <Avatar className="h-5 w-5 sm:h-6 sm:w-6 mr-2 mt-0.5 shrink-0">
                    <AvatarFallback>
                      {users.find((u) => u.id === comment.userId)?.firstName?.charAt(0) || '?'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center">
                      <span className="font-bold text-sm sm:text-base mr-2 truncate">
                        {users.find((u) => u.id === comment.userId)?.fullName || ''}
                      </span>
                      <span className="text-gray-500 text-xs sm:text-sm">{formatDate(comment.createdAt)}</span>
                    </div>

                    <div className="mt-1 text-gray-700 whitespace-pre-line text-sm sm:text-base">
                      {formatDisplayedMentions(comment.content)}
                    </div>
                  </div>
                </div>

                <div className="absolute right-2 top-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 hover:bg-gray-200 opacity-70">
                        <MoreVertical className="h-3.5 w-3.5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-36">
                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive flex items-center cursor-pointer"
                        onClick={() => handleDeleteComment(comment.id)}
                      >
                        <Trash2 className="h-3.5 w-3.5 mr-2" />
                        <span>Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <MessageSquare className="h-8 w-8 mb-2 mx-auto opacity-50" />
            <p>No comments yet</p>
            <p className="text-xs mt-1">Be the first to add a comment</p>
          </div>
        )}

        {/* Comment Form */}
        <div className="mt-4 py-4 border-t sticky bottom-0 bg-white">
          <div className="flex gap-2">
            <Avatar className="h-7 w-7 sm:h-8 sm:w-8 shrink-0 self-start mt-2">
              <AvatarFallback>{user?.fullName?.charAt(0) || '?'}</AvatarFallback>
            </Avatar>
            <div className="flex-1 relative">
              <div
                ref={textareaRef}
                className="w-full border rounded-md p-3 min-h-[60px] focus:min-h-[100px] transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                contentEditable
                suppressContentEditableWarning={true}
                style={{
                  whiteSpace: 'pre-wrap',
                  wordWrap: 'break-word',
                  minHeight: '60px',
                }}
                onInput={handleCommentChange}
                onKeyDown={handleKeyDown}
                onFocus={(e) => e.currentTarget.classList.add('min-h-[100px]')}
              />

              {!comment && (
                <div className="absolute top-3 left-3 text-gray-500 pointer-events-none">
                  Add a comment or tag a teammate using @...
                </div>
              )}

              {/* @mention dropdown */}
              {showMentionDropdown && (
                <div
                  className="absolute z-50 w-72 max-h-60 overflow-auto bg-white border border-gray-200 rounded-lg shadow-lg"
                  style={{
                    top: `${dropdownPosition.top}px`,
                    left: `${dropdownPosition.left}px`,
                  }}
                  ref={dropdownRef}
                >
                  {(() => {
                    const filteredMembers = teamMembers.filter((member) =>
                      member.fullName.toLowerCase().includes(mentionFilter.toLowerCase()),
                    );

                    return filteredMembers.length > 0 ? (
                      <div className="py-1">
                        {filteredMembers.map((member: TeamMemberMention, index: number) => (
                          <div
                            key={member.id}
                            className={`flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150 ${
                              selectedMentionIndex === index ? 'bg-gray-100' : ''
                            }`}
                            onClick={() => handleSelectMention(member.id)}
                          >
                            <Avatar className="h-6 w-6 mr-3">
                              <AvatarFallback className="text-xs">
                                {member.fullName.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className="font-medium text-sm text-gray-900">{member?.fullName || ''}</div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-4 text-gray-500 text-center text-sm">
                        No matching team members
                        {mentionFilter && ` for "${mentionFilter}"`}
                      </div>
                    );
                  })()}
                </div>
              )}

              <div className="flex justify-between items-center mt-2">
                <div className="text-xs text-gray-500">
                  <span>Use @username to mention</span>
                </div>
                <Button
                  onClick={handleSubmitComment}
                  disabled={!comment.trim() || isSubmittingComment}
                  aria-label="Post comment"
                >
                  <span className="hidden sm:inline mr-1">{isSubmittingComment ? 'Posting...' : 'Post'}</span>
                  <Send className="h-4 w-4" />
                </Button>
              </div>

              {comment.length > 0 && (
                <div className="absolute bottom-[52px] right-3 text-xs text-gray-500">{charCount} chars left</div>
              )}
            </div>
          </div>
        </div>
      </CardContent>

      {/* Delete Comment Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Comment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this comment? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteComment} disabled={isDeletingComment}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteComment}
              disabled={isDeletingComment}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeletingComment ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};
