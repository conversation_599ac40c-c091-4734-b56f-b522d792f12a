import {
  AlertDialog,
  AlertDialogT<PERSON>le,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogDescription,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog';
import { useAnalytics } from '@/hooks/use-analytics';
import { trpc } from '@/providers/trpc';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { accessPointStatusEnum } from '@shared/schema';
import { toast } from 'sonner';

export const ArchiveConfirmationDialog = ({
  archived,
  showDialog,
  setShowDialog,
  entityId,
  entityType,
}: {
  archived: boolean;
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
  entityId: string;
  entityType: 'event' | 'capa' | 'accessPoint';
}) => {
  const utils = trpc.useUtils();

  const entityLabel = entityType === 'event' ? 'Safety Event' : entityType === 'capa' ? 'CAPA' : 'Access Point';
  const analytics = useAnalytics();
  const trackArchive = (action: boolean) => {
    const eventKey =
      entityType === 'capa'
        ? ANALYTICS_EVENTS.CAPA.ARCHIVED
        : entityType === 'event'
          ? ANALYTICS_EVENTS.EVENT.ARCHIVED
          : ANALYTICS_EVENTS.ACCESS_POINT.ARCHIVED;
    analytics.track(eventKey, {
      [`${entityType === 'accessPoint' ? 'access_point' : entityType}_id`]: entityId,
      action: action ? 'unarchived' : 'archived',
    });
  };

  const mutationMap = {
    event: trpc.incident.update.useMutation({
      onSuccess: () => {
        utils.incident.list.invalidate();
        utils.incident.getById.invalidate({ id: entityId });
        trackArchive(archived);
        toast(archived ? `${entityLabel} Unarchived` : `${entityLabel} Archived`, {
          description: archived
            ? `${entityLabel} has been restored to its previous status.`
            : `${entityLabel} has been archived.`,
        });
      },
      onError: () => {
        toast('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${entityLabel}. Please try again.`,
        });
      },
    }),
    capa: trpc.capa.update.useMutation({
      onSuccess: () => {
        utils.capa.list.invalidate();
        utils.capa.getById.invalidate({ id: entityId });
        trackArchive(archived);
        toast(archived ? `${entityLabel} Unarchived` : `${entityLabel} Archived`, {
          description: archived
            ? `${entityLabel} has been restored to its previous status.`
            : `${entityLabel} has been archived.`,
        });
      },
      onError: () => {
        toast('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${entityLabel}. Please try again.`,
        });
      },
    }),
    accessPoint: trpc.accessPoint.update.useMutation({
      onMutate(variables) {
        if (entityType === 'accessPoint' && archived) {
          variables.status = accessPointStatusEnum.enumValues[1];
        }
      },
      onSuccess: () => {
        utils.accessPoint.list.invalidate();
        utils.accessPoint.getById.invalidate({ id: entityId });
        trackArchive(archived);
        toast(archived ? `${entityLabel} Unarchived` : `${entityLabel} Archived`, {
          description: archived
            ? `${entityLabel} has been restored to its previous status.`
            : `${entityLabel} has been archived.`,
        });
      },
      onError: () => {
        toast('Action Failed', {
          description: `Could not ${archived ? 'unarchive' : 'archive'} the ${entityLabel}. Please try again.`,
        });
      },
    }),
  };

  const onArchive = async () => {
    const mutation = mutationMap[entityType];

    await mutation.mutateAsync({
      id: entityId,
      archived: !archived,
    });

    setShowDialog(false);
  };

  return (
    <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{archived ? 'Unarchive' : 'Archive'}</AlertDialogTitle>
          <AlertDialogDescription>
            {archived
              ? `This will restore the ${entityLabel} to the active list with its previous status. Unarchived ${entityLabel}s will appear in the default ${entityLabel} log view.`
              : `This will remove the ${entityLabel} from the active list while preserving its current status. Archived ${entityLabel}s can be viewed by selecting the 'Include Archived' filter in the ${entityLabel} log.`}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onArchive} className={archived ? '' : 'bg-red-600 hover:bg-red-700'}>
            {archived ? 'Unarchive' : 'Archive'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
