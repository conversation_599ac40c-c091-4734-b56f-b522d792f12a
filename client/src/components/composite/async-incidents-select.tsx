import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useInfiniteMinimalIncidents } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { PopoverAnchor, PopoverPortal } from '@radix-ui/react-popover';
import { RouterOutputs } from '@shared/router.types';
import { Event, STATUS_MAP } from '@shared/schema.types';
import { useDebounce } from '@uidotdev/usehooks';
import { Check, ChevronsUpDown, Loader2, Search } from 'lucide-react';
import * as React from 'react';

interface AsyncIncidentSelectProps {
  value?: Event['id'] | null;
  onChange: (selected: Event['id'] | undefined) => void;
  className?: string;
  placeholder?: string;
  mustIncludeObjectIds?: string[];
}

export const AsyncIncidentsSelect = ({
  onChange,
  className,
  value: selected = null,
  placeholder = 'Select incident...',
  mustIncludeObjectIds = [],
  ...props
}: AsyncIncidentSelectProps) => {
  const [open, setOpen] = React.useState(false);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);
  const [search, setSearch] = React.useState('');

  const debouncedSearch = useDebounce(search, 300);

  const {
    data: incidents,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteMinimalIncidents({
    search: debouncedSearch,
    filters: {
      includeArchived: false,
      status: [],
      type: [],
      severity: [],
      oshaReportable: false,
      locationIds: [],
    },
    enabled: true,
    mustIncludeObjectIds,
  });

  const options = React.useMemo(() => {
    return incidents.reduce(
      (acc, incident) => {
        acc[incident.id] = incident;
        return acc;
      },
      {} as Record<Event['id'], RouterOutputs['incident']['minimalList']['result'][number]>,
    );
  }, [incidents]);

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (document.activeElement === inputRef.current && e.key === 'Backspace' && onChange) {
        onChange(undefined);
      }

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onChange, selected]);

  // Handle infinite scrolling
  const isFetchingRef = React.useRef(false);

  React.useEffect(() => {
    // Only set up scroll listener when popover is open
    if (!open) return;

    // Add a small delay to ensure the popover content is rendered
    const setupTimeoutId = setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea || !hasNextPage || !fetchNextPage) return;

      let scrollTimeoutId: NodeJS.Timeout;

      const handleScroll = () => {
        // Clear any existing timeout
        clearTimeout(scrollTimeoutId);

        // Debounce the scroll event
        scrollTimeoutId = setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
          // Trigger fetchNextPage when user scrolls to within 50px of the bottom
          if (distanceFromBottom < 50 && !isFetchingNextPage && !isFetchingRef.current) {
            isFetchingRef.current = true;
            try {
              const result = fetchNextPage();
              // Check if result is a promise-like object
              if (result && typeof result === 'object' && 'then' in result) {
                (result as Promise<unknown>).finally(() => {
                  isFetchingRef.current = false;
                });
              } else {
                // If fetchNextPage doesn't return a promise, reset immediately
                isFetchingRef.current = false;
              }
            } catch (error) {
              console.error('Error calling fetchNextPage:', error);
              isFetchingRef.current = false;
            }
          }
        }, 100); // 100ms debounce
      };

      scrollArea.addEventListener('scroll', handleScroll);

      // Cleanup function for the scroll listener
      return () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100); // Small delay to ensure DOM is ready

    return () => {
      clearTimeout(setupTimeoutId);
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange: React.ChangeEventHandler<HTMLInputElement> = (event) => {
    setSearch(event.target.value);
  };

  const onSelect = (incidentId: Event['id']) => {
    if (!onChange) return;
    onChange(incidentId);
    setOpen(false);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  const renderSelectedValue = () => {
    if (isLoading) {
      return <span className="text-muted-foreground text-base md:text-sm">Loading...</span>;
    }

    if (selected && options[selected]) {
      const incident = options[selected];

      return (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            {incident.slug} — {incident.title}
          </span>
          <Badge variant="secondary" className="text-xs">
            {STATUS_MAP[incident.status]}
          </Badge>
          <span className="text-xs text-muted-foreground">({formatDate(incident.reportedAt)})</span>
        </div>
      );
    }

    return <span className="text-muted-foreground">{placeholder}</span>;
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild className={className}>
        <div
          {...props}
          aria-expanded={open}
          className={cn(
            'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            'h-9 cursor-pointer',
          )}
          onClick={() => setOpen(!open)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">{renderSelectedValue()}</div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {isLoading && <Loader2 className="animate-spin size-4" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverAnchor />
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search incidents..."
                onChange={handleSearchChange}
                ref={inputRef}
              />
            </div>
            <Separator />
            <div ref={scrollAreaRef} className="p-2 overflow-auto" style={{ height: 200 }}>
              <ul>
                {Object.keys(options).length === 0 && !isLoading && (
                  <li className="p-2 text-sm text-muted-foreground text-center">No incidents found</li>
                )}
                {Object.values(options).map((incident) => (
                  <li
                    onClick={() => onSelect(incident.id)}
                    className="flex cursor-pointer items-center rounded-md p-2 text-sm hover:bg-secondary/80 gap-3"
                    key={incident.id}
                  >
                    <Check
                      className={cn('size-4 flex-shrink-0', selected === incident.id ? 'opacity-100' : 'opacity-0')}
                    />
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <span className="font-medium">
                        {incident.slug} — {incident.title}
                      </span>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {incident.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">({formatDate(incident.reportedAt)})</span>
                      </div>
                    </div>
                  </li>
                ))}
                {isFetchingNextPage && (
                  <li className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
