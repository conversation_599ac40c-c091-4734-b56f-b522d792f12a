import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteUsersPublic } from '@/hooks/use-paginated-data';
import { cn } from '@/lib/utils';
import { ChevronDown, Loader2, Search } from 'lucide-react';
import * as React from 'react';

export const AsyncUsersFilter = ({
  selected,
  onSelect,
  className,
  label,
  placeholder,
}: {
  selected?: string[];
  onSelect: (userIds: string[]) => void;
  label?: string;
  placeholder?: string;
  className?: string;
}) => {
  const { user } = useAppContext();
  const [open, setOpen] = React.useState(false);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [search, setSearch] = React.useState('');

  const {
    data: users,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteUsersPublic({
    upkeepCompanyId: user?.upkeepCompanyId!,
    search,
    enabled: open && !!user?.upkeepCompanyId,
  });

  // Handle infinite scrolling
  const isFetchingRef = React.useRef(false);

  React.useEffect(() => {
    // Only set up scroll listener when dropdown is open
    if (!open) return;

    // Add a small delay to ensure the dropdown content is rendered
    const setupTimeoutId = setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea || !hasNextPage || !fetchNextPage) return;

      let scrollTimeoutId: NodeJS.Timeout;

      const handleScroll = () => {
        // Clear any existing timeout
        clearTimeout(scrollTimeoutId);

        // Debounce the scroll event
        scrollTimeoutId = setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
          // Trigger fetchNextPage when user scrolls to within 50px of the bottom
          if (distanceFromBottom < 50 && !isFetchingNextPage && !isFetchingRef.current) {
            isFetchingRef.current = true;
            try {
              const result = fetchNextPage();
              // Check if result is a promise-like object
              if (result && typeof result === 'object' && 'then' in result) {
                (result as Promise<unknown>).finally(() => {
                  isFetchingRef.current = false;
                });
              } else {
                // If fetchNextPage doesn't return a promise, reset immediately
                isFetchingRef.current = false;
              }
            } catch (error) {
              console.error('Error calling fetchNextPage:', error);
              isFetchingRef.current = false;
            }
          }
        }, 100); // 100ms debounce
      };

      scrollArea.addEventListener('scroll', handleScroll);

      // Cleanup function for the scroll listener
      return () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100); // Small delay to ensure DOM is ready

    return () => {
      clearTimeout(setupTimeoutId);
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  // Focus input when dropdown opens
  React.useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={cn('justify-between', className)}>
          <div className="flex items-center">
            {label && <span className="text-sm font-medium mr-2">{label}</span>}
            <Badge className="px-1 py-0 h-5" variant="secondary">
              {selected?.length || 'All'}
            </Badge>
          </div>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="p-0 min-w-16">
        {/* Search Input */}
        <div className="flex items-center pl-4">
          <Search size={15} />
          <Input
            ref={inputRef}
            className="w-full border-none px-2 py-1 bg-transparent shadow-none outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-0 focus-visible:ring-offset-0"
            value={search}
            placeholder={placeholder || 'Search users...'}
            onChange={handleSearchChange}
          />
        </div>
        <Separator />

        {/* Scrollable User List */}
        <div ref={scrollAreaRef} className="max-h-64 overflow-auto p-1">
          {isLoading && (
            <div className="flex items-center justify-center p-2">
              <Loader2 className="animate-spin size-4" />
              <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
            </div>
          )}

          {users.length === 0 && !isLoading && (
            <div className="p-2 text-sm text-muted-foreground text-center">No users found</div>
          )}

          {users.map((user) => (
            <DropdownMenuCheckboxItem
              key={user.id}
              className="flex items-center space-x-2"
              onSelect={(e) => {
                e.preventDefault();
                if (!selected) {
                  onSelect([user.id]);
                  return;
                }

                const newSelected = selected.includes(user.id);
                if (newSelected) {
                  onSelect(selected.filter((id) => id !== user.id));
                } else {
                  onSelect([...selected, user.id]);
                }
              }}
              checked={selected?.includes(user.id)}
            >
              <label className="capitalize cursor-pointer">{user.fullName}</label>
            </DropdownMenuCheckboxItem>
          ))}

          {isFetchingNextPage && (
            <div className="flex items-center justify-center p-2">
              <Loader2 className="animate-spin size-4" />
              <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
