import { Badge } from '@/components/ui/badge';
import { AlertCircle, Flag } from 'lucide-react';
import { severityEnum } from '@shared/schema';

export const SeverityBadge = ({
  severity,
}: {
  severity: (typeof severityEnum.enumValues)[number];
}) => {
  const severityMap = {
    critical: 'Critical',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
  };

  const severityColorMap = {
    critical: 'bg-red-50 text-red-700 border-red-200',
    high: 'bg-orange-50 text-orange-700 border-orange-200',
    medium: 'bg-yellow-50 text-yellow-700 border-yellow-200',
    low: 'bg-green-50 text-green-700 border-green-200',
  };

  const severityIconMap = {
    critical: <AlertCircle className="h-3 w-3 mr-1" />,
    high: <Flag className="h-3 w-3 mr-1" />,
    medium: null,
    low: null,
  };

  return (
    <Badge
      className={`${severityColorMap[severity]} font-medium border px-2 py-1 capitalize`}
      variant="outline"
    >
      {severityIconMap[severity]}
      {severityMap[severity]}
    </Badge>
  );
};
