import { Badge } from '@/components/ui/badge';
import { RouterOutputs } from '@shared/router.types';
import { OSHA_STATUS_MAP, OshaStatusSchema } from '@shared/schema.types';
import { z } from 'zod';

export const OshaStatusBadge = ({
  oshaReport,
}: {
  oshaReport: RouterOutputs['oshaReport']['list']['result'][number];
}) => {
  let status: z.infer<typeof OshaStatusSchema> = OshaStatusSchema.enum.draft;
  if (oshaReport.preparedAt) {
    status = OshaStatusSchema.enum.submitted;
  } else if (oshaReport.preparedAt && oshaReport.archivedAt) {
    status = OshaStatusSchema.enum.archived;
  }

  const statusColorMap = {
    [OshaStatusSchema.enum.draft]: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
    [OshaStatusSchema.enum.submitted]: 'bg-green-100 text-green-800 hover:bg-green-200',
    [OshaStatusSchema.enum.archived]: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
  };

  return <Badge className={statusColorMap[status]}>{OSHA_STATUS_MAP[status]}</Badge>;
};
