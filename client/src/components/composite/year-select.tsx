import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { generateYears } from '@/lib/generate-years';

export const YearSelect = ({ value, onChange }: { value?: number; onChange: (value: number) => void }) => {
  return (
    <Select value={value?.toString()} onValueChange={(value) => onChange(parseInt(value))}>
      <SelectTrigger>
        <SelectValue placeholder="Select a year" />
      </SelectTrigger>
      <SelectContent>
        {generateYears().map((year) => (
          <SelectItem key={year} value={year.toString()}>
            {year}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
