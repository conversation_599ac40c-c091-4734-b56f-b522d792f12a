import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { ClipboardCheck, FileText, Plus } from 'lucide-react';
import { useLocation } from 'wouter';

interface CapasEmptyProps {
  hasActiveFilters: boolean;
  onResetFilters: () => void;
}

export const CapasEmpty = ({ hasActiveFilters, onResetFilters }: CapasEmptyProps) => {
  const isMobile = useIsMobile();
  const [_, navigate] = useLocation();
  const { hasPermission } = usePermissions();
  if (hasActiveFilters) {
    // Empty state when filters are applied but no results
    return (
      <div
        className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
      >
        <div className="text-center max-w-md">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
            <FileText className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>No CAPAs found</h3>
          <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm' : 'text-base'}`}>
            No corrective or preventive actions match your current filters. Try adjusting your search criteria or clear
            filters to see all CAPAs.
          </p>
          <Button variant="outline" onClick={onResetFilters} className={isMobile ? 'w-full' : ''}>
            Clear all filters
          </Button>
        </div>
      </div>
    );
  }

  // Empty state when no CAPAs exist at all
  return (
    <div
      className={`flex flex-col items-center justify-center py-12 px-4 ${isMobile ? 'min-h-[400px]' : 'min-h-[500px]'}`}
    >
      <div className="text-center max-w-md">
        <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center">
          <ClipboardCheck className="h-8 w-8 text-blue-500" />
        </div>
        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-lg' : 'text-xl'}`}>No CAPAs created yet</h3>
        <p className={`text-gray-500 mb-6 ${isMobile ? 'text-sm leading-5' : 'text-base'}`}>
          This is where all corrective and preventive actions will be tracked and managed. When CAPAs are created,
          they'll appear here for review and follow-up.
        </p>
        <div className="space-y-3">
          {hasPermission(MODULES.EHS_CAPA, ALLOWED_ACTIONS.CREATE) && (
            <Button className={`${isMobile ? 'w-full' : ''}`} onClick={() => navigate(ROUTES.CAPA_NEW)}>
              <Plus className="h-4 w-4 mr-2" />
              Create New CAPA
            </Button>
          )}
          {isMobile && (
            <p className="text-xs text-gray-400 mt-3">
              CAPAs can be created to address identified issues or potential risks.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
