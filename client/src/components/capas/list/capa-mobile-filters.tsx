import { AsyncUsersFilter } from '@/components/composite/async-users-filter';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { capaPriorityEnum, capaTypeEnum, statusEnum } from '@shared/schema';
import { CAPA_PRIORITY_MAP, CAPA_TYPE_MAP, CapasFilters, STATUS_MAP } from '@shared/schema.types';
import { Archive, ChevronDown, Filter } from 'lucide-react';

export const CapaMobileFilters = ({
  filters,
  toggleFilter,
  activeFilterCount,
  resetFilters,
  setFilters,
  trackFilterApplied,
}: {
  filters: CapasFilters;
  toggleFilter: (type: 'status' | 'type' | 'priority' | 'owner', value: string) => void;
  activeFilterCount: number;
  resetFilters: () => void;
  setFilters: React.Dispatch<React.SetStateAction<CapasFilters>>;
  trackFilterApplied: (
    type: 'status' | 'type' | 'priority' | 'owner' | 'tags' | 'dueDateRange' | 'includeArchived',
    value: string,
  ) => void;
}) => {
  return (
    <div className="md:hidden">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" className="w-full">
            <Filter className="h-4 w-4" />
            {activeFilterCount > 0 && (
              <span className="ml-2 text-xs bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Filter CAPAs</SheetTitle>
            <SheetDescription>Apply filters to narrow down the CAPA list.</SheetDescription>
          </SheetHeader>

          <div className="space-y-4 p-4">
            {/* Status Collapsible Section */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Status</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {statusEnum.enumValues.map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}-mobile`}
                      checked={filters.status?.includes(status)}
                      onCheckedChange={() => {
                        toggleFilter('status', status);
                        trackFilterApplied('status', status);
                      }}
                    />
                    <label htmlFor={`status-${status}-mobile`} className="text-sm cursor-pointer">
                      {STATUS_MAP[status]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Type Collapsible Section */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Type</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {capaTypeEnum.enumValues.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type}-mobile`}
                      checked={filters.type?.includes(type)}
                      onCheckedChange={() => {
                        toggleFilter('type', type);
                        trackFilterApplied('type', type);
                      }}
                    />
                    <label htmlFor={`type-${type}-mobile`} className="text-sm cursor-pointer">
                      {CAPA_TYPE_MAP[type]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Priority Collapsible Section */}
            <Collapsible defaultOpen className="bg-muted rounded-md p-3">
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h3 className="text-sm font-medium">Priority</h3>
                <ChevronDown className="h-4 w-4 transition-transform ui-open:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2 space-y-2">
                {capaPriorityEnum.enumValues.map((priority) => (
                  <div key={priority} className="flex items-center space-x-2">
                    <Checkbox
                      id={`priority-${priority}-mobile`}
                      checked={filters.priority?.includes(priority)}
                      onCheckedChange={() => {
                        toggleFilter('priority', priority);
                        trackFilterApplied('priority', priority);
                      }}
                    />
                    <label htmlFor={`priority-${priority}-mobile`} className="text-sm cursor-pointer">
                      {CAPA_PRIORITY_MAP[priority]}
                    </label>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>

            {/* Owner Collapsible Section */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Owner</Label>
              <AsyncUsersFilter
                selected={filters.owner}
                onSelect={(owner) => {
                  setFilters((prev) => ({ ...prev, owner }));
                  trackFilterApplied('owner', owner.join(','));
                }}
                label=""
                placeholder="Select owner"
                className="w-full"
              />
            </div>

            {/* Due Date Filter */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Due Date Range</Label>
              <DateRangePicker
                placeholder="Select due date range"
                range={{
                  from: filters.dueDateRange?.from,
                  to: filters.dueDateRange?.to,
                }}
                setRange={(range) => {
                  setFilters((prev) => ({
                    ...prev,
                    dueDateRange: range,
                  }));
                  trackFilterApplied(
                    'dueDateRange',
                    range?.from?.toISOString() + ' - ' + range?.to?.toISOString() || '',
                  );
                }}
                className="w-full"
              />
            </div>

            {/* Archive Status */}
            <div className="flex flex-col">
              <h3 className="font-medium mb-2">Archive Status</h3>
              <Button
                variant={filters.includeArchived ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setFilters((prev) => ({
                    ...prev,
                    includeArchived: !prev.includeArchived,
                  }));
                  trackFilterApplied('includeArchived', `${filters.includeArchived}`);
                }}
                className={`justify-start ${filters.includeArchived ? 'bg-amber-600 hover:bg-amber-700' : ''}`}
              >
                <Archive className="h-4 w-4 mr-2" />
                Include Archived
              </Button>
            </div>
          </div>
          <SheetFooter>
            <SheetClose asChild>
              <Button variant="outline" onClick={resetFilters} className="w-full sm:w-auto">
                Reset Filters
              </Button>
            </SheetClose>
            <SheetClose asChild>
              <Button type="submit" className="w-full sm:w-auto">
                Apply Filters
              </Button>
            </SheetClose>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};
