'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { PopoverPortal } from '@radix-ui/react-popover';
import { Check, ChevronsUpDown, Loader2, Search, X } from 'lucide-react';
import * as React from 'react';

export type OptionType = Record<'value' | 'label', string>;

interface AsyncSelectProps {
  options?: Record<string, string>;
  value?: string[] | string | null;
  onChange: (selected: string[] | string | undefined) => void;
  onSearch: (term: string) => void;
  search?: string;
  className?: string;
  loading?: boolean;
  placeholder?: string;
  multi?: boolean;
  disabled?: boolean;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => Promise<unknown> | void;
}

export const AsyncDropdown = ({
  options,
  search,
  onSearch,
  onChange,
  className,
  loading,
  multi = true,
  disabled = false,
  value: selected = multi ? [] : undefined,
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  ...props
}: AsyncSelectProps) => {
  const [open, setOpen] = React.useState(false);

  const inputRef = React.useRef<HTMLInputElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);

  const handleUnselect = (item: string) => {
    if (disabled) return;

    if (multi && Array.isArray(selected)) {
      onChange(selected.filter((i) => i !== item));
    } else {
      onChange(undefined);
    }
  };

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;

      if (
        document.activeElement === inputRef.current &&
        e.key === 'Backspace' &&
        Array.isArray(selected) &&
        selected.length > 0 &&
        onChange
      ) {
        onChange(selected.filter((_, index) => index !== selected.length - 1));
      }

      if (e.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onChange, selected, disabled]);

  // Handle infinite scrolling
  const isFetchingRef = React.useRef(false);

  React.useEffect(() => {
    // Only set up scroll listener when popover is open
    if (!open) return;

    // Add a small delay to ensure the popover content is rendered
    const setupTimeoutId = setTimeout(() => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea || !hasNextPage || !fetchNextPage) return;

      let scrollTimeoutId: NodeJS.Timeout;

      const handleScroll = () => {
        // Clear any existing timeout
        clearTimeout(scrollTimeoutId);

        // Debounce the scroll event
        scrollTimeoutId = setTimeout(() => {
          const { scrollTop, scrollHeight, clientHeight } = scrollArea;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
          // Trigger fetchNextPage when user scrolls to within 50px of the bottom
          if (distanceFromBottom < 50 && !isFetchingNextPage && !isFetchingRef.current) {
            isFetchingRef.current = true;
            try {
              const result = fetchNextPage();
              // Check if result is a promise-like object
              if (result && typeof result === 'object' && 'then' in result) {
                (result as Promise<unknown>).finally(() => {
                  isFetchingRef.current = false;
                });
              } else {
                // If fetchNextPage doesn't return a promise, reset immediately
                isFetchingRef.current = false;
              }
            } catch (error) {
              console.error('Error calling fetchNextPage:', error);
              isFetchingRef.current = false;
            }
          }
        }, 100); // 100ms debounce
      };

      scrollArea.addEventListener('scroll', handleScroll);

      // Cleanup function for the scroll listener
      return () => {
        scrollArea.removeEventListener('scroll', handleScroll);
        clearTimeout(scrollTimeoutId);
      };
    }, 100); // Small delay to ensure DOM is ready

    return () => {
      clearTimeout(setupTimeoutId);
    };
  }, [open, hasNextPage, fetchNextPage, isFetchingNextPage]);

  const handleSearchChange: React.ChangeEventHandler<HTMLInputElement> = (event) => {
    if (disabled) return;
    onSearch(event.target.value);
  };

  const onSelect = (option: string) => {
    if (!onChange || disabled) return;
    if (multi && Array.isArray(selected)) {
      const alreadySelected = selected.some((item) => item === option);

      if (alreadySelected) {
        onChange(selected.filter((item) => item !== option));
      } else {
        onChange([...selected, option]);
      }

      setOpen(true);
    } else {
      onChange(option);
      setOpen(false);
    }
  };

  const renderMultiSelectValues = () => {
    if (loading) {
      return <span className="text-muted-foreground text-base md:text-sm">loading...</span>;
    }

    if (Array.isArray(selected) && selected.length > 0 && options) {
      return selected.map((item) => (
        <Badge
          variant="outline"
          key={item}
          className="flex items-center gap-1 pr-1 group-hover:bg-background"
          onClick={() => handleUnselect(item)}
        >
          {options[item]}
          <Button
            variant="ghost"
            size="xs"
            className="border-none"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleUnselect(item);
              }
            }}
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleUnselect(item);
            }}
          >
            <X size={16} className="text-muted-foreground" />
          </Button>
        </Badge>
      ));
    }

    if (typeof selected === 'string' && options) {
      return <span className="text-base md:text-sm">{options[selected]}</span>;
    }

    return <span className="text-muted-foreground text-base md:text-sm">{props.placeholder ?? 'Select ...'}</span>;
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal>
      <PopoverTrigger asChild>
        <div
          {...props}
          aria-expanded={open}
          aria-disabled={disabled}
          className={cn(
            'group flex h-9 w-full items-center justify-between rounded-md border border-input bg-white px-3 py-2 text-base md:text-sm ring-offset-background transition-[color,box-shadow] placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-input/30',
            open && !disabled && 'border-ring ring-[3px] ring-ring/50',
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
            Array.isArray(selected) && selected.length > 1 ? 'h-full' : 'h-9',
            disabled && 'cursor-not-allowed opacity-50',
            className,
          )}
          onClick={() => !disabled && setOpen(!open)}
        >
          <div className="flex items-center gap-2">{renderMultiSelectValues()}</div>

          <div className="flex items-center gap-2">
            {!multi && selected && (
              <X size={16} className="text-muted-foreground" onClick={() => handleUnselect(selected as string)} />
            )}
            {loading && <Loader2 className="animate-spin" />}
            <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverPortal>
        <PopoverContent avoidCollisions className="w-[var(--radix-popover-trigger-width)] p-0">
          <div>
            <div className="flex items-center p-2 pl-4">
              <Search size={15} />
              <input
                size={15}
                className="w-full border-none px-2 py-1 outline-hidden placeholder:text-muted-foreground focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50"
                value={search}
                placeholder="Search..."
                onChange={handleSearchChange}
                disabled={disabled}
                ref={inputRef}
              />
            </div>
            <Separator />
            <div
              ref={scrollAreaRef}
              className="p-2 overflow-auto"
              style={{ height: 150 }}
            >
              <ul>
                {options &&
                  Object.keys(options).map((value) => (
                    <li
                      onClick={() => !disabled && onSelect(value)}
                      className={cn(
                        'flex cursor-pointer items-center rounded-md p-2 text-base md:text-sm hover:bg-secondary/80',
                        disabled && 'cursor-not-allowed opacity-50 hover:bg-transparent',
                      )}
                      key={value}
                    >
                      <Check
                        className={cn(
                          'mr-2 size-4',
                          Array.isArray(selected)
                            ? selected.some((item) => item === value)
                              ? 'opacity-100'
                              : 'opacity-0'
                            : selected === value
                              ? 'opacity-100'
                              : 'opacity-0',
                        )}
                      />
                      {options[value]}
                    </li>
                  ))}
                {isFetchingNextPage && (
                  <li className="flex items-center justify-center p-2">
                    <Loader2 className="animate-spin size-4" />
                    <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </PopoverContent>
      </PopoverPortal>
    </Popover>
  );
};
