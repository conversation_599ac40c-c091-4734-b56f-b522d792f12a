ALTER TYPE "public"."audit_trail_entity_type" ADD VALUE 'global_location';--> statement-breakpoint
CREATE TABLE "global_locations" (
	"id" varchar(24) PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"upkeep_company_id" varchar(10) NOT NULL,
	"created_by" varchar(10) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp,
	"archived_at" timestamp
);
--> statement-breakpoint
CREATE INDEX "global_locations_upkeep_company_id_index" ON "global_locations" USING btree ("upkeep_company_id");--> statement-breakpoint
CREATE INDEX "global_locations_name_upkeep_company_id_index" ON "global_locations" USING btree ("name","upkeep_company_id");