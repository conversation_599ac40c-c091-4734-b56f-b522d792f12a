import { createId, getConstants } from '@paralleldrive/cuid2';
import { boolean, index, integer, pgEnum, pgTable, real, text, timestamp, unique, varchar } from 'drizzle-orm/pg-core';

// Enum for events report classification
// Incident is an event type
export const reportTypeEnum = pgEnum('report_type', ['incident', 'near_miss', 'observation']);

// Enum for classifying severity levels of events and CAPAs
export const severityEnum = pgEnum('severity_level', ['low', 'medium', 'high', 'critical']);

// Enum for categorizing the root cause of events
export const rootCauseEnum = pgEnum('root_cause', [
  'human_error', // Errors made by staff
  'equipment_failure', // Failures of machinery or tools
  'environmental', // Environmental factors
  'procedural', // Issues with procedures or protocols
  'other', // Other uncategorized causes
]);

// Enum for tracking the status of events and CAPAs
export const statusEnum = pgEnum('status', ['open', 'in_review', 'closed']);

// Enum for user role types
export const roleEnum = pgEnum('role', ['admin', 'technician']);

// Enum for categorizing events types
export const eventCategoryEnum = pgEnum('event_category', [
  'chemical', // Chemical spills or exposures
  'electrical', // Electrical hazards
  'ergonomic', // Ergonomic issues
  'fall', // Falls or slips
  'fire', // Fire events
  'mechanical', // Mechanical hazards
  'radiation', // Radiation exposure
  'spill', // Spills of non-chemical materials
  'transportation', // Vehicle accidents
  'violence', // Workplace violence
  'other', // Other uncategorized causes
]);

// Enum for file status
export const fileStatusEnum = pgEnum('file_status', [
  'pending', // URL generated but upload not complete
  'completed', // Upload finished successfully
  'failed', // Upload failed
  'expired', // Presigned URL has expired
]);

// Enum for QR access point status
export const accessPointStatusEnum = pgEnum('access_point_status', [
  'active', // QR code is active and can be scanned
  'inactive', // QR code is inactive and cannot be scanned
]);

// Incident records for safety reporting
export const events = pgTable(
  'events',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }) // Associated upkeep company Id
      .notNull(),
    slug: text('slug'), // Human-readable identifier (SE-0001)
    title: varchar('title', { length: 255 }).notNull(), // Incident title/summary
    description: text('description'), // Detailed event description
    reportedAt: timestamp('reported_at').notNull().defaultNow(), // When event was reported
    reportedBy: varchar('reported_by', { length: 10 }), // upkeep user id Who reported the event
    reportedByName: varchar('reported_by_name', { length: 255 }), // Name of the user who reported the event
    reportedByEmail: varchar('reported_by_email', { length: 255 }), // Email of the user who reported the event
    type: reportTypeEnum('type').notNull(), // Incident or near miss
    locationId: varchar('location_id', { length: 10 }), // Where event occurred (ref to UpKeep location)
    assetIds: varchar('asset_ids', { length: 10 }).array(), // Equipment involved (ref to UpKeep assets)
    category: eventCategoryEnum('category').notNull(), // Type of hazard
    severity: severityEnum('severity').notNull(), // Incident severity rating
    status: statusEnum('status').notNull(), // Current status of event
    archived: boolean('archived').default(false), // Whether event is archived
    immediateActions: text('immediate_actions'), // Actions taken immediately after event
    oshaReportable: boolean('osha_reportable').default(false), // Whether event is OSHA reportable
    aiConfidenceScore: real('ai_confidence_score'), // AI analysis confidence level
    teamMembersToNotify: varchar('team_members_to_notify', { length: 10 }).array(), // Team members to notify about the incident
    updatedAt: timestamp('updated_at'), // Last update timestamp
    deletedAt: timestamp('deleted_at'), // Soft deletion timestamp
  },
  (events) => [
    index('event_title_index').on(events.title), // Index for title searches
    index('event_slug_index').on(events.slug), // Index for slug lookups
    index('event_reported_by_index').on(events.reportedBy), // Index for user-based queries
    index('event_type_index').on(events.type), // Index for type-based queries
    index('event_archived_index').on(events.archived), // Index for filtering archived events
    index('event_deleted_at_index').on(events.deletedAt), // Index for soft deletion queries
  ],
);

// Junction table linking events to users involved
export const eventsUsers = pgTable('events_users', {
  id: varchar('id', { length: getConstants().defaultLength })
    .$defaultFn(() => createId())
    .primaryKey(),
  eventId: varchar('event_id', { length: getConstants().defaultLength }) // Referenced event
    .notNull()
    .references(() => events.id),
  userId: varchar('user_id', { length: 10 }) // User involved in event
    .notNull(),
});

// Enum for CAPA types
export const capaTypeEnum = pgEnum('capa_type', ['corrective', 'preventive', 'both']);
// Enum for CAPA priority levels
export const capaPriorityEnum = pgEnum('capa_priority', ['low', 'medium', 'high']);

// Enum for RCA methods
export const rcaMethodEnum = pgEnum('rca_method', ['5_whys', 'fishbone', 'fault_tree', 'other', 'not_selected']);

// Enum for CAPA tags
export const capaTagsEnum = pgEnum('capa_tags', [
  'training',
  'policy',
  'hazard',
  'equipment',
  'procedure',
  'personnel',
]);

export const capaEffectivenessStatusEnum = pgEnum('capa_effectiveness_status', [
  'effective',
  'partial',
  'not_effective',
  'not_evaluated',
]);

// Corrective and Preventive Actions for addressing events
export const capas = pgTable(
  'capas',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
    slug: text('slug'),
    title: varchar('title', { length: 255 }).notNull(),
    summary: text('summary'),
    type: capaTypeEnum('type').notNull(),
    locationId: varchar('location_id', { length: 10 }),
    assetId: varchar('asset_id', { length: 10 }),
    rcaMethod: rcaMethodEnum('rca_method').default('not_selected'),
    rcaFindings: text('rca_findings').notNull(),
    rootCause: rootCauseEnum('root_cause'),
    otherRootCause: text('other_root_cause'),
    aiSuggestedAction: text('ai_suggested_action'), // AI-generated action suggestion
    aiConfidenceScore: real('ai_confidence_score'), // AI analysis confidence level
    actionsToAddress: text('actions_to_address'),
    ownerId: varchar('owner_id', { length: 10 }) // User responsible for the CAPA
      .notNull(),
    dueDate: timestamp('due_date'),
    priority: capaPriorityEnum('priority').notNull().default('medium'),
    tags: text('tags').array(),
    privateToAdmins: boolean('private_to_admins').default(false),
    status: statusEnum('status').notNull().default('open'),
    teamMembersToNotify: varchar('team_members_to_notify', { length: 10 }).array(), // Team members to notify about the CAPA
    archived: boolean('archived').default(false),
    eventId: varchar('event_id', { length: getConstants().defaultLength }).references(() => events.id),
    createdBy: varchar('created_by', { length: 10 }) // User who created the CAPA
      .notNull(),
    triggerWorkOrder: boolean('trigger_work_order').default(false),
    actionsImplemented: text('actions_implemented'),
    implementationDate: timestamp('implementation_date'),
    implementedBy: varchar('implemented_by', { length: 10 }),
    voeDueDate: timestamp('voe_due_date'),
    verificationFindings: text('verification_findings'),
    voePerformedBy: varchar('voe_performed_by', { length: 10 }),
    voeDate: timestamp('voe_date'),
    effectivenessStatus: capaEffectivenessStatusEnum('effectiveness_status').default('not_evaluated'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at'),
    deletedAt: timestamp('deleted_at'),
  },
  (capas) => [
    index('capa_slug_index').on(capas.slug),
    index('capa_created_by_index').on(capas.createdBy),
    index('capa_archived_index').on(capas.archived),
  ],
);

// Comments for events and CAPAs
export const comments = pgTable(
  'comments',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }) // Associated upkeep company Id
      .notNull(),
    eventId: varchar('event_id', {
      // Referenced event (if comment is on event)
      length: getConstants().defaultLength,
    }).references(() => events.id),
    capaId: varchar('capa_id', {
      // Referenced CAPA (if comment is on CAPA)
      length: getConstants().defaultLength,
    }).references(() => capas.id),
    content: text('content').notNull(), // Comment text content
    createdAt: timestamp('created_at').notNull().defaultNow(), // Comment creation timestamp
    updatedAt: timestamp('updated_at'), // Last edit timestamp
    userId: varchar('user_id', {
      // User who made the comment
      length: 10,
    }).notNull(),
  },
  (comments) => [index('comment_created_at_index').on(comments.createdAt)], // Index for chronological ordering
);

// User mentions in comments
export const commentMentions = pgTable(
  'comment_mentions',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    commentId: varchar('comment_id', { length: getConstants().defaultLength }) // Referenced comment
      .notNull()
      .references(() => comments.id),
    userId: varchar('user_id', { length: 10 }) // User being mentioned
      .notNull(),
    position: integer('position'), // Position of mention in comment text
    mentionText: varchar('mention_text', { length: 100 }), // Text of the mention (e.g., @username)
    createdAt: timestamp('created_at').notNull().defaultNow(), // Record creation timestamp
  },
  (commentMentions) => [
    index('comment_mention_comment_id_index').on(commentMentions.commentId), // Index for comment-based queries
    index('comment_mention_user_id_index').on(commentMentions.userId), // Index for user-based queries
  ],
);

// Enum for audit trail action types
export const auditTrailActionEnum = pgEnum('audit_trail_action', [
  'created', // New entity created
  'updated', // Entity updated
  'deleted', // Entity deleted
  'commented', // Comment added
  'submitted', // Entity submitted (e.g., to OSHA)
  'closed', // Entity marked as closed
  'in_review', // Entity marked as in review
  'reopened', // Entity reopened
  'archived', // Entity archived
  'unarchived', // Entity unarchived
]);

export const auditTrailEntityTypeEnum = pgEnum('audit_trail_entity_type', [
  'event',
  'capa',
  'access_point',
  'global_location',
]);

// Audit trail for tracking all system actions
export const auditTrail = pgTable('audit_trail', {
  id: varchar('id', { length: getConstants().defaultLength })
    .$defaultFn(() => createId())
    .primaryKey(),
  upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }) // Associated upkeep company Id
    .notNull(),
  entityType: auditTrailEntityTypeEnum('entity_type').notNull(), // Type of entity (event, CAPA)
  entityId: varchar('entity_id', {
    // ID of the affected entity
    length: getConstants().defaultLength,
  }).notNull(),
  action: auditTrailActionEnum('action').notNull(), // Type of action performed
  timestamp: timestamp('timestamp').notNull().defaultNow(), // When action occurred
  details: text('details'), // Additional action details
  userId: varchar('user_id', {
    // User who performed the action
    length: 10,
  }),
});

// Files for managing S3 presigned URLs and file uploads
export const files = pgTable(
  'files',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
    fileName: varchar('file_name', { length: 255 }).notNull(),
    fileSize: integer('file_size').notNull(),
    mimeType: varchar('mime_type', { length: 127 }).notNull(),
    presignedUrl: text('presigned_url').notNull(),
    s3Key: text('s3_key').notNull(),
    s3Bucket: varchar('s3_bucket', { length: 255 }).notNull(),
    status: fileStatusEnum('status').notNull().default('pending'),
    entityType: varchar('entity_type', { length: 255 }), // Type of entity this file belongs to
    entityId: varchar('entity_id', { length: getConstants().defaultLength }), // ID of the related entity
    uploadedBy: varchar('uploaded_by', { length: 10 }),
    expiresAt: timestamp('expires_at').notNull(), // When presigned URL expires
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at'),
  },
  (files) => [
    index('file_status_index').on(files.status),
    index('file_entity_index').on(files.entityType, files.entityId),
    index('file_uploaded_by_index').on(files.uploadedBy),
  ],
);

// QR code access points for location-based scanning
export const accessPoints = pgTable('access_points', {
  id: varchar('id', { length: getConstants().defaultLength })
    .$defaultFn(() => createId())
    .primaryKey(), // Unique identifier for the access point
  upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(), // Associated upkeep company Id for multi-tenancy
  locationId: varchar('location_id', { length: 10 }).notNull(), // Associated location where access point is placed
  name: varchar('name', { length: 255 }).notNull(), // Display name of the access point
  description: text('description'), // Optional detailed description
  status: accessPointStatusEnum('status').notNull().default('active'), // Current operational status
  createdBy: varchar('created_by', { length: 10 }).notNull(), // User who created the access point
  createdAt: timestamp('created_at').notNull().defaultNow(), // Creation timestamp
  archived: boolean('archived').default(false), // Whether access point is archived
  archivedAt: timestamp('archived_at'), // When access point was archived
  updatedAt: timestamp('updated_at'), // Last update timestamp
  deletedAt: timestamp('deleted_at'), // Soft deletion timestamp
});

// Osha Reports

export const shiftsEnum = pgEnum('shifts', ['day', 'evening', 'night', 'rotating', 'other']);

export const typeOfMedicalCareEnum = pgEnum('type_of_medical_care', [
  'first_aid',
  'medical_treatment',
  'emergency_room',
  'overnight_hospital_stay',
]);

export const oshaTypeEnum = pgEnum('osha_type', [
  'fatality',
  'days_away_from_work',
  'job_restriction',
  'medical_treatment_beyond_first_aid',
  'loss_of_consciousness',
  'significant_injury',
]);

export const oshaReports = pgTable(
  'osha_reports',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
    slug: text('slug'), // Human-readable identifier (OSHA-301-2025-0001)

    employeeName: varchar('employee_name', { length: 255 }),
    employeeWorkLocation: varchar('employee_work_location', { length: 255 }).notNull(), // mandatory
    employeeDepartment: varchar('employee_department', { length: 255 }),
    employeeJobTitle: varchar('employee_job_title', { length: 255 }).notNull(), // mandatory
    employeeDateOfHire: timestamp('employee_date_of_hire'),
    employeeShift: shiftsEnum('employee_shift'),
    employeeSupervisor: varchar('employee_supervisor', { length: 255 }),
    privacyCase: boolean('privacy_case').notNull().default(false),
    reasonForPrivacyCase: text('reason_for_privacy_case'),

    eventId: varchar('event_id', { length: getConstants().defaultLength })
      .references(() => events.id)
      .notNull(),
    reportedByName: varchar('reported_by_name', { length: 255 }),

    bodyPartInjured: varchar('body_part_injured', { length: 255 }).notNull(), // mandatory
    typeOfInjury: varchar('type_of_injury', { length: 255 }).notNull(), // mandatory
    treatmentLocation: varchar('treatment_location', { length: 255 }),
    typeOfMedicalCare: typeOfMedicalCareEnum('type_of_medical_care').notNull(), // mandatory
    prescribedMedication: boolean('prescribed_medication').default(false),
    wasHospitalized: boolean('was_hospitalized').notNull().default(false),
    wasDeceased: boolean('was_deceased').notNull().default(false),
    daysAwayFromWork: integer('days_away_from_work').notNull().default(0),
    daysRestrictedFromWork: integer('days_restricted_from_work').notNull().default(0),

    witnesses: varchar('witnesses', { length: 255 }),
    rootCauseAnalysis: text('root_cause_analysis'),
    correctiveActions: text('corrective_actions'),
    type: oshaTypeEnum('type').notNull(), // mandatory
    reasonForReport: text('reason_for_report'),
    createdBy: varchar('created_by', { length: 10 }).notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at'),
    archived: boolean('archived').default(false),
    archivedAt: timestamp('archived_at'),
    archivedBy: varchar('archived_by', { length: 10 }),
  },
  (table) => [
    index('osha_reports_company_archived_index').on(table.upkeepCompanyId, table.archivedAt, table.id),
    index('osha_reports_company_archived_created_index').on(table.upkeepCompanyId, table.archivedAt, table.createdAt),
    index('osha_reports_company_year_status_index').on(table.upkeepCompanyId, table.archivedAt, table.createdAt),
  ],
);

export const oshaCompanyInformation = pgTable(
  'osha_company_information',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
    year: integer('year').notNull(),

    companyName: varchar('company_name', { length: 255 }).notNull(),
    companyFacilityId: varchar('company_facility_id', { length: 255 }).notNull(),
    companyNAICSCode: integer('company_naics_code').notNull(),
    companyEIN: varchar('company_ein', { length: 9 }).notNull(), // EIN sometimes has leading zeros, better keep varchar
    companyAnnualAverageNumberOfEmployees: integer('company_annual_average_number_of_employees').notNull(),
    companyTotalHoursWorked: integer('company_total_hours_worked').notNull(),

    executiveName: varchar('executive_name', { length: 255 }),
    executiveTitle: varchar('executive_title', { length: 255 }),
    dateCertified: timestamp('date_certified'),
    digitalSignature: text('digital_signature'),

    createdBy: varchar('created_by', { length: 10 }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at'),

    archived: boolean('archived').default(false),
    archivedAt: timestamp('archived_at'),
    archivedBy: varchar('archived_by', { length: 10 }),
  },
  (table) => [
    index('osha_company_information_upkeep_company_id_index').on(table.upkeepCompanyId),
    index('osha_company_information_year_company_index').on(table.year, table.upkeepCompanyId),
    unique('osha_company_information_unique_year_company_index').on(table.year, table.upkeepCompanyId),
  ],
);

export const oshaAgencyReportTypeEnum = pgEnum('osha_agency_report_type', [
  'fatality',
  'amputation',
  'hospitalization',
  'eye_loss',
]);

export const oshaAgencyReport = pgTable('osha_agency_report', {
  id: varchar('id', { length: getConstants().defaultLength })
    .$defaultFn(() => createId())
    .primaryKey(),
  upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
  slug: text('slug'), // Human-readable identifier (SE-2025-0001)
  dateOfIncident: timestamp('date_of_incident').notNull(),
  locationId: varchar('location_id', { length: 10 }).notNull(),
  typeOfIncident: oshaAgencyReportTypeEnum('type_of_incident').notNull(),
  employeesInvolved: varchar('employees_involved', { length: 10 }).array(),
  companyContactPerson: varchar('company_contact_person', { length: 255 }).notNull(),
  contactPersonPhone: varchar('contact_person_phone', { length: 255 }).notNull(),
  createdBy: varchar('created_by', { length: 10 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at'),
});

export const oshaAuditTrailActionEnum = pgEnum('osha_audit_trail_action', [
  'created',
  'updated',
  'submitted',
  'downloaded',
  'signed',
  'archived',
  'restored',
]);

export const oshaEntityTypeEnum = pgEnum('osha_entity_type', [
  'osha_report',
  'osha_company_information',
  'osha_agency_report',
]);

export const oshaAuditTrail = pgTable(
  'osha_audit_trail',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
    entityId: varchar('entity_id', { length: getConstants().defaultLength }).notNull(),
    entityType: oshaEntityTypeEnum('entity_type').notNull(),
    action: oshaAuditTrailActionEnum('action').notNull(),
    ipAddress: varchar('ip_address', { length: 255 }).notNull(),
    userAgent: varchar('user_agent', { length: 255 }).notNull(),
    createdBy: varchar('created_by', { length: 10 }).notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    details: text('details'), // Additional action details
  },
  (table) => [
    index('osha_audit_trail_upkeep_company_id_index').on(table.upkeepCompanyId),
    index('osha_audit_trail_entity_company_index').on(table.entityId, table.upkeepCompanyId, table.entityType),
    index('osha_audit_trail_entity_order_index').on(table.entityId, table.createdAt),
  ],
);

export const globalLocations = pgTable(
  'global_locations',
  {
    id: varchar('id', { length: getConstants().defaultLength })
      .$defaultFn(() => createId())
      .primaryKey(),
    name: varchar('name', { length: 255 }).notNull(),
    upkeepCompanyId: varchar('upkeep_company_id', { length: 10 }).notNull(),
    createdBy: varchar('created_by', { length: 10 }).notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at'),
    archivedAt: timestamp('archived_at'),
  },
  (table) => [
    index('global_locations_upkeep_company_id_index').on(table.upkeepCompanyId),
    index('global_locations_name_upkeep_company_id_index').on(table.name, table.upkeepCompanyId),
  ],
);
