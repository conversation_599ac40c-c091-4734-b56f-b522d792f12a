import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import { globalLocations } from './schema';
import { PaginationInputSchema, SortInputSchema } from './schema.types';

export const CreateGlobalLocationSchema = createInsertSchema(globalLocations).omit({
  id: true,
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
});
export const UpdateGlobalLocationSchema = createUpdateSchema(globalLocations).omit({
  upkeepCompanyId: true,
  createdBy: true,
  createdAt: true,
});

export const GlobalLocationsFiltersSchema = z.object({
  includeArchived: z.boolean().optional(),
  createdBy: z.array(z.string()).optional(),
  createdDateRange: z
    .object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional(),
    })
    .optional(),
});

export type GlobalLocationsFilters = z.infer<typeof GlobalLocationsFiltersSchema>;

export const ListGlobalLocationsSchema = PaginationInputSchema.and(SortInputSchema).and(GlobalLocationsFiltersSchema);

export const SelectGlobalLocationSchema = createSelectSchema(globalLocations);
export const GlobalLocationFiltersSchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  includeDeleted: z.boolean().optional(),
});

export const ArchiveGlobalLocationSchema = z.object({
  id: z.string(),
});

export type CreateGlobalLocation = z.infer<typeof CreateGlobalLocationSchema>;
export type ArchiveGlobalLocation = z.infer<typeof ArchiveGlobalLocationSchema>;
export type UpdateGlobalLocation = z.infer<typeof UpdateGlobalLocationSchema>;
export type SelectGlobalLocation = z.infer<typeof SelectGlobalLocationSchema>;
export type GlobalLocationFilters = z.infer<typeof GlobalLocationFiltersSchema>;
