export const ROUTES = {
  LOGIN: '/web/login',
  UPKEEP_WORK_ORDERS: '/web/work-orders',
  BASE: '/ehs',
  NOT_FOUND: '/ehs/404',
  PUBLIC_BASE: '/ehs/public',
  INCIDENT_NEW: '/ehs/incidents/new',
  INCIDENT_PUBLIC_REPORT: '/ehs/public/incidents/new',
  INCIDENT_PUBLIC_REPORT_SUCCESS: '/ehs/public/incidents/new/success',
  INCIDENT_EDIT: '/ehs/incidents/:id/edit',
  INCIDENT_DETAILS: '/ehs/incidents/:id',
  INCIDENT_LIST: '/ehs/incidents',
  ACCESS_POINTS_LIST: '/ehs/access-points',
  ACCESS_POINTS_NEW: '/ehs/access-points/new',
  GLOBAL_LOCATIONS_LIST: '/ehs/global-locations',
  GLOBAL_LOCATIONS_NEW: '/ehs/global-locations/new',
  CAPA_NEW: '/ehs/capas/new',
  CAPA_EDIT: '/ehs/capas/:id/edit',
  CAPA_DETAILS: '/ehs/capas/:id',
  CAPA_LIST: '/ehs/capas',
  OSHA_REPORTS: '/ehs/osha/reports',
  OSHA_REPORT_NEW: '/ehs/osha/reports/new',
  OSHA_SUMMARY: '/ehs/osha/summary',
  OSHA_AGENCY_REPORTS: '/ehs/osha/agency-reports',
  BUILD_INCIDENT_EDIT_PATH: (id: string) => `/ehs/incidents/${id}/edit`,
  BUILD_INCIDENT_DETAILS_PATH: (id: string) => `/ehs/incidents/${id}`,
  BUILD_CAPA_EDIT_PATH: (id: string) => `/ehs/capas/${id}/edit`,
  BUILD_CAPA_DETAILS_PATH: (id: string) => `/ehs/capas/${id}`,
  BUILD_OSHA_REPORT_DETAILS_PATH: (id: string) => `/ehs/osha/reports/${id}`,
  BUILD_WORK_ORDER_DETAILS_PATH: (id: string) => `/web/work-orders/list?id=${id}&modal=workorder&tab=details`,
} as const;
