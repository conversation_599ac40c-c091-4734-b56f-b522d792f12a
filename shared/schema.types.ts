import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import {
  accessPoints,
  accessPointStatusEnum,
  auditTrail,
  auditTrailActionEnum,
  capaEffectivenessStatusEnum,
  capaPriorityEnum,
  capas,
  capaTagsEnum,
  capaTypeEnum,
  comments,
  eventCategoryEnum,
  events,
  files,
  fileStatusEnum,
  oshaAgencyReportTypeEnum,
  oshaAuditTrail,
  oshaAuditTrailActionEnum,
  oshaEntityTypeEnum,
  oshaReports,
  oshaTypeEnum,
  rcaMethodEnum,
  reportTypeEnum,
  roleEnum,
  rootCauseEnum,
  severityEnum,
  shiftsEnum,
  statusEnum,
  typeOfMedicalCareEnum,
} from './schema';

import { isBefore } from 'date-fns';
import { USER_ACCOUNT_TYPES, UserPermissionSchema } from './user-permissions';

export const featureFlags = ['webEHS', 'ehsOsha'] as const;

export const UserSchema = z.object({
  id: z.string(),
  username: z.string(),
  email: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  fullName: z.string(),
  upkeepCompanyId: z.string(),
  role: z.union([z.literal(USER_ACCOUNT_TYPES.ADMIN), z.literal(USER_ACCOUNT_TYPES.TECHNICIAN)]).optional(),
  permissions: UserPermissionSchema,
  featureFlags: z.record(z.enum(featureFlags), z.boolean()),
  createdAt: z.string().optional(),
  lastLoginAt: z.string().optional(),
  hasEhsEnabled: z.boolean(),
});

export const UserPublicSchema = UserSchema.pick({
  id: true,
  firstName: true,
  lastName: true,
  username: true,
}).extend({
  lastName: z.string().optional(),
  fullName: z.string().optional(),
  email: z.string().optional(),
});

export type FeatureFlag = (typeof featureFlags)[number];
export type FeatureFlags = Record<FeatureFlag, boolean>;

export type User = z.infer<typeof UserSchema>;
export type UserPublic = z.infer<typeof UserPublicSchema>;

export const ReportTypeSchema = z.enum(reportTypeEnum.enumValues);
export const SeveritySchema = z.enum(severityEnum.enumValues);
export const RootCauseSchema = z.enum(rootCauseEnum.enumValues);
export const StatusSchema = z.enum(statusEnum.enumValues);

export const STATUS_MAP: Record<(typeof statusEnum.enumValues)[number], string> = {
  [statusEnum.enumValues[0]]: 'Open',
  [statusEnum.enumValues[1]]: 'In Review',
  [statusEnum.enumValues[2]]: 'Closed',
};

export const SEVERITY_MAP: Record<(typeof severityEnum.enumValues)[number], string> = {
  [severityEnum.enumValues[0]]: 'Low',
  [severityEnum.enumValues[1]]: 'Medium',
  [severityEnum.enumValues[2]]: 'High',
  [severityEnum.enumValues[3]]: 'Critical',
};

export const REPORT_TYPE_MAP: Record<(typeof reportTypeEnum.enumValues)[number], string> = {
  [reportTypeEnum.enumValues[0]]: 'Incident',
  [reportTypeEnum.enumValues[1]]: 'Near Miss',
  [reportTypeEnum.enumValues[2]]: 'Observation',
};

export const CATEGORY_MAP: Record<(typeof eventCategoryEnum.enumValues)[number], string> = {
  [eventCategoryEnum.enumValues[0]]: 'Chemical',
  [eventCategoryEnum.enumValues[1]]: 'Electrical',
  [eventCategoryEnum.enumValues[2]]: 'Ergonomic',
  [eventCategoryEnum.enumValues[3]]: 'Fall',
  [eventCategoryEnum.enumValues[4]]: 'Fire',
  [eventCategoryEnum.enumValues[5]]: 'Mechanical',
  [eventCategoryEnum.enumValues[6]]: 'Radiation',
  [eventCategoryEnum.enumValues[7]]: 'Spill',
  [eventCategoryEnum.enumValues[8]]: 'Transportation',
  [eventCategoryEnum.enumValues[9]]: 'Violence',
  [eventCategoryEnum.enumValues[10]]: 'Other',
};

export const RCA_METHOD_MAP: Record<(typeof rcaMethodEnum.enumValues)[number], string> = {
  [rcaMethodEnum.enumValues[0]]: '5 Whys',
  [rcaMethodEnum.enumValues[1]]: 'Fishbone Diagram',
  [rcaMethodEnum.enumValues[2]]: 'Fault Tree Analysis',
  [rcaMethodEnum.enumValues[3]]: 'Other Method',
  [rcaMethodEnum.enumValues[4]]: 'Not Selected',
};

export const ROOT_CAUSE_MAP: Record<(typeof rootCauseEnum.enumValues)[number], string> = {
  [rootCauseEnum.enumValues[0]]: 'Human Error',
  [rootCauseEnum.enumValues[1]]: 'Equipment Failure',
  [rootCauseEnum.enumValues[2]]: 'Environmental',
  [rootCauseEnum.enumValues[3]]: 'Procedural',
  [rootCauseEnum.enumValues[4]]: 'Other',
};

export const CAPA_PRIORITY_MAP: Record<(typeof capaPriorityEnum.enumValues)[number], string> = {
  [capaPriorityEnum.enumValues[0]]: 'High',
  [capaPriorityEnum.enumValues[1]]: 'Medium',
  [capaPriorityEnum.enumValues[2]]: 'Low',
};

export const CAPA_TYPE_MAP: Record<(typeof capaTypeEnum.enumValues)[number], string> = {
  [capaTypeEnum.enumValues[0]]: 'Corrective',
  [capaTypeEnum.enumValues[1]]: 'Preventive',
  [capaTypeEnum.enumValues[2]]: 'Both',
};

export const CAPA_TAGS_MAP: Record<(typeof capaTagsEnum.enumValues)[number], string> = {
  [capaTagsEnum.enumValues[0]]: 'Training',
  [capaTagsEnum.enumValues[1]]: 'Policy',
  [capaTagsEnum.enumValues[2]]: 'Hazard',
  [capaTagsEnum.enumValues[3]]: 'Equipment',
  [capaTagsEnum.enumValues[4]]: 'Procedure',
  [capaTagsEnum.enumValues[5]]: 'Personnel',
};

export const CAPA_EFFECTIVENESS_STATUS_MAP: Record<(typeof capaEffectivenessStatusEnum.enumValues)[number], string> = {
  [capaEffectivenessStatusEnum.enumValues[0]]: 'Effective',
  [capaEffectivenessStatusEnum.enumValues[1]]: 'Partial',
  [capaEffectivenessStatusEnum.enumValues[2]]: 'Not Effective',
  [capaEffectivenessStatusEnum.enumValues[3]]: 'Not Evaluated',
};

export const ACCESS_POINT_STATUS_MAP: Record<(typeof accessPointStatusEnum.enumValues)[number], string> = {
  [accessPointStatusEnum.enumValues[0]]: 'Active',
  [accessPointStatusEnum.enumValues[1]]: 'Inactive',
};

export const ENTITY_TYPE_MAP: Record<'event' | 'capa', string> = {
  event: 'Safety Event',
  capa: 'CAPA',
};

// Interface for status configuration
export interface StatusConfig {
  label: string;
  color: string;
  backgroundColor: string;
}

// Status to style configuration mapping (colors)
export const STATUS_STYLES: Record<(typeof statusEnum.enumValues)[number], StatusConfig> = {
  [statusEnum.enumValues[0]]: {
    label: 'Open',
    backgroundColor: '#e0e7ff',
    color: '#2563eb',
  },
  [statusEnum.enumValues[1]]: {
    label: 'In Review',
    backgroundColor: '#fef9c3',
    color: '#b45309',
  },
  [statusEnum.enumValues[2]]: {
    label: 'Closed',
    backgroundColor: '#dcfce7',
    color: '#166534',
  },
};

// Severity to style configuration mapping (colors)
export const SEVERITY_STYLES: Record<(typeof severityEnum.enumValues)[number], StatusConfig> = {
  [severityEnum.enumValues[0]]: {
    label: 'Low',
    backgroundColor: '#dcfce7',
    color: '#15803d',
  },
  [severityEnum.enumValues[1]]: {
    label: 'Medium',
    backgroundColor: '#fef3c7',
    color: '#d97706',
  },
  [severityEnum.enumValues[2]]: {
    label: 'High',
    backgroundColor: '#fee2e2',
    color: '#b91c1c',
  },
  [severityEnum.enumValues[3]]: {
    label: 'Critical',
    backgroundColor: '#7f1d1d',
    color: '#fecaca',
  },
};

export const RoleSchema = z.enum(roleEnum.enumValues);
export const EventCategorySchema = z.enum(eventCategoryEnum.enumValues);
export const FileStatusSchema = z.enum(fileStatusEnum.enumValues);
export const AccessPointStatusSchema = z.enum(accessPointStatusEnum.enumValues);

// Specific schema for file status updates
export const FileUpdateStatusSchema = z.enum(['completed', 'failed'] as const);

export const EventValidations = {
  title: z
    .string({
      required_error: 'Title is required',
    })
    .min(1, {
      message: 'Title is required',
    }),
  reportedAt: z.coerce
    .date({ required_error: 'Date & Time is required' })
    .refine((date) => isBefore(date, new Date()), {
      message: 'Reported at must be in the past',
    }),
  type: z.enum(reportTypeEnum.enumValues, {
    message: 'Type is required',
  }),
  category: z.enum(eventCategoryEnum.enumValues, {
    required_error: 'Category is required',
  }),
  severity: z.enum(severityEnum.enumValues, {
    message: 'Severity is required',
  }),
  status: z.enum(statusEnum.enumValues, {
    message: 'Status is required',
  }),
};

export const CreateEventSchema = createInsertSchema(events, EventValidations);

export const IdSchema = z.object({
  id: z.string().cuid2(),
});

export const IdArraySchema = z.array(z.string().cuid2());

export const UpkeepCompanyIdSchema = z.object({
  upkeepCompanyId: z.string().min(1).max(10),
});

export const CapaValidations = {
  title: z.string({ required_error: 'Title is required' }).min(1, 'Title must be at least 1 character'),
  ownerId: z.string({ required_error: 'Owner is required' }).min(1),
  type: z.enum(capaTypeEnum.enumValues, {
    message: 'Type is required',
  }),
  rcaFindings: z
    .string({ required_error: 'RCA findings are required' })
    .min(1, 'RCA findings must be at least 1 character'),
  priority: z.enum(capaPriorityEnum.enumValues, {
    message: 'Priority is required',
  }),
  status: z.enum(statusEnum.enumValues, {
    message: 'Status is required',
  }),
  actionsToAddress: z
    .string({ required_error: 'Proposed actions are required' })
    .min(1, 'Proposed actions must be at least 1 character'),
};

export const CreateCapasSchema = createInsertSchema(capas, CapaValidations);
export const UpdateEventSchema = createUpdateSchema(events);

export const SelectEventSchema = createSelectSchema(events);

export type CreateEvent = InferInsertModel<typeof events>;
export type Event = InferSelectModel<typeof events>;

export const UpdateCapasSchema = createUpdateSchema(capas);

export const SelectCapasSchema = createSelectSchema(capas);

export type CreateCapa = z.infer<typeof CreateCapasSchema>;
export type Capa = InferSelectModel<typeof capas>;

export const CreateCommentsSchema = createInsertSchema(comments);

export const CreateCommentFormSchema = CreateCommentsSchema.omit({
  id: true,
  createdAt: true,
  capaId: true,
  eventId: true,
  updatedAt: true,
  upkeepCompanyId: true,
  userId: true,
}).and(
  z.object({
    entityId: z.string(),
    entityType: z.enum(['event', 'capa']),
    entitySlug: z.string(),
    entityTitle: z.string(),
    status: z.enum(statusEnum.enumValues),
  }),
);

export const ListCommentsSchema = z.object({
  entityId: z.string(),
  entityType: z.enum(['event', 'capa']),
  options: z
    .object({
      limit: z.number().optional(),
      offset: z.number().optional(),
    })
    .optional(),
});

export const UpdateCommentsSchema = createUpdateSchema(comments);
export const SelectCommentsSchema = createSelectSchema(comments);

export type CreateComments = InferInsertModel<typeof comments>;
export type Comments = InferSelectModel<typeof comments>;

export const CreateAuditTrailSchema = createInsertSchema(auditTrail);
export const UpdateAuditTrailSchema = createUpdateSchema(auditTrail);
export const SelectAuditTrailSchema = createSelectSchema(auditTrail);

export const GetAuditTrailSchema = z.object({
  entityId: z.string(),
  entityType: z.enum(['event', 'capa', 'access_point']),
});

export type CreateAuditTrail = InferInsertModel<typeof auditTrail>;
export type AuditTrail = InferSelectModel<typeof auditTrail>;

export const CreateFileSchema = createInsertSchema(files);
export const UpdateFileSchema = createUpdateSchema(files);
export const SelectFileSchema = createSelectSchema(files);

export const UpdateFilePublicSchema = UpdateFileSchema.and(UpkeepCompanyIdSchema);

export const TransientFileSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  url: z.string(),
  type: z.string(),
  size: z.number(),
  file: z.instanceof(File).optional(),
});

// Asset Schema and Type
export const AssetSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
});

export type Asset = z.infer<typeof AssetSchema>;

// Cursor-based Pagination Schema for useInfiniteQuery
export const PaginationInputSchema = z.object({
  cursor: z.number().min(0).default(0).optional(),
  limit: z.number().min(1).max(100).default(50).optional(),
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
});

// Generic Sorting Schemas and Types
export const SortInputSchema = z.object({
  sortBy: z.string().optional(),
  sortOrder: z.string().optional(),
});

export type SortInput = z.infer<typeof SortInputSchema>;

// Cursor-based search schemas
export const AssetSearchInputSchema = PaginationInputSchema.extend({
  locationId: z.string().optional(),
});

// Location Schema and Type
export const LocationSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export type Location = z.infer<typeof LocationSchema>;

export const LocationSearchInputSchema = PaginationInputSchema.extend({
  restrictionsLevel: z.number().optional(),
});

export type LocationSearchInput = z.infer<typeof LocationSearchInputSchema>;

export type PaginationInput = z.infer<typeof PaginationInputSchema>;

export type AssetSearchInput = z.infer<typeof AssetSearchInputSchema>;

// Cursor-based pagination response type for useInfiniteQuery
export interface PaginatedResponse<T> {
  noResults: boolean;
  result: T[];
  nextCursor: number | undefined;
}

export const EventsFiltersSchema = z.object({
  status: z.array(z.enum(statusEnum.enumValues)).optional(),
  type: z.array(z.enum(reportTypeEnum.enumValues)).optional(),
  severity: z.array(z.enum(severityEnum.enumValues)).optional(),
  oshaReportable: z.boolean().optional(),
  includeArchived: z.boolean().optional(),
  locationIds: z.array(z.string()).optional(),
});

export type EventsFilters = z.infer<typeof EventsFiltersSchema>;

// Cursor-based version of ListEventSchema for useInfiniteQuery
export const ListEventSchema = PaginationInputSchema.and(SortInputSchema).and(EventsFiltersSchema);

export const CapasFiltersSchema = z.object({
  status: z.array(z.enum(statusEnum.enumValues)).optional(),
  type: z.array(z.enum(capaTypeEnum.enumValues)).optional(),
  priority: z.array(z.enum(capaPriorityEnum.enumValues)).optional(),
  owner: z.array(z.string()).optional(),
  dueDateRange: z
    .object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional(),
    })
    .optional(),
  includeArchived: z.boolean().optional(),
  tags: z.array(z.enum(capaTagsEnum.enumValues)).optional(),
  eventId: z.string().cuid2().optional(),
});

export type CapasFilters = z.infer<typeof CapasFiltersSchema>;

// Cursor-based version of ListCapasSchema for useInfiniteQuery
export const ListCapasSchema = PaginationInputSchema.and(SortInputSchema).and(CapasFiltersSchema);

export const GetPresignedUrlInputSchema = CreateFileSchema.pick({
  fileName: true,
  fileSize: true,
  mimeType: true,
  entityType: true,
  entityId: true,
}).extend({
  entityType: z.string().min(1),
  fileSize: z.number().positive('File size must be positive'),
});

export const GetPresignedUrlInputPublicSchema = GetPresignedUrlInputSchema.and(UpkeepCompanyIdSchema);

export const ListFilesSchema = z.object({
  entityType: z.string().min(1),
  entityId: z.string().min(1),
  status: FileStatusSchema.optional(),
});

export const PublicSearchSchema = z
  .object({
    upkeepCompanyId: z.string().min(1, 'Required'),
    locationId: z.string().optional(),
    userAccountType: z.string().optional(),
    objectId: z.union([z.string(), z.array(z.string())]).optional(),
    mustIncludeObjectIds: z.array(z.string()).optional(),
  })
  .and(PaginationInputSchema);

export type PublicSearchInput = z.infer<typeof PublicSearchSchema>;

export const CreateWorkOrderFromCapaSchema = CreateCapasSchema.pick({
  title: true,
  actionsToAddress: true,
  priority: true,
  dueDate: true,
  locationId: true,
  assetId: true,
})
  .extend({
    id: z.string().min(1, 'CAPA ID is required'),
    slug: z.string().min(1, 'Slug is required'),
    userAssignedTo: z.string().optional(),
  })
  .extend({ dueDate: z.string().min(1, 'Due date is required') }); // avoid conflicts with the dueDate type from the schema

export type CreateWorkOrderFromCapaInput = z.infer<typeof CreateWorkOrderFromCapaSchema>;

// Work Order Search Schema extending PaginationInputSchema for consistency with other entities
export const WorkOrderSearchInputSchema = PaginationInputSchema.extend({
  capaId: z.array(z.string()),
  sort: z.string().optional().default('createdAt DESC'),
});

export const CountWorkOrdersByCapaIdSchema = z.object({
  capaId: z.array(z.string()),
});

export type WorkOrderSearchInput = z.infer<typeof WorkOrderSearchInputSchema>;
export type CountWorkOrdersByCapaIdInput = z.infer<typeof CountWorkOrdersByCapaIdSchema>;

export type RawParseObject = {
  __type?: string;
  className?: string;
  objectId: string;
};

export type UpkeepAsset = {
  id: string;
  Name: string;
  Description: string;
  objectLocation?: { stringName: string };
};

// Work Order Types for UpKeep API responses
export type WorkOrder = {
  id: string;
  workOrderNumber: string;
  title: string;
  currentStatus: string;
  priority: 'low' | 'medium' | 'high';
  dueDate: string;
  assignedTo: string;
  locationId: string;
  assetId: string;
  assetName: string;
};

export const CreateAccessPointSchema = createInsertSchema(accessPoints);
export const UpdateAccessPointSchema = createUpdateSchema(accessPoints);
export const SelectAccessPointSchema = createSelectSchema(accessPoints);

export const CreateAccessPointFormSchema = CreateAccessPointSchema.pick({
  name: true,
  locationId: true,
}).extend({
  name: z.string().min(1, 'Name is required'),
});

export type CreateAccessPoint = InferInsertModel<typeof accessPoints>;
export type AccessPoint = InferSelectModel<typeof accessPoints>;

export const AccessPointsFiltersSchema = z.object({
  includeArchived: z.boolean().optional(),
  locationId: z.array(z.string()).optional(),
  status: z.array(z.enum(accessPointStatusEnum.enumValues)).optional(),
  createdBy: z.array(z.string()).optional(),
  createdDateRange: z
    .object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional(),
    })
    .optional(),
});

export type AccessPointsFilters = z.infer<typeof AccessPointsFiltersSchema>;

export const ListAccessPointsSchema = PaginationInputSchema.and(SortInputSchema).and(AccessPointsFiltersSchema);

export type CreateWorkOrderParams = {
  mainDescription: string; // Title
  note: string; // Description
  priorityNumber: number;
  dueDate: string;
  objectLocationForWorkOrder?: string;
  objectAsset?: string;
  userAssignedTo?: string;
  capaId: string;
};

export const EventNotificationSchema = SelectEventSchema.omit({
  type: true,
  category: true,
  archived: true,
  immediateActions: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  deletedAt: true,
  upkeepCompanyId: true,
  updatedAt: true,
}).extend({
  location: LocationSchema.optional(),
  action: z.enum(auditTrailActionEnum.enumValues).optional(),
  teamMembersToNotify: z.array(UserSchema.pick({ id: true, fullName: true, email: true })).optional(),
});

export const IncidentNotificationPublicSchema = SelectEventSchema.omit({
  type: true,
  category: true,
  archived: true,
  immediateActions: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  deletedAt: true,
  upkeepCompanyId: true,
  updatedAt: true,
  teamMembersToNotify: true,
}).extend({
  location: LocationSchema.optional(),
  action: z.enum(auditTrailActionEnum.enumValues).optional(),
});

export const CreateEventFormSchema = CreateEventSchema.omit({
  id: true,
  oshaReportable: true,
  aiConfidenceScore: true,
  archived: true,
  deletedAt: true,
  reportedBy: true,
  slug: true,
  upkeepCompanyId: true,
  updatedAt: true,
  status: true,
}).extend({
  media: z.array(TransientFileSchema).optional(),
});

export const EditEventFormSchema = UpdateEventSchema.omit({
  aiConfidenceScore: true,
  deletedAt: true,
  reportedBy: true,
  slug: true,
  upkeepCompanyId: true,
  updatedAt: true,
}).extend({
  media: z.array(TransientFileSchema).optional(),
  id: z.string().cuid2(),
});

export const CreateEventFormPublicSchema = CreateEventFormSchema.extend({
  name: z.string({ required_error: 'Name is required' }).min(1, { message: 'Name is required' }),
  email: z.string({ required_error: 'Invalid email address' }).email({ message: 'Invalid email address' }),
  reportedBy: z.string().optional(),
}).and(UpkeepCompanyIdSchema);

export const CreateCapasFormSchema = CreateCapasSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  id: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
  aiConfidenceScore: true,
  archived: true,
}).extend({
  attachments: z.array(TransientFileSchema).optional(),
});

export type CreateCapasForm = z.infer<typeof CreateCapasFormSchema>;

export const EditCapasFormSchema = UpdateCapasSchema.omit({
  upkeepCompanyId: true,
  createdBy: true,
  slug: true,
  createdAt: true,
  updatedAt: true,
  deletedAt: true,
  aiSuggestedAction: true,
}).extend({
  attachments: z.array(TransientFileSchema).optional(),
  id: z.string().cuid2(),
});

export type EditCapasForm = z.infer<typeof EditCapasFormSchema>;

// Reusable refinement logic
const OshaReportConditionalValidation = (
  data: {
    privacyCase?: boolean;
    employeeName?: string | null;
    reasonForPrivacyCase?: string | null;
    wasHospitalized?: boolean;
    wasDeceased?: boolean;
    daysAwayFromWork?: number;
    daysRestrictedFromWork?: number;
  },
  ctx: z.RefinementCtx,
) => {
  if (data.privacyCase === true) {
    data.employeeName = null;
    if (!data.reasonForPrivacyCase?.trim()) {
      ctx.addIssue({
        code: 'custom',
        path: ['reasonForPrivacyCase'],
        message: 'Reason for privacy case is required.',
      });
    }
  } else if (data.privacyCase === false) {
    if (!data.employeeName?.trim()) {
      ctx.addIssue({
        code: 'custom',
        path: ['employeeName'],
        message: 'Employee name is required.',
      });
    }
  }

  // New validation for Hospitalization/Deceased condition
  if (data.wasHospitalized || data.wasDeceased) {
    if (!data.daysAwayFromWork || data.daysAwayFromWork <= 0) {
      ctx.addIssue({
        code: 'custom',
        path: ['daysAwayFromWork'],
        message: 'Days away from work required if hospitalized or deceased.',
      });
    }
    if (!data.daysRestrictedFromWork || data.daysRestrictedFromWork <= 0) {
      ctx.addIssue({
        code: 'custom',
        path: ['daysRestrictedFromWork'],
        message: 'Days restricted from work required if hospitalized or deceased.',
      });
    }
  }
};

const RequestInfoSchema = z.object({
  ipAddress: z.string().ip('Invalid IP address').optional(),
  userAgent: z.string().min(1, 'User agent is required').optional(),
});

// Clean schemas
export const UpdateOshaReportFormSchema = createUpdateSchema(oshaReports)
  .omit({
    upkeepCompanyId: true,
    createdBy: true,
    slug: true,
    createdAt: true,
    updatedAt: true,
  })
  .and(RequestInfoSchema)
  .superRefine(OshaReportConditionalValidation);

export const CreateOshaReportFormSchema = createInsertSchema(oshaReports)
  .omit({
    id: true,
    upkeepCompanyId: true,
    createdBy: true,
    slug: true,
    createdAt: true,
    updatedAt: true,
  })
  .and(RequestInfoSchema)
  .superRefine(OshaReportConditionalValidation);

export type CreateOshaReportForm = z.infer<typeof CreateOshaReportFormSchema>;
export type UpdateOshaReportForm = z.infer<typeof UpdateOshaReportFormSchema>;

export const OshaReportAuditTrailSchema = createSelectSchema(oshaAuditTrail);
export type OshaReportAuditTrail = z.infer<typeof OshaReportAuditTrailSchema>;

export const OshaReportsFiltersSchema = z.object({
  caseType: z.enum(oshaTypeEnum.enumValues).optional(),
  locationIds: z.array(z.string()).optional(),
  includeArchived: z.boolean().optional(),
  year: z.number().optional(),
});

export type OshaReportsFilters = z.infer<typeof OshaReportsFiltersSchema>;

// OSHA Report List Schema for cursor-based pagination
export const ListOshaReportsSchema = PaginationInputSchema.and(SortInputSchema).and(OshaReportsFiltersSchema);

export const SHIFTS_MAP: Record<(typeof shiftsEnum.enumValues)[number], string> = {
  [shiftsEnum.enumValues[0]]: 'Day',
  [shiftsEnum.enumValues[1]]: 'Evening',
  [shiftsEnum.enumValues[2]]: 'Night',
  [shiftsEnum.enumValues[3]]: 'Rotating',
  [shiftsEnum.enumValues[4]]: 'Other',
};

export const TYPE_OF_MEDICAL_CARE_MAP: Record<(typeof typeOfMedicalCareEnum.enumValues)[number], string> = {
  [typeOfMedicalCareEnum.enumValues[0]]: 'First Aid',
  [typeOfMedicalCareEnum.enumValues[1]]: 'Medical Treatment',
  [typeOfMedicalCareEnum.enumValues[2]]: 'Emergency Room',
  [typeOfMedicalCareEnum.enumValues[3]]: 'Overnight Hospital Stay',
};

export const OSHA_TYPE_MAP: Record<(typeof oshaTypeEnum.enumValues)[number], string> = {
  [oshaTypeEnum.enumValues[0]]: 'Fatality',
  [oshaTypeEnum.enumValues[1]]: 'Days Away From Work',
  [oshaTypeEnum.enumValues[2]]: 'Job Restriction',
  [oshaTypeEnum.enumValues[3]]: 'Medical Treatment Beyond First Aid',
  [oshaTypeEnum.enumValues[4]]: 'Loss of Consciousness',
  [oshaTypeEnum.enumValues[5]]: 'Significant Injury',
};

export const OSHA_AGENCY_REPORT_TYPE_MAP: Record<(typeof oshaAgencyReportTypeEnum.enumValues)[number], string> = {
  [oshaAgencyReportTypeEnum.enumValues[0]]: 'Fatality',
  [oshaAgencyReportTypeEnum.enumValues[1]]: 'Amputation',
  [oshaAgencyReportTypeEnum.enumValues[2]]: 'Hospitalization',
  [oshaAgencyReportTypeEnum.enumValues[3]]: 'Eye Loss',
};

export const OSHA_AUDIT_TRAIL_ACTION_MAP: Record<(typeof oshaAuditTrailActionEnum.enumValues)[number], string> = {
  [oshaAuditTrailActionEnum.enumValues[0]]: 'Created',
  [oshaAuditTrailActionEnum.enumValues[1]]: 'Updated',
  [oshaAuditTrailActionEnum.enumValues[2]]: 'Submitted',
  [oshaAuditTrailActionEnum.enumValues[3]]: 'Downloaded',
  [oshaAuditTrailActionEnum.enumValues[4]]: 'Signed',
  [oshaAuditTrailActionEnum.enumValues[5]]: 'Archived',
  [oshaAuditTrailActionEnum.enumValues[6]]: 'Restored',
};

export const OSHA_ENTITY_TYPE_MAP: Record<(typeof oshaEntityTypeEnum.enumValues)[number], string> = {
  [oshaEntityTypeEnum.enumValues[0]]: 'OSHA Report',
  [oshaEntityTypeEnum.enumValues[1]]: 'OSHA Company Information',
  [oshaEntityTypeEnum.enumValues[2]]: 'OSHA Agency Report',
};

export const BulkCreateAccessPointInputSchema = z.object({
  name: z.string().min(1),
  location: z.string().min(1),
});

export type BulkCreateAccessPointInput = z.infer<typeof BulkCreateAccessPointInputSchema>;

export interface BulkImportResult {
  total: number;
  created: number;
  failed: number;
  failedItems: Array<{
    name: string;
    location: string;
    reason: string;
  }>;
}

export interface BulkImportSummary {
  successCount: number;
  failureCount: number;
  totalProcessed: number;
  failures: Array<{
    name: string;
    location: string;
    reason: string;
  }>;
}

// Helper function to convert API response to client format
export function convertBulkImportResult(apiResult: BulkImportResult): BulkImportResult {
  return {
    total: apiResult.total || 0,
    created: apiResult.created || 0,
    failed: apiResult.failed || 0,
    failedItems: apiResult.failedItems || [],
  };
}
