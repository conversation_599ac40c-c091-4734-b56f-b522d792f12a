import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __dirname = dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  test: {
    environment: 'node',
    include: ['server/**/__tests__/**/*.test.ts', 'server/test/**/*.test.ts'],
    exclude: ['node_modules/**', 'dist/**', 'server/upkeep/__tests__/**'],
    globals: true,
    setupFiles: ['./vitest.setup.ts'],
    maxConcurrency: 1,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: ['server/**/*.ts'],
      exclude: ['server/**/__tests__/**/*.ts', 'server/**/*.spec.ts'],
      // Thresholds disabled - CI won't fail due to coverage requirements
      // thresholds: {
      //   statements: 35,
      //   branches: 50,
      //   functions: 60,
      //   lines: 35,
      // }
    },
  },
  resolve: {
    alias: {
      '@server': resolve(__dirname, './server'),
      '@shared': resolve(__dirname, './shared'),
    },
  },
});
